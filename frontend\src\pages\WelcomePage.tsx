
import { useState, useCallback } from 'react';
import { TrendingUp, Plus, FolderO<PERSON>, Clock, BarChart3, Upload, FileSpreadsheet, X, ArrowRight, Database, Target, CheckCircle2, Users, Zap, Shield, Activity, Layers, Sparkles, Brain, Gauge } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AnimatedBackground } from '@/components/ui/animated-background';
import { GradientText } from '@/components/ui/gradient-text';
import { ShimmerButton } from '@/components/ui/shimmer-button';
import { SimulationDemo } from '@/components/demo/SimulationDemo';
import { Typewriter } from '@/components/ui/typewriter';
import { Separator } from '@/components/ui/separator';
import { useNavigate } from 'react-router-dom';
import { Navbar } from '@/components/layout/Navbar';
import { Footer } from '@/components/layout/Footer';
import { useCreateTask, useTasks } from '@/hooks/use-api';
import { toast } from 'sonner';

export function WelcomePage() {
  const navigate = useNavigate();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [query, setQuery] = useState('');
  const [dragActive, setDragActive] = useState(false);

  // API hooks
  const createTaskMutation = useCreateTask();
  const { data: tasksData } = useTasks(5, 0); // 获取最近5个任务

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      if (isValidFile(file)) {
        setSelectedFile(file);
      }
    }
  }, []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      if (isValidFile(file)) {
        setSelectedFile(file);
      }
    }
  };

  const isValidFile = (file: File) => {
    const validTypes = ['text/csv', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
    return validTypes.includes(file.type) || file.name.endsWith('.csv') || file.name.endsWith('.xlsx') || file.name.endsWith('.xls');
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleStartAnalysis = async () => {
    if (!selectedFile || !query.trim()) return;

    try {
      const task = await createTaskMutation.mutateAsync({
        prompt: query.trim(),
        file: selectedFile
      });

      // 导航到分析页面，传递任务ID
      navigate(`/analysis/${task.task_id}`);
    } catch (error) {
      console.error('Failed to start analysis:', error);
      toast.error('启动分析失败', {
        description: error instanceof Error ? error.message : '未知错误'
      });
    }
  };

  const handleCreateNew = () => {
    // 滚动到上传区域
    document.getElementById('upload')?.scrollIntoView({ behavior: 'smooth' });
  };

  const handleOpenTask = (taskId: string) => {
    navigate(`/analysis/${taskId}`);
  };

  const exampleQueries = [
    "分析销售数据中的关键业绩驱动因素，识别增长机会并预测未来三个季度的收入趋势",
    "基于用户行为数据构建精准客户画像，制定个性化营销策略以提升转化率和客户终身价值",
    "检测财务数据中的异常模式和潜在风险，提供成本优化建议和盈利能力提升方案",
    "构建智能需求预测模型，结合市场趋势和季节因素优化库存管理和供应链决策"
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-success text-success-foreground';
      case 'running': return 'bg-primary text-primary-foreground';
      case 'failed': return 'bg-destructive text-destructive-foreground';
      case 'cancelled': return 'bg-muted text-muted-foreground';
      default: return 'bg-muted text-muted-foreground';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成';
      case 'running': return '进行中';
      case 'failed': return '失败';
      case 'cancelled': return '已取消';
      default: return '等待中';
    }
  };

  // 获取最近任务数据
  const recentTasks = tasksData?.tasks || [];

  return (
    <div className="min-h-screen bg-background">
      <Navbar />
      
      {/* Hero Section with Integrated Demo */}
      <section id="hero" className="relative pt-20 pb-4 overflow-hidden">
        {/* Enhanced Background */}
        <div className="absolute inset-0 w-full h-full">
          <AnimatedBackground />
        </div>
        
        {/* Subtle Grid Background */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.01)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.01)_1px,transparent_1px)] bg-[size:80px_80px] [mask-image:radial-gradient(ellipse_at_center,black,transparent_80%)]" />
        
        {/* Dynamic Gradient Orbs */}
        <div className="absolute top-1/5 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-1/3 right-1/4 w-80 h-80 bg-accent/8 rounded-full blur-3xl animate-pulse delay-1000" />
        <div className="absolute bottom-1/4 left-1/3 w-72 h-72 bg-success/6 rounded-full blur-3xl animate-pulse delay-500" />
        
        <div className="relative container mx-auto px-6">
          <div className="max-w-7xl mx-auto">
            {/* Header Content */}
            <div className="text-center mb-8">
              {/* Badge */}
              <div className="inline-flex items-center gap-3 px-6 py-3 bg-primary/5 border border-primary/10 rounded-full backdrop-blur-sm mb-6">
                <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                <span className="text-sm font-medium text-primary">下一代AI数据分析平台</span>
              </div>
              
              {/* Main Title with Hierarchy */}
              <div className="space-y-6">
                <h1 className="text-5xl md:text-7xl font-black leading-tight tracking-tight">
                  <GradientText>智析云</GradientText>
                </h1>
                
                <div className="text-3xl md:text-4xl font-bold text-foreground/90 min-h-[3rem] flex items-center justify-center">
                  <Typewriter 
                    text="重新定义数据分析" 
                    delay={150}
                    startDelay={1000}
                    className="bg-gradient-to-r from-foreground via-primary to-accent bg-clip-text text-transparent"
                  />
                </div>
                
                <p className="max-w-3xl mx-auto text-xl text-muted-foreground leading-relaxed">
                  企业级AI驱动的智能数据分析平台，通过透明的推理过程、
                  交互式结果展示和人机协同分析，让每一个数据都成为业务增长的催化剂
                </p>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-6 pt-8">
                <ShimmerButton
                  size="lg"
                  className="px-10 py-4 text-lg font-bold shadow-2xl shadow-primary/20"
                  onClick={() => document.getElementById('upload')?.scrollIntoView({ behavior: 'smooth' })}
                >
                  <Gauge className="w-6 h-6 mr-3" />
                  立即开始分析
                  <ArrowRight className="w-6 h-6 ml-3" />
                </ShimmerButton>
                
                <Button
                  variant="outline"
                  size="lg"
                  className="px-8 py-4 text-lg border-2 border-border/60 bg-background/60 backdrop-blur-sm hover:bg-background/80 hover:border-primary/30 transition-all duration-300"
                >
                  <FolderOpen className="w-5 h-5 mr-3" />
                  查看演示案例
                </Button>
              </div>
            </div>

            {/* Demo Component - moved up with reduced spacing */}
            <div className="mt-8">
              <SimulationDemo />
            </div>
          </div>
        </div>
      </section>

      {/* Elegant Transition */}
      <div className="relative py-4">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-muted/5 to-background" />
        <div className="relative flex items-center justify-center">
          <div className="w-1 h-1 bg-primary/40 rounded-full" />
          <div className="w-2 h-2 bg-primary/60 rounded-full mx-4" />
          <div className="w-1 h-1 bg-primary/40 rounded-full" />
        </div>
      </div>

      {/* Features Section - Compact */}
      <section id="features" className="py-8 bg-gradient-to-b from-background to-muted/5">
        <div className="container mx-auto px-6">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold mb-4">平台核心能力</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              企业级AI驱动的数据分析解决方案，助力业务决策优化
            </p>
          </div>
          
          <div className="grid lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <Card className="text-center hover:shadow-2xl hover:shadow-primary/5 transition-all duration-500 border-0 bg-card/60 backdrop-blur-sm hover:bg-card/80 group">
              <CardHeader className="pb-6">
                <div className="mx-auto w-16 h-16 bg-primary/10 rounded-2xl flex items-center justify-center mb-6 group-hover:bg-primary/20 transition-colors duration-300">
                  <Activity className="w-8 h-8 text-primary" />
                </div>
                <CardTitle className="text-xl mb-4">透明AI推理</CardTitle>
                <CardDescription className="text-base leading-relaxed">
                  实时可视化AI分析过程，每一个决策步骤都清晰可见，
                  确保分析结果的可解释性和可信度
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card className="text-center hover:shadow-2xl hover:shadow-accent/5 transition-all duration-500 border-0 bg-card/60 backdrop-blur-sm hover:bg-card/80 group">
              <CardHeader className="pb-6">
                <div className="mx-auto w-16 h-16 bg-accent/10 rounded-2xl flex items-center justify-center mb-6 group-hover:bg-accent/20 transition-colors duration-300">
                  <BarChart3 className="w-8 h-8 text-accent" />
                </div>
                <CardTitle className="text-xl mb-4">智能交互分析</CardTitle>
                <CardDescription className="text-base leading-relaxed">
                  动态图表、交互式代码查看和深度洞察展示，
                  支持多维度数据探索和自定义分析路径
                </CardDescription>
              </CardHeader>
            </Card>
            
            <Card className="text-center hover:shadow-2xl hover:shadow-success/5 transition-all duration-500 border-0 bg-card/60 backdrop-blur-sm hover:bg-card/80 group">
              <CardHeader className="pb-6">
                <div className="mx-auto w-16 h-16 bg-success/10 rounded-2xl flex items-center justify-center mb-6 group-hover:bg-success/20 transition-colors duration-300">
                  <Users className="w-8 h-8 text-success" />
                </div>
                <CardTitle className="text-xl mb-4">人机协同决策</CardTitle>
                <CardDescription className="text-base leading-relaxed">
                  分析过程中随时介入指导，支持专家知识与AI能力深度融合，
                  确保分析结果符合业务需求
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Elegant Transition */}
      <div className="relative py-4">
        <div className="absolute inset-0 bg-gradient-to-b from-muted/5 via-background to-muted/5" />
        <div className="relative flex items-center justify-center">
          <div className="w-1 h-1 bg-accent/40 rounded-full" />
          <div className="w-2 h-2 bg-accent/60 rounded-full mx-4" />
          <div className="w-1 h-1 bg-accent/40 rounded-full" />
        </div>
      </div>

      {/* Upload Section - Compact */}
      <section id="upload" className="py-8 bg-gradient-to-b from-muted/5 to-background">
        <div className="container mx-auto px-6">
          <div className="max-w-6xl mx-auto space-y-8">
            <div className="text-center space-y-4">
              <h2 className="text-3xl font-bold">开始您的智能数据分析</h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                简单三步骤，让专业AI为您的数据提供深度洞察和业务价值
              </p>
            </div>

            <div className="grid lg:grid-cols-2 gap-10">
              {/* File Upload */}
              <Card className="border-2 hover:border-primary/30 transition-all duration-300 hover:shadow-xl hover:shadow-primary/5">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center">
                      <Database className="w-5 h-5 text-primary" />
                    </div>
                    上传数据集
                  </CardTitle>
                  <CardDescription className="text-base">
                    支持 CSV、Excel (.xlsx, .xls) 格式，最大 100MB
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  {!selectedFile ? (
                    <div
                      className={`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300 ${
                        dragActive 
                          ? 'border-primary bg-primary/5 scale-105' 
                          : 'border-muted-foreground/25 hover:border-primary/50 hover:bg-primary/2'
                      }`}
                      onDragEnter={handleDrag}
                      onDragLeave={handleDrag}
                      onDragOver={handleDrag}
                      onDrop={handleDrop}
                    >
                      <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                      <div className="space-y-2">
                        <p className="text-lg font-medium">拖拽文件到此处</p>
                        <p className="text-base text-muted-foreground">或点击选择文件</p>
                      </div>
                      <input
                        type="file"
                        accept=".csv,.xlsx,.xls"
                        onChange={handleFileChange}
                        className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      />
                    </div>
                  ) : (
                    <div className="flex items-center justify-between p-4 border rounded-xl bg-muted/20">
                      <div className="flex items-center gap-4">
                        <FileSpreadsheet className="w-8 h-8 text-primary" />
                        <div>
                          <p className="font-medium">{selectedFile.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {formatFileSize(selectedFile.size)}
                          </p>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedFile(null)}
                      >
                        <X className="w-5 h-5" />
                      </Button>
                    </div>
                  )}

                  {selectedFile && (
                    <Alert>
                      <AlertDescription>
                        文件上传成功！现在请描述您的分析目标。
                      </AlertDescription>
                    </Alert>
                  )}
                </CardContent>
              </Card>

              {/* Analysis Query */}
              <Card className="border-2 hover:border-accent/30 transition-all duration-300 hover:shadow-xl hover:shadow-accent/5">
                <CardHeader className="pb-4">
                  <CardTitle className="flex items-center gap-3 text-xl">
                    <div className="w-8 h-8 bg-accent/10 rounded-lg flex items-center justify-center">
                      <Target className="w-5 h-5 text-accent" />
                    </div>
                    分析目标设定
                  </CardTitle>
                  <CardDescription className="text-base">
                    使用自然语言描述您的业务问题和分析需求
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label htmlFor="query" className="text-base font-medium">描述您的分析需求</Label>
                    <Textarea
                      id="query"
                      placeholder="例如：分析我的销售数据，识别关键增长驱动因素，预测下季度业绩表现..."
                      value={query}
                      onChange={(e) => setQuery(e.target.value)}
                      className="min-h-[140px] resize-none text-base"
                    />
                  </div>

                  <div className="space-y-4">
                    <Label className="text-base font-medium">专业分析模板</Label>
                    <div className="space-y-3">
                      {exampleQueries.slice(0, 2).map((example, index) => (
                        <button
                          key={index}
                          onClick={() => setQuery(example)}
                          className="w-full text-left p-4 text-sm border rounded-xl hover:bg-muted/30 hover:border-accent/30 transition-all duration-300 leading-relaxed"
                        >
                          {example}
                        </button>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-center gap-6 pt-6">
              <Button
                className="btn-gradient-primary px-12 py-4 text-lg font-bold shadow-xl shadow-primary/20"
                onClick={handleStartAnalysis}
                disabled={!selectedFile || !query.trim() || createTaskMutation.isPending}
                size="lg"
              >
                {createTaskMutation.isPending ? (
                  <>
                    <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-3" />
                    正在启动...
                  </>
                ) : (
                  <>
                    <TrendingUp className="w-5 h-5 mr-3" />
                    启动智能分析
                    <ArrowRight className="w-5 h-5 ml-3" />
                  </>
                )}
              </Button>
            </div>

            {/* Status */}
            <div className="text-center">
              <div className="flex items-center justify-center gap-8 text-base text-muted-foreground">
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full transition-colors ${selectedFile ? 'bg-success' : 'bg-muted'}`} />
                  <span className={selectedFile ? 'text-success font-medium' : ''}>
                    {selectedFile ? '数据集已准备' : '上传数据集'}
                  </span>
                </div>
                <div className="flex items-center gap-3">
                  <div className={`w-4 h-4 rounded-full transition-colors ${query.trim() ? 'bg-success' : 'bg-muted'}`} />
                  <span className={query.trim() ? 'text-success font-medium' : ''}>
                    {query.trim() ? '分析目标已设定' : '描述分析目标'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
