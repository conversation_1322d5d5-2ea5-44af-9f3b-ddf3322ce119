#!/usr/bin/env python3
"""反思审查系统完整演示"""

import asyncio
import json
import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.agents.reflection_agent import ReflectionAgent
from src.agents.report_generation_agent import ReportGenerationAgent
from src.models.state_models import AnalysisState
from src.utils.logger import get_logger

logger = get_logger(__name__)


def create_demo_state() -> AnalysisState:
    """创建演示用的分析状态"""
    return AnalysisState(
        task_id=f"demo_reflection_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        original_query="分析电商平台的用户购买行为数据，识别影响用户转化率的关键因素，并提供业务优化建议",
        dataframe_path="demo_ecommerce_data.csv",
        plan={
            "steps": [
                {
                    "step": 1,
                    "objective": "数据预处理和质量检查",
                    "description": "清洗数据，处理缺失值，检查数据质量",
                    "expected_output": "清洁的数据集和数据质量报告"
                },
                {
                    "step": 2,
                    "objective": "用户行为探索性分析",
                    "description": "分析用户浏览、点击、购买等行为模式",
                    "expected_output": "用户行为分布图表和统计摘要"
                },
                {
                    "step": 3,
                    "objective": "转化率影响因素分析",
                    "description": "识别影响用户转化的关键变量",
                    "expected_output": "相关性分析和特征重要性排序"
                },
                {
                    "step": 4,
                    "objective": "预测模型构建",
                    "description": "建立用户转化率预测模型",
                    "expected_output": "训练好的机器学习模型和性能评估"
                }
            ],
            "estimated_time": 45,
            "complexity": "中等"
        },
        current_step=4,
        executed_steps=[
            {
                "step": 1,
                "objective": "数据预处理和质量检查",
                "code": """
import pandas as pd
import numpy as np

# 加载数据
df = pd.read_csv('ecommerce_data.csv')
print(f"数据形状: {df.shape}")
print(f"缺失值统计:\\n{df.isnull().sum()}")

# 数据清洗
df_clean = df.dropna()
print(f"清洗后数据形状: {df_clean.shape}")
                """,
                "result": {
                    "success": True,
                    "output": "数据形状: (10000, 12)\\n缺失值统计:\\nuser_id: 0\\nage: 150\\ngender: 0\\n...",
                    "execution_time": 2.3
                },
                "insights": [
                    "数据集包含10,000条用户记录，12个特征变量",
                    "年龄字段存在1.5%的缺失值，已进行处理",
                    "数据质量整体良好，适合进行后续分析"
                ]
            },
            {
                "step": 2,
                "objective": "用户行为探索性分析",
                "code": """
import matplotlib.pyplot as plt
import seaborn as sns

# 用户年龄分布
plt.figure(figsize=(10, 6))
plt.subplot(2, 2, 1)
sns.histplot(df_clean['age'], bins=30)
plt.title('用户年龄分布')

# 转化率按性别分析
plt.subplot(2, 2, 2)
conversion_by_gender = df_clean.groupby('gender')['converted'].mean()
sns.barplot(x=conversion_by_gender.index, y=conversion_by_gender.values)
plt.title('不同性别转化率')

# 页面浏览时长分布
plt.subplot(2, 2, 3)
sns.histplot(df_clean['session_duration'], bins=50)
plt.title('页面浏览时长分布')

# 购买金额分布
plt.subplot(2, 2, 4)
sns.histplot(df_clean[df_clean['converted']==1]['purchase_amount'], bins=30)
plt.title('购买金额分布')

plt.tight_layout()
plt.savefig('user_behavior_analysis.png', dpi=300, bbox_inches='tight')
plt.show()
                """,
                "result": {
                    "success": True,
                    "output": "生成了用户行为分析图表",
                    "plots": ["user_behavior_analysis.png"],
                    "execution_time": 3.7
                },
                "insights": [
                    "用户年龄主要集中在25-45岁区间，呈正态分布",
                    "女性用户转化率(12.3%)略高于男性用户(11.8%)",
                    "页面浏览时长与转化率呈正相关关系",
                    "转化用户的平均购买金额为156.7元"
                ]
            },
            {
                "step": 3,
                "objective": "转化率影响因素分析",
                "code": """
from scipy.stats import chi2_contingency
import pandas as pd

# 计算各特征与转化率的相关性
correlation_matrix = df_clean.corr()
conversion_corr = correlation_matrix['converted'].sort_values(ascending=False)
print("与转化率相关性排序:")
print(conversion_corr)

# 卡方检验分析分类变量
categorical_vars = ['gender', 'device_type', 'traffic_source']
for var in categorical_vars:
    contingency_table = pd.crosstab(df_clean[var], df_clean['converted'])
    chi2, p_value, dof, expected = chi2_contingency(contingency_table)
    print(f"{var} 与转化率关联性: χ²={chi2:.3f}, p={p_value:.3f}")
                """,
                "result": {
                    "success": True,
                    "output": "与转化率相关性排序:\\nsession_duration: 0.342\\npage_views: 0.298\\n...",
                    "execution_time": 1.8
                },
                "insights": [
                    "页面浏览时长是影响转化率的最重要因素(r=0.342)",
                    "页面浏览次数与转化率呈中等正相关(r=0.298)",
                    "移动端用户转化率显著高于PC端用户",
                    "搜索引擎流量的转化率最高，社交媒体流量转化率较低"
                ]
            },
            {
                "step": 4,
                "objective": "预测模型构建",
                "code": """
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, roc_auc_score
from sklearn.preprocessing import LabelEncoder

# 特征工程
features = ['age', 'session_duration', 'page_views', 'previous_purchases', 
           'gender', 'device_type', 'traffic_source']

# 编码分类变量
le_gender = LabelEncoder()
le_device = LabelEncoder()
le_traffic = LabelEncoder()

df_model = df_clean.copy()
df_model['gender_encoded'] = le_gender.fit_transform(df_model['gender'])
df_model['device_encoded'] = le_device.fit_transform(df_model['device_type'])
df_model['traffic_encoded'] = le_traffic.fit_transform(df_model['traffic_source'])

# 准备训练数据
X = df_model[['age', 'session_duration', 'page_views', 'previous_purchases',
              'gender_encoded', 'device_encoded', 'traffic_encoded']]
y = df_model['converted']

# 训练模型
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X_train, y_train)

# 模型评估
y_pred = rf_model.predict(X_test)
y_pred_proba = rf_model.predict_proba(X_test)[:, 1]

print("模型性能评估:")
print(classification_report(y_test, y_pred))
print(f"AUC Score: {roc_auc_score(y_test, y_pred_proba):.3f}")

# 特征重要性
feature_importance = pd.DataFrame({
    'feature': X.columns,
    'importance': rf_model.feature_importances_
}).sort_values('importance', ascending=False)
print("\\n特征重要性排序:")
print(feature_importance)
                """,
                "result": {
                    "success": True,
                    "output": "模型性能评估:\\n              precision    recall  f1-score   support\\n\\n           0       0.89      0.95      0.92      1756\\n           1       0.67      0.45      0.54       244\\n\\n    accuracy                           0.87      2000\\n   macro avg       0.78      0.70      0.73      2000\\nweighted avg       0.86      0.87      0.86      2000\\n\\nAUC Score: 0.823",
                    "execution_time": 4.2
                },
                "insights": [
                    "随机森林模型在测试集上的AUC得分为0.823，表现良好",
                    "模型对非转化用户的识别准确率较高(95%)",
                    "页面浏览时长是最重要的预测特征(重要性: 0.28)",
                    "历史购买次数和页面浏览次数也是关键预测因子",
                    "模型可用于识别高转化潜力用户，支持精准营销"
                ]
            }
        ],
        chroma_collection_name="demo_ecommerce_analysis",
        errors=[],
        insights=[
            "电商用户转化率分析完成",
            "识别出页面浏览时长等关键影响因素",
            "构建了有效的转化率预测模型"
        ],
        data_summary={
            "shape": [10000, 12],
            "columns": ["user_id", "age", "gender", "device_type", "traffic_source", 
                       "session_duration", "page_views", "previous_purchases", 
                       "cart_additions", "purchase_amount", "converted", "timestamp"],
            "dtypes": {
                "user_id": "int64",
                "age": "float64", 
                "gender": "object",
                "device_type": "object",
                "traffic_source": "object",
                "session_duration": "float64",
                "page_views": "int64",
                "previous_purchases": "int64",
                "cart_additions": "int64", 
                "purchase_amount": "float64",
                "converted": "int64",
                "timestamp": "datetime64[ns]"
            },
            "missing_values": {
                "age": 150,
                "purchase_amount": 8756  # 未转化用户无购买金额
            },
            "basic_stats": {
                "age": {"mean": 34.2, "std": 8.7, "min": 18, "max": 65},
                "session_duration": {"mean": 245.6, "std": 180.3, "min": 10, "max": 1200},
                "page_views": {"mean": 5.8, "std": 4.2, "min": 1, "max": 25},
                "conversion_rate": 0.122
            }
        },
        final_report={
            "title_and_abstract": "电商用户转化率分析报告\n\n本报告基于10,000条用户行为数据，分析了影响电商平台用户转化率的关键因素。",
            "introduction": "用户转化率是电商平台的核心指标，直接影响业务收入和增长。",
            "data_description": "数据集包含用户基本信息、行为数据和转化结果，共12个变量。",
            "exploratory_analysis": "探索性分析发现用户年龄、浏览行为等与转化率存在关联。",
            "modeling_and_results": "使用随机森林模型预测用户转化，AUC得分达到0.823。",
            "discussion": "页面浏览时长是影响转化的最重要因素，移动端用户转化率更高。",
            "conclusion": "建议优化页面设计，提升用户体验，重点关注移动端用户。"
        }
    )


async def demo_complete_reflection_workflow():
    """演示完整的反思审查工作流程"""
    print("🚀 开始反思审查系统完整演示\n")
    
    # 1. 创建演示状态
    print("📊 创建演示分析状态...")
    demo_state = create_demo_state()
    task_id = demo_state["task_id"]
    print(f"   任务ID: {task_id}")
    print(f"   分析目标: {demo_state['original_query']}")
    print(f"   执行步骤: {len(demo_state['executed_steps'])} 个")
    print(f"   数据规模: {demo_state['data_summary']['shape']}")
    
    # 2. 生成初始报告
    print("\n📝 生成初始数据分析报告...")
    report_agent = ReportGenerationAgent()
    
    try:
        report_result = await report_agent.execute(demo_state)
        initial_report = report_result.get("final_report", {})
        print("   ✅ 初始报告生成完成")
        print(f"   📄 报告章节: {len(initial_report)} 个")
        
        # 更新状态
        demo_state["final_report"] = initial_report
        
    except Exception as e:
        print(f"   ❌ 初始报告生成失败: {str(e)}")
        return False
    
    # 3. 执行反思审查
    print("\n🧠 执行Reflexion框架反思审查...")
    reflection_agent = ReflectionAgent()
    
    try:
        reflection_result = await reflection_agent.execute(demo_state)
        
        evaluation = reflection_result.get("reflection_evaluation", {})
        reflection_insights = reflection_result.get("reflection_insights", {})
        improved_report_path = reflection_result.get("improved_report_path", "")
        
        print("   ✅ 反思审查完成")
        print(f"   📈 报告评分: {evaluation.get('overall_score', 'N/A')}/10")
        print(f"   🔍 发现问题: {len(evaluation.get('critical_issues', []))} 个")
        print(f"   💡 改进策略: {len(reflection_insights.get('improvement_strategies', {}))} 个")
        print(f"   📄 改进报告: {improved_report_path}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 反思审查失败: {str(e)}")
        return False


async def demo_reflection_components():
    """演示反思框架的各个组件"""
    print("\n🔬 Reflexion框架组件演示\n")
    
    demo_state = create_demo_state()
    reflection_agent = ReflectionAgent()
    
    # 1. 上下文收集演示
    print("📚 1. 上下文信息收集")
    try:
        context = await reflection_agent._collect_analysis_context(demo_state)
        print(f"   ✅ 收集完成")
        print(f"   📊 数据摘要: {bool(context.get('data_summary'))}")
        print(f"   🔄 执行步骤: {len(context.get('executed_steps', []))}")
        print(f"   🖼️  生成图片: {len(context.get('generated_images', []))}")
        print(f"   💾 ChromaDB洞察: {len(context.get('chroma_insights', []))}")
    except Exception as e:
        print(f"   ❌ 收集失败: {str(e)}")
    
    # 2. 报告评估演示
    print("\n🔍 2. 报告质量评估 (Evaluator)")
    try:
        context = await reflection_agent._collect_analysis_context(demo_state)
        evaluation = await reflection_agent._evaluate_report(demo_state, context)
        print(f"   ✅ 评估完成")
        print(f"   📊 总体评分: {evaluation.get('overall_score', 'N/A')}/10")
        
        critical_issues = evaluation.get('critical_issues', [])
        if critical_issues:
            print(f"   ⚠️  关键问题:")
            for i, issue in enumerate(critical_issues[:3], 1):
                print(f"      {i}. {issue}")
                
    except Exception as e:
        print(f"   ❌ 评估失败: {str(e)}")
    
    # 3. 反思分析演示
    print("\n🧠 3. 反思分析 (Self-Reflection)")
    try:
        reflection = await reflection_agent._generate_reflection(demo_state, context, evaluation)
        print(f"   ✅ 反思完成")
        
        strategies = reflection.get('improvement_strategies', {})
        print(f"   💡 改进策略: {len(strategies)} 个")
        for strategy, description in list(strategies.items())[:2]:
            print(f"      • {strategy}: {description[:60]}...")
            
        priorities = reflection.get('rewrite_priorities', [])
        if priorities:
            print(f"   🎯 重写优先级: {', '.join(priorities[:3])}")
            
    except Exception as e:
        print(f"   ❌ 反思失败: {str(e)}")
    
    print("\n✨ Reflexion框架组件演示完成")


async def main():
    """主演示函数"""
    print("🎯 反思审查系统 (Reflexion Framework) 完整演示")
    print("=" * 60)
    
    # 演示组件功能
    await demo_reflection_components()
    
    # 演示完整工作流程
    success = await demo_complete_reflection_workflow()
    
    print("\n" + "=" * 60)
    print("📊 演示总结:")
    print("✅ Reflexion框架三大组件:")
    print("   🔍 Evaluator - 报告质量评估")
    print("   🧠 Self-Reflection - 问题分析和改进策略")
    print("   🔄 Actor - 基于反思重新生成改进报告")
    print("\n✅ 核心功能:")
    print("   📈 多维度报告质量评估")
    print("   🎯 智能问题识别和根因分析")
    print("   💡 具体改进建议生成")
    print("   📄 自动化报告改进重写")
    print("   🔄 实时流式进度反馈")
    
    if success:
        print("\n🎉 反思审查系统演示成功完成!")
        print("💡 系统已准备好集成到完整的数据分析工作流中。")
        return 0
    else:
        print("\n⚠️  演示过程中遇到问题，请检查日志。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
