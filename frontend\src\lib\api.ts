/**
 * API服务层 - 处理与后端的HTTP通信
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import {
  AnalysisTask,
  TaskCreateRequest,
  TaskListResponse,
  TaskResultsResponse,
  StatsResponse,
  PlanApprovalRequest,
  UserInteractionResponse
} from '@/types/analysis';

// API配置
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
const API_VERSION = '/api/v1';

class ApiService {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: `${API_BASE_URL}${API_VERSION}`,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API] Request error:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response: AxiosResponse) => {
        console.log(`[API] Response ${response.status}:`, response.data);
        return response;
      },
      (error) => {
        console.error('[API] Response error:', {
          message: error.message,
          status: error.response?.status,
          data: error.response?.data,
          code: error.code,
          config: error.config
        });

        // 统一错误处理
        if (error.response?.status === 404) {
          throw new Error('请求的资源不存在');
        } else if (error.response?.status === 422) {
          throw new Error(`请求参数错误: ${error.response?.data?.detail || '参数验证失败'}`);
        } else if (error.response?.status === 500) {
          throw new Error('服务器内部错误，请稍后重试');
        } else if (error.code === 'ECONNABORTED') {
          throw new Error('请求超时，请检查网络连接');
        } else if (error.code === 'ERR_NETWORK') {
          throw new Error('网络错误，请检查CORS配置和后端服务');
        } else if (!error.response) {
          throw new Error('网络连接失败，请检查后端服务是否启动');
        }

        throw error;
      }
    );
  }

  /**
   * 创建分析任务
   */
  async createTask(prompt: string, file?: File): Promise<AnalysisTask> {
    const formData = new FormData();
    formData.append('prompt', prompt);
    
    if (file) {
      formData.append('file', file);
    }

    const response = await this.client.post<AnalysisTask>('/tasks', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return response.data;
  }

  /**
   * 获取任务详情
   */
  async getTask(taskId: string): Promise<AnalysisTask> {
    const response = await this.client.get<AnalysisTask>(`/tasks/${taskId}`);
    return response.data;
  }

  /**
   * 获取任务列表
   */
  async getTasks(limit = 10, offset = 0): Promise<TaskListResponse> {
    const response = await this.client.get<TaskListResponse>('/tasks', {
      params: { limit, offset }
    });
    return response.data;
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId: string): Promise<{ message: string; task_id: string }> {
    const response = await this.client.delete(`/tasks/${taskId}`);
    return response.data;
  }

  /**
   * 获取任务执行结果
   */
  async getTaskResults(taskId: string): Promise<TaskResultsResponse> {
    const response = await this.client.get<TaskResultsResponse>(`/tasks/${taskId}/results`);
    return response.data;
  }

  /**
   * 获取任务洞察
   */
  async getTaskInsights(taskId: string): Promise<{ task_id: string; insights: string[] }> {
    const response = await this.client.get(`/tasks/${taskId}/insights`);
    return response.data;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{ status: string; timestamp: string; version: string }> {
    const response = await this.client.get('/health');
    return response.data;
  }

  /**
   * 获取系统统计信息
   */
  async getStats(): Promise<StatsResponse> {
    const response = await this.client.get<StatsResponse>('/stats');
    return response.data;
  }

  /**
   * 审批分析计划
   */
  async approvePlan(taskId: string, request: PlanApprovalRequest): Promise<UserInteractionResponse> {
    // 增加超时时间，因为后端需要处理更多逻辑
    const response = await this.client.post<UserInteractionResponse>(
      `/tasks/${taskId}/approve-plan`,
      request,
      { timeout: 5000 } // 减少超时时间到5秒，因为我们只需要确认请求已接收
    );
    return response.data;
  }

  /**
   * 获取静态文件URL（图表等）
   */
  getStaticFileUrl(filePath: string): string {
    // 移除路径中的前导斜杠和相对路径标识
    const cleanPath = filePath.replace(/^\.?\//, '');
    return `${API_BASE_URL}/static/${cleanPath}`;
  }

  /**
   * 获取任务文件列表
   */
  async getTaskFiles(taskId: string): Promise<{
    files: Array<{
      name: string;
      path: string;
      type: string;
      size: number;
      created: number;
      modified: number;
    }>
  }> {
    const response = await this.client.get<{
      files: Array<{
        name: string;
        path: string;
        type: string;
        size: number;
        created: number;
        modified: number;
      }>
    }>(`/tasks/${taskId}/files`);
    return response.data;
  }

  /**
   * 获取文件内容
   */
  async getFileContent(taskId: string, fileName: string): Promise<string> {
    const response = await this.client.get<string>(`/tasks/${taskId}/files/${encodeURIComponent(fileName)}/content`);
    return response.data;
  }
}

// 创建单例实例
export const apiService = new ApiService();

// 导出类型和实例
export default apiService;
