
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface ShimmerButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'default' | 'outline';
  size?: 'default' | 'sm' | 'lg';
}

export function ShimmerButton({ 
  children, 
  className, 
  variant = 'default',
  size = 'default',
  ...props 
}: ShimmerButtonProps) {
  return (
    <Button
      variant={variant as any}
      size={size as any}
      className={cn(
        'relative overflow-hidden group',
        'before:absolute before:inset-0 before:-translate-x-full before:skew-x-12',
        'before:bg-gradient-to-r before:from-transparent before:via-white/30 before:to-transparent',
        'before:transition-transform before:duration-1000 before:ease-out',
        'hover:before:translate-x-full',
        'hover:scale-105 transition-all duration-300',
        variant === 'default' && 'bg-gradient-to-r from-primary to-primary-glow shadow-lg shadow-primary/25',
        className
      )}
      {...props}
    >
      {children}
    </Button>
  );
}
