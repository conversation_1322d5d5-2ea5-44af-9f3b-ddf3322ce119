"""应用配置设置"""

import os
from typing import List, Dict, Any
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""

    def __init__(self, **kwargs):
        # 过滤掉VITE_开头的环境变量
        filtered_env = {k: v for k, v in os.environ.items() if not k.startswith('VITE_')}
        # 临时替换环境变量
        original_env = os.environ.copy()
        os.environ.clear()
        os.environ.update(filtered_env)

        try:
            super().__init__(**kwargs)
        finally:
            # 恢复原始环境变量
            os.environ.clear()
            os.environ.update(original_env)

    # AI模型配置
    openai_api_base: str = "https://api.deepseek.com/v1"
    openai_api_key: str
    openai_model_name: str = "deepseek-chat"
    
    # ChromaDB配置
    chroma_persist_directory: str = "./data/chroma"
    chroma_host: str = "localhost"
    chroma_port: int = 8000
    
    # FastAPI配置
    api_host: str = "0.0.0.0"
    api_port: int = 8001
    api_reload: bool = True
    
    # Jupyter配置
    jupyter_timeout: int = 30
    jupyter_kernel_name: str = "python3"
    
    # 日志配置
    log_level: str = "INFO"
    log_format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    
    # 安全配置
    secret_key: str = "your-secret-key-here"
    cors_origins: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "http://localhost:8081",
        "http://127.0.0.1:8081"
    ]
    
    # 数据存储配置
    data_directory: str = "./data"
    upload_directory: str = "./data/uploads"
    results_directory: str = "./data/results"
    
    # 任务配置
    max_concurrent_tasks: int = 5
    task_timeout: int = 300
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        extra = "ignore"  # 忽略额外的环境变量
        env_prefix = ""  # 不使用前缀，避免读取VITE_变量


# 创建全局设置实例
settings = Settings()
