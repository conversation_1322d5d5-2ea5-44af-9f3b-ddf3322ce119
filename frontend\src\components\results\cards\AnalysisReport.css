/* 分析报告专用样式 */
.analysis-report {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen', 'Ubuntu', '<PERSON><PERSON><PERSON>', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.7;
  color: #374151;
}

.analysis-report h1 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #e5e7eb;
  position: relative;
}

.analysis-report h1::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.analysis-report h2 {
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
  margin-top: 2rem;
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  border-left: 4px solid #3b82f6;
  border-radius: 0 0.5rem 0.5rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.analysis-report h3 {
  color: #374151;
  font-size: 1.25rem;
  font-weight: 600;
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  padding-left: 1rem;
}

.analysis-report p {
  margin-bottom: 1rem;
  text-align: justify;
  color: #4b5563;
}

.analysis-report strong {
  color: #1f2937;
  font-weight: 600;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}

.analysis-report ul, .analysis-report ol {
  margin: 1rem 0;
  padding-left: 0;
}

.analysis-report li {
  margin-bottom: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: #f9fafb;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
}

.analysis-report li:hover {
  background: #f3f4f6;
  transform: translateX(2px);
}

.analysis-report code {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: #f9fafb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analysis-report pre {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
  color: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.75rem;
  overflow-x: auto;
  margin: 1.5rem 0;
  border: 1px solid #374151;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  position: relative;
}

.analysis-report pre::before {
  content: '';
  position: absolute;
  top: 0.75rem;
  left: 1rem;
  width: 12px;
  height: 12px;
  background: #ef4444;
  border-radius: 50%;
  box-shadow: 20px 0 0 #f59e0b, 40px 0 0 #10b981;
}

.analysis-report pre code {
  background: none;
  padding: 0;
  border-radius: 0;
  box-shadow: none;
  font-size: 0.875rem;
  line-height: 1.6;
}

.analysis-report blockquote {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
  padding: 1rem 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  font-style: italic;
  color: #1e40af;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
  position: relative;
}

.analysis-report blockquote::before {
  content: '"';
  font-size: 3rem;
  color: #3b82f6;
  position: absolute;
  top: -0.5rem;
  left: 0.5rem;
  opacity: 0.3;
}

.analysis-report table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin: 1.5rem 0;
  background: white;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.analysis-report th {
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: white;
  font-weight: 600;
  padding: 1rem;
  text-align: left;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  text-transform: uppercase;
}

.analysis-report td {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e5e7eb;
  color: #4b5563;
  font-size: 0.875rem;
}

.analysis-report tr:nth-child(even) td {
  background: #f9fafb;
}

.analysis-report tr:hover td {
  background: #f3f4f6;
}

.analysis-report tr:last-child td {
  border-bottom: none;
}

/* 深色模式适配 */
.dark .analysis-report {
  color: #d1d5db;
}

.dark .analysis-report h2 {
  color: #f9fafb;
  background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
  border-left-color: #60a5fa;
}

.dark .analysis-report h3 {
  color: #e5e7eb;
  border-left-color: #fbbf24;
}

.dark .analysis-report p {
  color: #9ca3af;
}

.dark .analysis-report strong {
  color: #f9fafb;
  background: linear-gradient(135deg, #92400e 0%, #b45309 100%);
}

.dark .analysis-report li {
  background: #374151;
  color: #d1d5db;
}

.dark .analysis-report li:hover {
  background: #4b5563;
}

.dark .analysis-report blockquote {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 100%);
  color: #bfdbfe;
  border-left-color: #60a5fa;
}

.dark .analysis-report table {
  background: #374151;
}

.dark .analysis-report th {
  background: linear-gradient(135deg, #111827 0%, #1f2937 100%);
}

.dark .analysis-report td {
  border-bottom-color: #4b5563;
  color: #d1d5db;
}

.dark .analysis-report tr:nth-child(even) td {
  background: #4b5563;
}

.dark .analysis-report tr:hover td {
  background: #6b7280;
}

/* 动画效果 */
.analysis-report h1, .analysis-report h2, .analysis-report h3 {
  animation: fadeInUp 0.6s ease-out;
}

.analysis-report p, .analysis-report ul, .analysis-report ol, .analysis-report table, .analysis-report blockquote {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .analysis-report h1 {
    font-size: 1.5rem;
  }
  
  .analysis-report h2 {
    font-size: 1.25rem;
    padding: 0.5rem 0.75rem;
  }
  
  .analysis-report h3 {
    font-size: 1.125rem;
  }
  
  .analysis-report pre {
    padding: 1rem;
    font-size: 0.75rem;
  }
  
  .analysis-report table {
    font-size: 0.75rem;
  }
  
  .analysis-report th, .analysis-report td {
    padding: 0.5rem;
  }
}
