/**
 * WebSocket连接管理 - 处理实时通信
 */

import { WebSocketMessage, NodeUpdate } from '@/types/analysis';

type WebSocketEventHandler = (message: WebSocketMessage) => void;
type ConnectionStateHandler = (connected: boolean) => void;

class WebSocketManager {
  private ws: WebSocket | null = null;
  private taskId: string | null = null;
  private eventHandlers: Map<string, WebSocketEventHandler[]> = new Map();
  private connectionHandlers: ConnectionStateHandler[] = [];
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 1秒
  private heartbeatInterval: NodeJS.Timeout | null = null;
  private isManualClose = false;

  private readonly WS_BASE_URL = import.meta.env.VITE_WS_BASE_URL || 'ws://localhost:8001';

  /**
   * 连接到指定任务的WebSocket
   */
  connect(taskId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN && this.taskId === taskId) {
        resolve();
        return;
      }

      // 关闭现有连接
      this.disconnect();

      this.taskId = taskId;
      this.isManualClose = false;
      
      const wsUrl = `${this.WS_BASE_URL}/ws/${taskId}`;
      console.log(`[WebSocket] 连接到: ${wsUrl}`);

      try {
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log(`[WebSocket] 连接成功: ${taskId}`);
          this.reconnectAttempts = 0;
          this.startHeartbeat();
          this.notifyConnectionState(true);
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const message: WebSocketMessage = JSON.parse(event.data);
            console.log(`[WebSocket] 收到消息:`, message);
            this.handleMessage(message);
          } catch (error) {
            console.error('[WebSocket] 消息解析失败:', error, event.data);
          }
        };

        this.ws.onclose = (event) => {
          console.log(`[WebSocket] 连接关闭: ${event.code} - ${event.reason}`);
          this.stopHeartbeat();
          this.notifyConnectionState(false);

          // 只有在非正常关闭且不是手动关闭时才重连
          if (!this.isManualClose && event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            console.log(`[WebSocket] 准备重连，尝试次数: ${this.reconnectAttempts + 1}/${this.maxReconnectAttempts}`);
            this.scheduleReconnect();
          } else if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('[WebSocket] 达到最大重连次数，停止重连');
          }
        };

        this.ws.onerror = (error) => {
          console.error('[WebSocket] 连接错误:', error);
          reject(new Error('WebSocket连接失败'));
        };

      } catch (error) {
        console.error('[WebSocket] 创建连接失败:', error);
        reject(error);
      }
    });
  }

  /**
   * 断开WebSocket连接
   */
  disconnect(): void {
    this.isManualClose = true;
    this.stopHeartbeat();
    
    if (this.ws) {
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.close(1000, 'Manual disconnect');
      }
      this.ws = null;
    }
    
    this.taskId = null;
    this.reconnectAttempts = 0;
    this.notifyConnectionState(false);
  }

  /**
   * 发送消息
   */
  send(message: any): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message));
    } else {
      console.warn('[WebSocket] 连接未就绪，无法发送消息');
    }
  }

  /**
   * 添加事件监听器
   */
  on(event: string, handler: WebSocketEventHandler): void {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, []);
    }
    this.eventHandlers.get(event)!.push(handler);
  }

  /**
   * 移除事件监听器
   */
  off(event: string, handler: WebSocketEventHandler): void {
    const handlers = this.eventHandlers.get(event);
    if (handlers) {
      const index = handlers.indexOf(handler);
      if (index > -1) {
        handlers.splice(index, 1);
      }
    }
  }

  /**
   * 添加连接状态监听器
   */
  onConnectionChange(handler: ConnectionStateHandler): void {
    this.connectionHandlers.push(handler);
  }

  /**
   * 移除连接状态监听器
   */
  offConnectionChange(handler: ConnectionStateHandler): void {
    const index = this.connectionHandlers.indexOf(handler);
    if (index > -1) {
      this.connectionHandlers.splice(index, 1);
    }
  }

  /**
   * 获取连接状态
   */
  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }

  /**
   * 获取当前任务ID
   */
  getCurrentTaskId(): string | null {
    return this.taskId;
  }

  private handleMessage(message: WebSocketMessage): void {
    // 处理心跳响应
    if (message.event === 'pong') {
      return;
    }

    // 触发对应事件的处理器
    const handlers = this.eventHandlers.get(message.event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error(`[WebSocket] 事件处理器错误 (${message.event}):`, error);
        }
      });
    }

    // 触发通用消息处理器
    const allHandlers = this.eventHandlers.get('*');
    if (allHandlers) {
      allHandlers.forEach(handler => {
        try {
          handler(message);
        } catch (error) {
          console.error('[WebSocket] 通用事件处理器错误:', error);
        }
      });
    }
  }

  private notifyConnectionState(connected: boolean): void {
    this.connectionHandlers.forEach(handler => {
      try {
        handler(connected);
      } catch (error) {
        console.error('[WebSocket] 连接状态处理器错误:', error);
      }
    });
  }

  private scheduleReconnect(): void {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1); // 指数退避
    
    console.log(`[WebSocket] ${delay}ms后尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
    
    setTimeout(() => {
      if (this.taskId && !this.isManualClose) {
        this.connect(this.taskId).catch(error => {
          console.error('[WebSocket] 重连失败:', error);
        });
      }
    }, delay);
  }

  private startHeartbeat(): void {
    this.stopHeartbeat();
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected()) {
        this.send({
          type: 'ping',
          timestamp: Date.now()
        });
      }
    }, 30000); // 30秒心跳
  }

  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }
}

// 创建单例实例
export const wsManager = new WebSocketManager();

// 导出类型和实例
export default wsManager;
