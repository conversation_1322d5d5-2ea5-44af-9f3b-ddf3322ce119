// 测试reflection_complete事件的脚本
// 在浏览器控制台中运行

// 模拟发送reflection_complete事件
function testReflectionComplete(taskId) {
  const eventData = {
    type: 'reflection_complete',
    task_id: taskId,
    report_path: 'test_improved_report.json',
    message: '反思审查完成，改进报告已生成',
    timestamp: Date.now()
  };
  
  console.log('发送测试reflection_complete事件:', eventData);
  
  // 创建自定义事件
  const event = new CustomEvent('reflection_complete', {
    detail: eventData
  });
  
  // 触发事件
  window.dispatchEvent(event);
}

// 使用方法：
// testReflectionComplete('your_task_id_here');
console.log('测试脚本已加载，使用 testReflectionComplete(taskId) 来测试');
