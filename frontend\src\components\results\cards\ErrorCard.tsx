import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { ResultCard } from '@/types/analysis';
import { cn } from '@/lib/utils';

interface ErrorCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

interface ErrorData {
  error_type: string;
  error_message: string;
  original_code?: string;
  corrected_code?: string;
  correction_explanation?: string;
  stack_trace?: string;
}

export function ErrorCard({ card, isHighlighted }: ErrorCardProps) {
  const data = card.content as ErrorData;

  return (
    <Card className={cn(
      "result-card animate-fade-in-up border-destructive/50",
      isHighlighted && "ring-2 ring-primary"
    )}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="w-5 h-5 text-destructive" />
          Error & Correction
          <Badge variant="destructive" className="ml-auto">
            {data.error_type}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Error Alert */}
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="font-mono text-sm">
            {data.error_message}
          </AlertDescription>
        </Alert>

        {/* Stack Trace */}
        {data.stack_trace && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium">Stack Trace</h4>
            <div className="code-block max-h-32 overflow-y-auto">
              <pre className="text-xs whitespace-pre-wrap text-destructive">
                {data.stack_trace}
              </pre>
            </div>
          </div>
        )}

        {/* Original Code */}
        {data.original_code && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Code className="w-4 h-4" />
              Original Code (Failed)
            </h4>
            <div className="rounded-lg overflow-hidden border border-destructive/30">
              <SyntaxHighlighter
                language="python"
                style={oneDark}
                customStyle={{
                  margin: 0,
                  borderRadius: 0,
                  background: 'hsl(var(--destructive) / 0.1)',
                  fontSize: '14px',
                }}
              >
                {data.original_code}
              </SyntaxHighlighter>
            </div>
          </div>
        )}

        {/* Correction Explanation */}
        {data.correction_explanation && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <RotateCcw className="w-4 h-4 text-primary" />
              AI Correction Analysis
            </h4>
            <Alert>
              <AlertDescription>
                {data.correction_explanation}
              </AlertDescription>
            </Alert>
          </div>
        )}

        {/* Corrected Code */}
        {data.corrected_code && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium flex items-center gap-2">
              <Code className="w-4 h-4 text-success" />
              Corrected Code
            </h4>
            <div className="rounded-lg overflow-hidden border border-success/30">
              <SyntaxHighlighter
                language="python"
                style={oneDark}
                customStyle={{
                  margin: 0,
                  borderRadius: 0,
                  background: 'hsl(var(--success) / 0.1)',
                  fontSize: '14px',
                }}
              >
                {data.corrected_code}
              </SyntaxHighlighter>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}