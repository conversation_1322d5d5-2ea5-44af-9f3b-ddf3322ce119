import { Brain, Github, Twitter, Mail } from 'lucide-react';
import { Button } from '@/components/ui/button';
import logo from '@/assets/logo.png';

export function Footer() {
  const footerSections = [
    {
      title: '产品',
      links: [
        { name: '数据分析', href: '#' },
        { name: '预测建模', href: '#' },
        { name: '可视化报告', href: '#' },
        { name: 'API 集成', href: '#' },
      ]
    },
    {
      title: '资源',
      links: [
        { name: '文档中心', href: '#' },
        { name: '使用教程', href: '#' },
        { name: '最佳实践', href: '#' },
        { name: '社区支持', href: '#' },
      ]
    },
    {
      title: '公司',
      links: [
        { name: '关于我们', href: '#' },
        { name: '招聘信息', href: '#' },
        { name: '新闻动态', href: '#' },
        { name: '联系我们', href: '#' },
      ]
    }
  ];

  return (
    <footer className="bg-background border-t border-border">
      <div className="container mx-auto px-6 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2 space-y-4">
            <div className="flex items-center gap-3">
              <img src={logo} alt="AI 数据分析平台" className="w-8 h-8" />
              <div className="flex items-center gap-2">
                <Brain className="w-5 h-5 text-primary" />
                <span className="font-bold text-lg text-gradient">
                  AI 数据分析平台
                </span>
              </div>
            </div>
            <p className="text-muted-foreground leading-relaxed">
              通过透明可交互的AI技术，让数据分析变得简单高效。
              体验下一代数据科学平台，实现人机协同的深度数据洞察。
            </p>
            <div className="flex items-center gap-2">
              <Button variant="ghost" size="sm" className="w-9 h-9 p-0">
                <Github className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="w-9 h-9 p-0">
                <Twitter className="w-4 h-4" />
              </Button>
              <Button variant="ghost" size="sm" className="w-9 h-9 p-0">
                <Mail className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Footer Sections */}
          {footerSections.map((section) => (
            <div key={section.title} className="space-y-4">
              <h4 className="font-semibold text-foreground">{section.title}</h4>
              <ul className="space-y-2">
                {section.links.map((link) => (
                  <li key={link.name}>
                    <a
                      href={link.href}
                      className="text-sm text-muted-foreground hover:text-primary transition-colors"
                    >
                      {link.name}
                    </a>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="border-t border-border mt-12 pt-8">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="text-sm text-muted-foreground">
              © 2024 AI 数据分析平台. 保留所有权利。
            </div>
            <div className="flex items-center gap-6 text-sm text-muted-foreground">
              <a href="#" className="hover:text-primary transition-colors">隐私政策</a>
              <a href="#" className="hover:text-primary transition-colors">服务条款</a>
              <a href="#" className="hover:text-primary transition-colors">Cookie 政策</a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}