"""报告生成智能体 - 直接生成专业数据分析报告"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime
from langchain_core.messages import HumanMessage, SystemMessage
from src.agents.base_agent import BaseAgent
from src.models.state_models import AnalysisState
from src.database.chroma_client import chroma_client
from src.utils.logger import get_logger
from src.api.sse import report_stream_manager
from src.config.settings import settings

logger = get_logger(__name__)


class ReportGenerationAgent(BaseAgent):
    """报告生成智能体 - 直接生成专业数据分析报告"""

    def __init__(self):
        super().__init__()
        self.agent_name = "报告生成智能体"

        # 创建专用的流式LLM实例（仅用于报告生成）
        self.streaming_llm = self._create_streaming_llm()

        # 定义报告章节顺序
        self.report_sections = [
            "title_and_abstract",
            "introduction",
            "data_description",
            "exploratory_analysis",
            "modeling_and_results",
            "discussion",
            "conclusion"
        ]

    def _create_streaming_llm(self):
        """创建专用于报告生成的流式LLM实例"""
        from langchain_openai import ChatOpenAI
        from src.config.settings import settings

        return ChatOpenAI(
            base_url=settings.openai_api_base,
            api_key=settings.openai_api_key,
            model=settings.openai_model_name,
            temperature=0.7,  # 报告生成使用稍高的温度以增加创造性
            max_tokens=6000,
            streaming=True  # 启用流式传输
        )

    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行报告生成"""
        self._log_execution("开始生成数据分析报告")

        try:
            task_id = state["task_id"]

            # 使用简化的直接生成方式
            report_content = await self._generate_report_direct(state)

            # 保存完整报告
            report_path = await self._save_report(task_id, report_content)

            # 发送报告完成信号并设置状态
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_report_complete(task_id, report_path)
            report_stream_manager.set_report_complete(task_id, report_path)

            # 更新状态
            updated_state = {
                "final_report": report_content,
                "report_path": report_path,
                "insights": state.get("insights", []) + ["数据分析报告生成完成"]
            }

            self._log_execution("数据分析报告生成完成", f"报告路径: {report_path}")
            return updated_state

        except Exception as e:
            error_msg = self._format_error_message(str(e))
            self._log_execution("报告生成失败", str(e))
            return {
                "errors": state.get("errors", []) + [error_msg]
            }

    async def _generate_report_direct(self, state: AnalysisState) -> Dict[str, str]:
        """直接生成报告，不使用ReAct框架"""
        task_id = state["task_id"]
        report_content = {}

        # 开始报告生成流
        await report_stream_manager.start_report_stream(task_id)

        # 收集所有分析信息
        analysis_context = await self._collect_analysis_context(state)

        # 生成全局报告上下文，确保一致性
        global_context = await self._generate_global_context(analysis_context)

        section_names = {
            "title_and_abstract": "标题和摘要",
            "introduction": "引言/背景",
            "data_description": "数据描述性分析",
            "exploratory_analysis": "探索性分析",
            "modeling_and_results": "建模方法和模型结果",
            "discussion": "结果分析和探讨",
            "conclusion": "总结"
        }

        try:
            # 直接生成报告的各个部分
            for section in self.report_sections:
                section_name = section_names.get(section, section)
                logger.info(f"开始生成报告部分: {section}")

                # 发送章节开始信号
                await report_stream_manager.send_section_start(task_id, section, section_name)

                # 直接生成内容（流式）
                content = await self._generate_section_content(
                    task_id, section, analysis_context, global_context, report_content
                )

                report_content[section] = content

                # 发送章节完成信号
                await report_stream_manager.send_section_complete(task_id, section, section_name)
                logger.info(f"报告部分 {section} 生成完成")

            return report_content

        except Exception as e:
            # 发送错误信息
            await report_stream_manager.send_error(task_id, str(e))
            raise

    async def _generate_global_context(self, analysis_context: Dict[str, Any]) -> Dict[str, str]:
        """生成全局报告上下文，确保整体一致性

        注意：此方法使用普通LLM（非流式），因为全局上下文生成不需要实时显示
        """

        # 提取关键信息
        data_summary = analysis_context.get("data_summary", {})
        insights = analysis_context.get("insights", [])

        # 生成核心主题和发现
        core_findings_prompt = f"""
        基于以下分析结果，提取3-5个核心发现和主要洞察：

        数据概况：{data_summary}
        分析洞察：{insights}

        请用简洁的学术语言总结最重要的发现，这些将作为整个报告的核心主线。
        """

        messages = [
            {"role": "system", "content": "你是一位资深数据科学家，擅长提炼分析结果的核心价值。"},
            {"role": "user", "content": core_findings_prompt}
        ]

        # 使用普通LLM（非流式）
        response = await self.llm.ainvoke(messages)
        core_findings = response.content

        # 生成写作风格指导
        style_guide = """
        本报告采用严谨的学术写作风格：
        1. 使用客观、专业的语言表达
        2. 逻辑清晰，论证严密
        3. 数据驱动，结论有据
        4. 长段落和短段落相互交错，结构层次分明
        5. 避免口语化表达，使用规范的学术术语
        """

        return {
            "core_findings": core_findings,
            "style_guide": style_guide,
            "research_theme": analysis_context.get("original_query", "数据分析研究")
        }

    async def _generate_section_content(
        self,
        task_id: str,
        section: str,
        analysis_context: Dict[str, Any],
        global_context: Dict[str, str],
        existing_content: Dict[str, str]
    ) -> str:
        """生成单个章节的内容

        注意：此方法使用流式LLM，实现实时内容生成和传输
        """

        # 获取章节特定的提示词
        section_prompt = self._get_section_prompt(section, analysis_context, global_context, existing_content)

        messages = [
            {"role": "system", "content": self._get_academic_system_prompt()},
            {"role": "user", "content": section_prompt}
        ]

        # 使用流式生成，确保实时传输
        full_content = ""
        chunk_buffer = ""
        word_count = 0
        stream_count = 0

        logger.info(f"开始流式生成章节 {section}")

        async for chunk in self.streaming_llm.astream(messages):
            if hasattr(chunk, 'content') and chunk.content:
                chunk_buffer += chunk.content
                full_content += chunk.content
                word_count += len(chunk.content)
                stream_count += 1

                # 每累积一定字符数或遇到句号、换行符时发送一次
                if (word_count >= 20 or
                    chunk.content.endswith(('。', '！', '？', '\n', '.', '!', '?')) or
                    len(chunk_buffer) >= 80 or
                    stream_count % 3 == 0):  # 每10个chunk强制发送一次

                    logger.debug(f"发送流式内容: {section}, 长度: {len(full_content)}, 累积字符: {word_count}")
                    await report_stream_manager.send_section_content(
                        task_id, section, full_content, is_partial=True
                    )
                    chunk_buffer = ""
                    word_count = 0

        logger.info(f"章节 {section} 流式生成完成，总长度: {len(full_content)}")

        # 发送最终完整内容
        logger.info(f"发送章节 {section} 最终内容，总长度: {len(full_content)}")
        await report_stream_manager.send_section_content(task_id, section, full_content, is_partial=False)

        return full_content

    def _get_academic_system_prompt(self) -> str:
        """获取学术写作风格的系统提示词"""
        return """
        你是一位顶级期刊的资深数据科学研究员，具有深厚的学术写作功底。你的任务是撰写高质量的数据分析报告。

        写作要求：
        1. **学术严谨性**：使用精确、客观的学术语言，避免主观臆断
        2. **逻辑连贯性**：确保论述逻辑清晰，前后呼应，层次分明
        3. **专业深度**：展现深入的数据洞察和专业分析能力
        4. **结构完整性**：每个段落都有明确的主题，段落间过渡自然
        5. **证据支撑**：所有结论都基于数据证据，避免空泛表述

        语言风格：
        - 使用第三人称客观表述
        - 采用规范的学术术语和表达方式
        - 写作语言仅为中文
        - 长短段落交错，长段落也不要太长（10句话以内），短段落不要太短（3句话以上）
        - 避免口语化和非正式表达
        - 注重数据的准确性和分析的深度

        请确保生成的内容具有顶级学术期刊如`Nature`、`Science`的专业水准。
        """

    async def _collect_analysis_context(self, state: AnalysisState) -> Dict[str, Any]:
        """收集分析上下文信息"""
        task_id = state["task_id"]

        # 从ChromaDB获取相关文档
        try:
            docs = await chroma_client.search_documents(
                collection_name=f"task_{task_id}",
                query_text="数据分析结果 统计信息 模型结果",
                n_results=20
            )
        except Exception as e:
            logger.warning(f"获取ChromaDB文档失败: {e}")
            docs = []

        # 收集状态中的信息
        context = {
            "task_id": task_id,
            "original_query": state.get("original_query", ""),
            "data_summary": state.get("data_summary", {}),
            "insights": state.get("insights", []),
            "errors": state.get("errors", []),
            "documents": docs
        }

        return context

    def _get_section_prompt(
        self,
        section: str,
        analysis_context: Dict[str, Any],
        global_context: Dict[str, str],
        existing_content: Dict[str, str]
    ) -> str:
        """获取特定章节的提示词"""

        # 基础上下文信息
        data_summary = analysis_context.get("data_summary", {})
        insights = analysis_context.get("insights", [])
        core_findings = global_context.get("core_findings", "")
        research_theme = global_context.get("research_theme", "")

        # 已生成的内容，用于保持一致性
        previous_sections = "\n\n".join([
            f"### {k.replace('_', ' ').title()}\n{v}"
            for k, v in existing_content.items()
        ]) if existing_content else "暂无前文内容"

        if section == "title_and_abstract":
            return f"""
            请为本数据分析研究撰写标题和摘要部分。

            **研究主题**: {research_theme}
            **核心发现**: {core_findings}
            **数据概况**: {data_summary}
            **主要洞察**: {insights}

            **撰写要求**:
            1. **标题**: 简洁明确，体现研究的核心价值和创新点，15-20字为宜
            2. **摘要**: 200-300字，包含以下要素：
               - 研究背景和目标（2-3句）
               - 数据来源和分析方法（2-3句）
               - 主要发现和结论（3-4句）
               - 研究意义和应用价值（1-2句）

            **学术标准**:
            - 使用客观、精确的学术语言
            - 突出数据驱动的分析方法
            - 强调发现的科学价值和实际意义
            - 避免夸大或主观性表述

            请生成符合顶级期刊标准的标题和摘要。
            """

        elif section == "introduction":
            return f"""
            请撰写引言/背景部分，为整个研究奠定理论基础。

            **研究主题**: {research_theme}
            **核心发现**: {core_findings}
            **数据概况**: {data_summary}
            **前文内容**: {previous_sections}

            **撰写要求**:
            1. **研究背景**: 阐述问题的重要性和研究必要性（2-3段）
            2. **文献综述**: 简要回顾相关研究现状和理论基础（1-2段）
            3. **研究目标**: 明确本研究的具体目标和预期贡献（1段）
            4. **方法概述**: 简要介绍采用的分析方法和技术路线（1段）
            5. **报告结构**: 说明报告的组织结构和各部分内容（1段）

            **学术标准**:
            - 体现问题的理论价值和实践意义
            - 展现对领域现状的深入理解
            - 逻辑递进，从宏观到具体
            - 与前文标题摘要保持一致的研究主线

            请确保内容具有学术深度和理论高度。
            """

        elif section == "data_description":
            return f"""
            请撰写数据描述性分析部分，全面展现数据的特征和质量。

            **数据概况**: {data_summary}
            **分析洞察**: {insights}
            **前文内容**: {previous_sections}

            **撰写要求**:
            1. **数据来源**: 详细描述数据的来源、收集方法和时间范围（1-2段）
            2. **数据结构**: 分析数据集的维度、变量类型和基本统计特征（2-3段）
            3. **数据质量**: 评估数据完整性、一致性和可靠性（1-2段）
            4. **预处理过程**: 描述数据清洗、转换和标准化的具体步骤（1-2段）
            5. **变量定义**: 对关键变量进行明确定义和分类（1段）

            **学术标准**:
            - 使用统计学术语准确描述数据特征
            - 客观评估数据的优势和局限性
            - 为后续分析奠定坚实的数据基础
            - 体现数据处理的科学性和规范性

            请确保描述准确、全面且具有专业性。
            """

        # 继续其他章节...
        else:
            return self._get_remaining_section_prompts(section, analysis_context, global_context, existing_content)

    def _get_remaining_section_prompts(
        self,
        section: str,
        analysis_context: Dict[str, Any],
        global_context: Dict[str, str],
        existing_content: Dict[str, str]
    ) -> str:
        """获取剩余章节的提示词"""

        insights = analysis_context.get("insights", [])
        core_findings = global_context.get("core_findings", "")

        previous_sections = "\n\n".join([
            f"### {k.replace('_', ' ').title()}\n{v}"
            for k, v in existing_content.items()
        ]) if existing_content else "暂无前文内容"

        if section == "exploratory_analysis":
            return f"""
            请撰写探索性分析部分，深入挖掘数据中的模式和关系。

            **核心发现**: {core_findings}
            **分析洞察**: {insights}
            **前文内容**: {previous_sections}

            **撰写要求**:
            1. **分布特征分析**: 深入分析关键变量的分布特征和统计性质（2-3段）
            2. **相关性分析**: 探讨变量间的相关关系和依赖模式（2-3段）
            3. **异常值检测**: 识别和分析数据中的异常模式（1-2段）
            4. **分组比较**: 基于不同维度进行分组对比分析（2-3段）
            5. **趋势发现**: 揭示数据中的潜在趋势和规律（1-2段）

            **学术标准**:
            - 如果有必要可以增加一些数学公式来增强分析效果
            - 运用统计学理论解释发现的模式
            - 提供深入的数据洞察和专业解读
            - 为后续建模分析提供理论支撑
            - 体现探索性分析的科学方法

            请确保分析深入、逻辑清晰且具有洞察力。
            """

        elif section == "modeling_and_results":
            return f"""
            请撰写建模方法和模型结果部分，展现分析的技术深度。

            **分析洞察**: {insights}
            **核心发现**: {core_findings}
            **前文内容**: {previous_sections}

            **撰写要求**:
            1. **方法选择**: 阐述选择特定分析方法的理论依据（1-2段）
            2. **模型构建**: 详细描述模型的构建过程和参数设置（2-3段）
            3. **结果呈现**: 系统展示模型的主要结果和性能指标（2-3段）
            4. **模型验证**: 分析模型的可靠性和泛化能力（1-2段）
            5. **结果解释**: 从业务角度解读模型结果的实际意义（1-2段）

            **学术标准**:
            - 体现方法选择的科学性和合理性
            - 使用专业术语准确描述技术细节
            - 模型构建时增加必要的数学公式（也可以有数学推导）和描述，体现出专业性和学术性
            - 客观评估模型的优势和局限性
            - 强调结果的统计显著性和实用价值

            请确保技术描述准确且具有专业深度。
            """

        elif section == "discussion":
            return f"""
            请撰写结果分析和探讨部分，深入阐释发现的意义。

            **核心发现**: {core_findings}
            **分析洞察**: {insights}
            **前文内容**: {previous_sections}

            **撰写要求**:
            1. **结果综合**: 整合前文所有分析结果，形成完整的认知图景（2-3段）
            2. **理论阐释**: 从理论角度解释发现的深层原因和机制（2-3段）
            3. **实践意义**: 分析结果对实际应用的指导价值（2-3段）
            4. **局限性讨论**: 客观分析研究的局限性和改进方向（1-2段）
            5. **创新贡献**: 突出本研究的创新点和学术贡献（1-2段）

            **学术标准**:
            - 展现深入的理论思考和学术洞察
            - 体现批判性思维和科学严谨性
            - 平衡肯定成果与承认局限
            - 为领域发展提供有价值的思考

            请确保讨论具有理论深度和实践价值。
            """

        elif section == "conclusion":
            return f"""
            请撰写总结部分，为整个研究画下完美句号。

            **核心发现**: {core_findings}
            **研究主题**: {global_context.get("research_theme", "")}
            **前文内容**: {previous_sections}

            **撰写要求**:
            1. **主要发现总结**: 简明扼要地总结最重要的研究发现（1-2段）
            2. **理论贡献**: 阐述本研究对理论发展的贡献（1段）
            3. **实践价值**: 强调研究结果的实际应用价值（1段）
            4. **研究局限**: 诚实地指出研究的局限性（1段）
            5. **未来展望**: 提出后续研究的方向和建议（1段）

            **学术标准**:
            - 与引言部分形成呼应，体现研究的完整性
            - 突出研究的创新性和重要性
            - 保持客观和谦逊的学术态度
            - 为领域未来发展指明方向

            请确保总结简洁有力且具有前瞻性。
            """

        else:
            return f"请生成{section}部分的专业内容。"

    async def _save_report(self, task_id: str, sections: Dict[str, str]) -> str:
        """保存完整报告到文件"""
        # 创建报告目录
        report_dir = os.path.join(settings.results_directory, task_id)
        os.makedirs(report_dir, exist_ok=True)

        # 生成报告文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"analysis_report_{timestamp}.md"
        report_path = os.path.join(report_dir, report_filename)

        # 组装完整报告
        report_content = self._assemble_report(sections)

        # 保存到文件
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(report_content)

        logger.info(f"报告已保存: {report_path}")
        return report_filename

    def _assemble_report(self, sections: Dict[str, str]) -> str:
        """组装完整报告"""
        section_titles = {
            "title_and_abstract": "# 数据分析报告\n\n## 摘要",
            "introduction": "## 引言",
            "data_description": "## 数据描述",
            "exploratory_analysis": "## 探索性分析",
            "modeling_and_results": "## 建模与结果",
            "discussion": "## 讨论",
            "conclusion": "## 结论"
        }

        report_parts = []

        # 按顺序组装各部分
        for section in self.report_sections:
            content = sections.get(section, "")
            if content:
                title = section_titles.get(section, "")
                if title:
                    report_parts.append(title)
                report_parts.append(content)
                report_parts.append("")  # 添加空行分隔

        return "\n".join(report_parts)