"""代码生成和执行智能体 - 直接执行模式"""

import json
from typing import Dict, Any, Optional
from langchain_core.messages import HumanMessage, SystemMessage
from src.agents.base_agent import BaseAgent
from src.models.state_models import AnalysisState
from src.models.api_models import NodeUpdate, NodeStatus, WebSocketMessage
from src.execution.jupyter_executor import jupyter_executor
from src.database.chroma_client import chroma_client
from src.utils.id_generator import generate_collection_name
from src.api.websocket import websocket_manager
from datetime import datetime


class CodeAgent(BaseAgent):
    """代码生成和执行智能体 - 直接执行模式"""

    def __init__(self):
        super().__init__(
            name="代码执行智能体",
            description="直接生成和执行Python代码，进行数据分析，支持自动纠错"
        )
        self.max_retries = 3
    
    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行代码生成和执行任务 - 直接执行模式"""
        self._log_execution("开始代码生成和执行")
        print(f"[DEBUG] CodeAgent.execute 被调用 - 使用直接执行模式")

        try:
            # 获取当前步骤信息
            current_step = state.get("current_step", 0)
            print(f"[DEBUG] 当前步骤: {current_step}")

            # 优先使用用户修改的计划
            plan = state.get("user_modified_plan") or state.get("plan", {})
            print(f"[DEBUG] 计划类型: {'user_modified_plan' if state.get('user_modified_plan') else 'plan'}")
            print(f"[DEBUG] 计划内容: {plan}")

            if not plan:
                return {"errors": state.get("errors", []) + ["没有找到执行计划"]}

            steps = plan.get("steps", [])
            if not steps:
                return {"errors": state.get("errors", []) + ["计划中没有步骤"]}

            if current_step >= len(steps):
                return {"errors": state.get("errors", []) + [f"步骤索引超出范围: {current_step} >= {len(steps)}"]}

            step_info = steps[current_step]
            print(f"[DEBUG] 步骤信息: {step_info}")

            if not step_info:
                return {"errors": state.get("errors", []) + [f"步骤 {current_step} 的信息为空"]}

            # 获取用户的库偏好
            library_preferences = state.get("library_preferences", {})
            task_id = state["task_id"]

            # 直接执行步骤
            result = await self._direct_execute(state, step_info)

            # 更新状态
            executed_steps = state.get("executed_steps", [])
            executed_steps.append(result)

            updated_state = {
                "executed_steps": executed_steps,
                "current_step": current_step + 1
            }

            # 如果有新的洞察，添加到状态中
            if result.get("insights"):
                insights = state.get("insights", [])
                # 洞察现在是字符串，添加到列表中
                if isinstance(result["insights"], str):
                    insights.append(result["insights"])
                else:
                    insights.extend(result["insights"])
                updated_state["insights"] = insights

            self._log_execution("代码执行完成", f"步骤 {current_step + 1}")
            return updated_state

        except Exception as e:
            error_msg = self._format_error_message(str(e))
            self._log_execution("代码执行失败", str(e))
            return {
                "errors": state.get("errors", []) + [error_msg]
            }
    
    async def _direct_execute(self, state: AnalysisState, step_info: Dict[str, Any]) -> Dict[str, Any]:
        """直接执行步骤，支持自动纠错"""
        task_id = state["task_id"]
        objective = step_info["objective"]
        current_step = state.get("current_step", 0)
        step_node_id = f"step-{current_step + 1}"

        # 发送步骤开始消息
        print(f"[DEBUG] 即将发送步骤开始消息: {task_id}, {step_node_id}")
        await self._send_step_update(task_id, step_node_id, objective, "started", {
            "message": f"开始执行步骤: {objective}"
        })
        print(f"[DEBUG] 步骤开始消息已发送")

        # 代码生成和执行循环（最多3次尝试）
        for attempt in range(self.max_retries):
            self._log_execution(f"尝试 {attempt + 1}/{self.max_retries}", objective)

            # 发送代码生成开始消息
            await self._send_step_update(task_id, step_node_id, objective, "generating_code", {
                "attempt": attempt + 1,
                "message": f"正在生成代码 (尝试 {attempt + 1}/{self.max_retries})"
            })

            # 生成代码
            print(f"[DEBUG] 准备生成代码，state keys: {list(state.keys())}")
            print(f"[DEBUG] step_info: {step_info}")
            code = await self._generate_code(state, step_info, attempt)

            # 发送代码生成完成消息
            await self._send_step_update(task_id, step_node_id, objective, "code_generated", {
                "attempt": attempt + 1,
                "code": code,
                "message": "代码生成完成，开始执行"
            })

            # 执行代码
            execution_result = await jupyter_executor.execute_code(task_id, code)

            # 发送代码执行结果消息
            await self._send_step_update(task_id, step_node_id, objective, "code_executed", {
                "attempt": attempt + 1,
                "code": code,
                "execution_result": execution_result.model_dump(),
                "success": execution_result.success,
                "message": "代码执行完成" if execution_result.success else f"代码执行失败: {execution_result.stderr}"
            })

            if execution_result.success:
                # 成功执行，生成洞察
                await self._send_step_update(task_id, step_node_id, objective, "generating_insights", {
                    "message": "正在生成分析洞察"
                })

                insights = await self._generate_insights(state, step_info, code, execution_result)

                # 记录到ChromaDB
                await self._log_to_chroma(state, step_info, code, execution_result, insights)

                # 发送最终成功消息
                await self._send_step_update(task_id, step_node_id, objective, "completed", {
                    "code": code,
                    "execution_result": execution_result.model_dump(),
                    "insights": insights,
                    "message": "步骤执行成功"
                })

                return {
                    "step_id": step_node_id,
                    "step_name": objective,
                    "status": "success",
                    "code": code,
                    "result": execution_result.model_dump(),
                    "insights": insights,
                    "execution_time": execution_result.execution_time,
                    "timestamp": datetime.now()
                }
            else:
                # 执行失败，记录错误并准备重试
                await self._log_to_chroma(state, step_info, code, execution_result, [])

                # 保存错误信息到状态中，供下次尝试参考
                state["last_execution_error"] = execution_result.stderr

                if attempt == self.max_retries - 1:
                    # 最后一次尝试失败
                    await self._send_step_update(task_id, step_node_id, objective, "failed", {
                        "code": code,
                        "execution_result": execution_result.model_dump(),
                        "error": execution_result.stderr,
                        "message": f"步骤执行失败，已尝试 {self.max_retries} 次"
                    })

                    return {
                        "step_id": step_node_id,
                        "step_name": objective,
                        "status": "error",
                        "code": code,
                        "result": execution_result.model_dump(),
                        "error": execution_result.stderr,
                        "execution_time": execution_result.execution_time,
                        "timestamp": datetime.now()
                    }
                else:
                    # 准备重试，发送重试消息
                    await self._send_step_update(task_id, step_node_id, objective, "retrying", {
                        "attempt": attempt + 1,
                        "error": execution_result.stderr,
                        "message": f"执行失败，准备重试 (尝试 {attempt + 2}/{self.max_retries})"
                    })

                # 继续下一次尝试
                continue

    async def _send_step_update(self, task_id: str, step_node_id: str, step_name: str,
                               status: str, content: Dict[str, Any]) -> None:
        """发送步骤更新消息到前端"""
        try:
            from src.models.api_models import WebSocketMessage

            message = WebSocketMessage(
                event="step_update",
                payload={
                    "step_node_id": step_node_id,
                    "step_name": step_name,
                    "status": status,
                    "content": content,
                    "timestamp": datetime.now().isoformat()
                }
            )

            # 添加详细的调试日志
            print(f"[DEBUG] 准备发送步骤更新: task_id={task_id}, status={status}")
            print(f"[DEBUG] 消息内容: {message.model_dump()}")

            # 检查WebSocket连接数
            connection_count = websocket_manager.get_connection_count(task_id)
            print(f"[DEBUG] WebSocket连接数: {connection_count}")

            await websocket_manager.send_message(task_id, message)
            self._log_execution(f"步骤更新已发送: {step_node_id} - {status}")
            print(f"[DEBUG] 步骤更新发送成功: {step_node_id} - {status}")

        except Exception as e:
            self._log_execution(f"发送步骤更新失败: {str(e)}")
            print(f"[DEBUG] 发送步骤更新失败: {str(e)}")
            import traceback
            print(f"[DEBUG] 错误堆栈: {traceback.format_exc()}")
            # 不要因为WebSocket失败而中断执行


    
    async def _generate_code(self, state: AnalysisState, step_info: Dict[str, Any], attempt: int) -> str:
        """生成Python代码，支持基于错误的自动纠错"""
        print(f"[DEBUG] _generate_code 开始")
        print(f"[DEBUG] state 类型: {type(state)}")
        print(f"[DEBUG] state 是否为 None: {state is None}")

        if state is None:
            raise ValueError("state 不能为 None")

        print(f"[DEBUG] step_info: {step_info}")

        # 根据尝试次数调整代码复杂度
        complexity_level = "高级" if attempt == 0 else "中等" if attempt == 1 else "简单"
        print(f"[DEBUG] complexity_level: {complexity_level}")

        # 获取用户的库偏好
        print(f"[DEBUG] 准备获取 library_preferences")
        print(f"[DEBUG] state 中的 library_preferences 原始值: {state.get('library_preferences')}")
        library_preferences = state.get("library_preferences") or {}
        print(f"[DEBUG] library_preferences 处理后: {library_preferences}, type: {type(library_preferences)}")

        # 构建库偏好提示
        library_hints = []
        data_processing = library_preferences.get("data_processing")
        if data_processing and isinstance(data_processing, list):
            library_hints.append(f"数据处理优先使用: {', '.join(data_processing)}")

        visualization = library_preferences.get("visualization")
        if visualization and isinstance(visualization, list):
            library_hints.append(f"可视化优先使用: {', '.join(visualization)}")

        machine_learning = library_preferences.get("machine_learning")
        if machine_learning and isinstance(machine_learning, list):
            library_hints.append(f"机器学习优先使用: {', '.join(machine_learning)}")

        statistics = library_preferences.get("statistics")
        if statistics and isinstance(statistics, list):
            library_hints.append(f"统计分析优先使用: {', '.join(statistics)}")

        library_preference_text = "\n".join(library_hints) if library_hints else "使用pandas、numpy、matplotlib、seaborn、plotly、scikit-learn等常用库"

        system_prompt = self._create_system_prompt(
            "Python数据分析专家",
            f"""
你是一个专业的Python数据分析专家。请生成具体的Python代码来完成任务。

当前是第{attempt + 1}次尝试，请使用{complexity_level}复杂度的代码。

库偏好设置：
{library_preference_text}

代码要求：
1. 根据用户的库偏好选择合适的库
2. 代码应该是完整的、可执行的
3. 包含必要的导入语句
4. 添加适当的注释
5. 处理可能的异常情况
6. 生成有意义的输出和可视化
7. 不要使用网格搜索方式来寻找模型最优参数

图表生成指导：
- 使用matplotlib时：直接使用plt.show()即可自动保存图表
- 使用plotly时：直接使用display(fig)即可自动保存图表，系统会自动转换为PNG格式
- 使用seaborn时：配合matplotlib使用plt.show()
- **将图表中数据信息内容以清晰的格式全部print出来**，所有可视化图表必须要有，方便后面做数据洞察
- 确保图表有清晰的标题、轴标签和图例

复杂度指导：
- 高级：可以使用复杂的算法和高级功能
- 中等：使用标准的分析方法，避免过于复杂的操作
- 简单：使用最基础的方法，确保代码稳定可执行

请只返回Python代码，不要包含其他解释。
            """
        )

        print(f"[DEBUG] 准备调用 _build_context")
        try:
            context = self._build_context(state, step_info, attempt)
            print(f"[DEBUG] _build_context 成功完成")
        except Exception as e:
            print(f"[DEBUG] _build_context 出错: {e}")
            print(f"[DEBUG] 错误类型: {type(e)}")
            import traceback
            print(f"[DEBUG] 错误堆栈: {traceback.format_exc()}")
            raise

        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"""
任务上下文：{context}

请生成Python代码：
            """)
        ]

        response = await self.llm.ainvoke(messages)
        code = response.content.strip()

        # 清理代码中的markdown格式标记
        code = self._clean_code(code)
        return code
    
    def _build_context(self, state: AnalysisState, step_info: Dict[str, Any], attempt: int = 0) -> str:
        """构建上下文信息"""
        print(f"[DEBUG] _build_context 开始")
        print(f"[DEBUG] state 类型: {type(state)}")
        print(f"[DEBUG] state 是否为 None: {state is None}")

        if state is None:
            raise ValueError("state 不能为 None")

        print(f"[DEBUG] state keys: {list(state.keys())}")
        print(f"[DEBUG] step_info: {step_info}")
        print(f"[DEBUG] step_info 类型: {type(step_info)}")

        # 检查 step_info 是否为 None
        if not step_info:
            raise ValueError("step_info 不能为 None")

        context_parts = [
            f"任务目标: {step_info.get('objective', '未知目标')}",
            f"任务描述: {step_info.get('description', '')}",
            f"数据文件路径: {state.get('dataframe_path', '')}",
        ]

        # 添加数据摘要
        data_summary = state.get("data_summary")
        print(f"[DEBUG] data_summary: {data_summary}")
        if data_summary:
            print(f"[DEBUG] data_summary type: {type(data_summary)}")
            shape = data_summary.get('shape', '未知')
            print(f"[DEBUG] shape: {shape}")
            context_parts.append(f"数据形状: {shape}")

            columns = data_summary.get('columns', [])
            print(f"[DEBUG] columns: {columns}, type: {type(columns)}")
            if columns and isinstance(columns, list):
                context_parts.append(f"列名: {', '.join(columns)}")
            else:
                context_parts.append(f"列名: 未知")

        # 添加之前的执行结果
        executed_steps = state.get("executed_steps", [])
        if executed_steps:
            context_parts.append("\n之前的执行结果:")
            for i, step in enumerate(executed_steps[-3:]):  # 只显示最近3个步骤
                if step:  # 确保 step 不为 None
                    context_parts.append(f"步骤{i+1}: {step.get('step_name', '')} - {step.get('status', '')}")

        # 如果是重试，添加错误信息和纠错指导
        if attempt > 0:
            context_parts.append(f"\n这是第{attempt + 1}次尝试，之前的代码执行失败。")

            # 获取上一次的错误信息（如果有的话）
            last_error = state.get("last_execution_error")
            if last_error:
                context_parts.append(f"上次错误信息: {last_error}")

            # 根据尝试次数提供不同的纠错策略
            if attempt == 1:
                context_parts.append("请简化代码逻辑，避免复杂的操作，确保代码稳定性。")
            elif attempt == 2:
                context_parts.append("请使用最基础的方法，优先保证代码能够成功执行。")

        return "\n".join(context_parts)

    def _clean_code(self, code: str) -> str:
        """清理代码中的markdown格式标记"""
        import re

        # 移除markdown代码块标记
        # 匹配 ```python 或 ``` 开头和结尾的标记
        code = re.sub(r'^```(?:python)?\s*\n', '', code, flags=re.MULTILINE)
        code = re.sub(r'\n```\s*$', '', code, flags=re.MULTILINE)
        code = re.sub(r'^```(?:python)?\s*', '', code)
        code = re.sub(r'```\s*$', '', code)

        # 移除可能的额外空行
        lines = code.split('\n')
        # 移除开头和结尾的空行
        while lines and not lines[0].strip():
            lines.pop(0)
        while lines and not lines[-1].strip():
            lines.pop()

        return '\n'.join(lines)

    async def _generate_insights(
        self,
        state: AnalysisState,
        step_info: Dict[str, Any],
        code: str,
        execution_result
    ) -> list:
        """生成分析洞察"""
        system_prompt = self._create_system_prompt(
            "数据分析洞察专家",
            """
基于执行的代码和结果，生成有价值的分析洞察。

洞察应该包括：
1. 数据的关键发现
2. 模式和趋势
3. 异常或有趣的观察

请提供至少5个具体的洞察点，每个洞察应该清晰、具体且有价值。
            """
        )

        result_summary = f"""
任务目标: {step_info['objective']}

执行代码：
{code}

执行结果：
- 成功: {execution_result.success}
- 输出: {execution_result.stdout or '无输出'}
- 执行时间: {execution_result.execution_time:.2f}秒
- 生成图表: {len(execution_result.plots)}个
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"请基于以下执行结果生成洞察：\n\n{result_summary}")
        ]

        response = await self.llm.ainvoke(messages)

        # 直接返回洞察文本，不分割成列表
        insights_text = response.content.strip()
        return insights_text  # 返回完整的洞察文本

    async def _log_to_chroma(
        self,
        state: AnalysisState,
        step_info: Dict[str, Any],
        code: str,
        execution_result,
        insights: list
    ) -> None:
        """记录执行信息到ChromaDB"""
        try:
            collection_name = generate_collection_name(state["task_id"])
            current_step = state.get("current_step", 0)

            # 记录代码
            chroma_client.add_document(collection_name, {
                "task_id": state["task_id"],
                "step": current_step,
                "type": "python_code",
                "content": code,
                "metadata": {
                    "objective": step_info["objective"],
                    "success": execution_result.success,
                    "execution_time": execution_result.execution_time
                },
                "timestamp": datetime.now()
            })

            # 记录执行结果
            result_content = f"成功: {execution_result.success}\n"
            if execution_result.stdout:
                result_content += f"输出: {execution_result.stdout}\n"
            if execution_result.stderr:
                result_content += f"错误: {execution_result.stderr}\n"

            chroma_client.add_document(collection_name, {
                "task_id": state["task_id"],
                "step": current_step,
                "type": "execution_result",
                "content": result_content,
                "metadata": {
                    "success": execution_result.success,
                    "execution_time": execution_result.execution_time,
                    "plots_count": len(execution_result.plots)
                },
                "timestamp": datetime.now()
            })

            # 记录洞察（如果有的话）
            if insights:
                chroma_client.add_document(collection_name, {
                    "task_id": state["task_id"],
                    "step": current_step,
                    "type": "insights",
                    "content": insights,  # insights现在是字符串
                    "metadata": {
                        "objective": step_info["objective"],
                        "insights_length": len(insights)
                    },
                    "timestamp": datetime.now()
                })

        except Exception as e:
            self._log_execution("ChromaDB记录失败", str(e))

    async def _log_to_chroma(
        self,
        state: AnalysisState,
        step_info: Dict[str, Any],
        code: str,
        execution_result,
        insights: list
    ) -> None:
        """记录执行信息到ChromaDB"""
        try:
            collection_name = generate_collection_name(state["task_id"])
            current_step = state.get("current_step", 0)

            # 记录代码
            chroma_client.add_document(collection_name, {
                "task_id": state["task_id"],
                "step": current_step,
                "type": "python_code",
                "content": code,
                "metadata": {
                    "objective": step_info["objective"],
                    "success": execution_result.success,
                    "execution_time": execution_result.execution_time
                },
                "timestamp": datetime.now()
            })

            # 记录执行结果
            result_content = f"成功: {execution_result.success}\n"
            if execution_result.stdout:
                result_content += f"输出: {execution_result.stdout}\n"
            if execution_result.stderr:
                result_content += f"错误: {execution_result.stderr}\n"

            chroma_client.add_document(collection_name, {
                "task_id": state["task_id"],
                "step": current_step,
                "type": "execution_result",
                "content": result_content,
                "metadata": {
                    "success": execution_result.success,
                    "execution_time": execution_result.execution_time,
                    "plots_count": len(execution_result.plots)
                },
                "timestamp": datetime.now()
            })

            # 记录洞察（如果有的话）
            if insights:
                insights_content = "\n".join(insights)
                chroma_client.add_document(collection_name, {
                    "task_id": state["task_id"],
                    "step": current_step,
                    "type": "insights",
                    "content": insights_content,
                    "metadata": {
                        "objective": step_info["objective"],
                        "insights_count": len(insights)
                    },
                    "timestamp": datetime.now()
                })

        except Exception as e:
            self._log_execution("ChromaDB记录失败", str(e))
