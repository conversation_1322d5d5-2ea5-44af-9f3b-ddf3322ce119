
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom animations for SimulationDemo */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes slide-in-top {
  from { transform: translateY(-50px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

@keyframes slide-in-left {
  from { transform: translateX(-50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

@keyframes slide-in-right {
  from { transform: translateX(50px); opacity: 0; }
  to { transform: translateX(0); opacity: 1; }
}

.animate-slide-in-top { animation: slide-in-top 0.6s ease-out; }
.animate-slide-in-left { animation: slide-in-left 0.6s ease-out; }
.animate-slide-in-right { animation: slide-in-right 0.6s ease-out; }
.animate-float { animation: float 3s ease-in-out infinite; }
.animation-delay-200 { animation-delay: 0.2s; }
.animation-delay-300 { animation-delay: 0.3s; }
.animation-delay-500 { animation-delay: 0.5s; }
.animation-delay-600 { animation-delay: 0.6s; }
.animation-delay-900 { animation-delay: 0.9s; }

/* AI Data Analysis Platform Design System - Modern & Professional */

@layer base {
  :root {
    /* Light mode colors */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;

    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;

    /* Primary - Electric blue for AI/tech feel */
    --primary: 217 91% 60%;
    --primary-foreground: 0 0% 98%;
    --primary-glow: 217 91% 70%;

    /* Secondary - Professional slate */
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;

    /* Muted - Subtle grays */
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;

    /* Accent - Vibrant purple for highlights */
    --accent: 270 95% 75%;
    --accent-foreground: 240 10% 3.9%;

    /* Status colors */
    --success: 142 76% 36%;
    --success-foreground: 0 0% 98%;
    --warning: 38 92% 50%;
    --warning-foreground: 0 0% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    /* Workflow node states */
    --node-pending: 240 5% 64.9%;
    --node-running: 217 91% 60%;
    --node-success: 142 76% 36%;
    --node-warning: 38 92% 50%;
    --node-error: 0 84.2% 60.2%;

    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 217 91% 60%;

    --radius: 0.75rem;

    /* Gradients for visual appeal */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary-glow)));
    --gradient-accent: linear-gradient(135deg, hsl(var(--accent)), hsl(var(--primary)));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 76% 46%));
    
    /* Shadows for depth */
    --shadow-elegant: 0 10px 40px -15px hsl(var(--primary) / 0.3);
    --shadow-card: 0 4px 24px -6px hsl(0 0% 0% / 0.1);
    --shadow-glow: 0 0 40px hsl(var(--primary-glow) / 0.4);
    
    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-fast: all 0.15s ease-out;
  }

  .dark {
    /* Dark mode colors */
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;

    --card: 240 10% 4.5%;
    --card-foreground: 0 0% 98%;

    --popover: 240 10% 4.5%;
    --popover-foreground: 0 0% 98%;

    --secondary: 240 6% 10%;
    --secondary-foreground: 0 0% 98%;

    --muted: 240 6% 10%;
    --muted-foreground: 240 5% 64.9%;

    --accent: 270 95% 75%;
    --accent-foreground: 240 10% 3.9%;

    --border: 240 6% 20%;
    --input: 240 6% 10%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }
}

@layer components {
  /* Workflow Node Styles */
  .workflow-node {
    @apply relative p-4 rounded-xl border border-border bg-card transition-all duration-300;
    box-shadow: var(--shadow-card);
  }
  
  .workflow-node.pending {
    @apply border-muted-foreground bg-muted/30;
  }
  
  .workflow-node.running {
    @apply border-primary bg-primary/10;
    box-shadow: var(--shadow-glow);
    animation: pulse-glow 2s infinite;
  }
  
  .workflow-node.success {
    @apply border-success bg-success/10;
  }
  
  .workflow-node.warning {
    @apply border-warning bg-warning/10;
  }
  
  .workflow-node.error {
    @apply border-destructive bg-destructive/10;
  }

  /* Card Variants */
  .result-card {
    @apply bg-card border border-border rounded-xl p-6 mb-6 transition-all duration-200;
    box-shadow: var(--shadow-card);
  }
  
  .result-card:hover {
    @apply border-border/80;
    transform: translateY(-2px);
    box-shadow: var(--shadow-elegant);
  }

  /* Gradient Buttons */
  .btn-gradient-primary {
    background: var(--gradient-primary);
    @apply text-primary-foreground border-0 font-medium;
    transition: var(--transition-smooth);
  }
  
  .btn-gradient-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-glow);
  }

  /* Code Block Styling */
  .code-block {
    @apply bg-secondary rounded-lg p-4 font-mono text-sm border border-border;
  }

  /* Status Indicators */
  .status-indicator {
    @apply w-3 h-3 rounded-full;
  }
  
  .status-indicator.pending {
    @apply bg-muted-foreground;
    animation: pulse 2s infinite;
  }
  
  .status-indicator.running {
    @apply bg-primary;
    animation: pulse-glow 1.5s infinite;
  }
  
  .status-indicator.success {
    @apply bg-success;
  }
  
  .status-indicator.warning {
    @apply bg-warning;
  }
  
  .status-indicator.error {
    @apply bg-destructive;
  }
}

@layer utilities {
  /* Custom animations */
  @keyframes pulse-glow {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.02);
    }
  }
  
  @keyframes slide-in-right {
    from {
      opacity: 0;
      transform: translateX(20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes fade-in-up {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  @keyframes data-flow {
    0% {
      opacity: 0;
      transform: translateX(-20px) scale(0.8);
    }
    50% {
      opacity: 1;
      transform: translateX(0) scale(1);
    }
    100% {
      opacity: 0;
      transform: translateX(20px) scale(0.8);
    }
  }

  @keyframes chart-grow {
    from {
      height: 0;
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes processing-glow {
    0%, 100% {
      box-shadow: 0 0 20px hsl(var(--primary) / 0.3);
    }
    50% {
      box-shadow: 0 0 40px hsl(var(--primary) / 0.6);
    }
  }

  @keyframes shimmer-wave {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  @keyframes loading-dots {
    0%, 20% {
      opacity: 0;
      transform: scale(0.8);
    }
    50% {
      opacity: 1;
      transform: scale(1);
    }
    80%, 100% {
      opacity: 0;
      transform: scale(0.8);
    }
  }

  @keyframes gradient-shift {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes orbit {
    0% {
      transform: rotate(0deg) translateX(20px) rotate(0deg);
    }
    100% {
      transform: rotate(360deg) translateX(20px) rotate(-360deg);
    }
  }

  .animate-slide-in-right {
    animation: slide-in-right 0.3s ease-out;
  }
  
  .animate-fade-in-up {
    animation: fade-in-up 0.4s ease-out;
  }

  .animate-data-flow {
    animation: data-flow 2s ease-in-out infinite;
  }

  .animate-chart-grow {
    animation: chart-grow 0.6s ease-out;
  }

  .animate-processing-glow {
    animation: processing-glow 2s ease-in-out infinite;
  }

  .animate-shimmer {
    animation: shimmer-wave 2s ease-in-out infinite;
  }

  .animate-loading-dots {
    animation: loading-dots 1.5s ease-in-out infinite;
  }

  .animate-gradient-shift {
    animation: gradient-shift 3s ease-in-out infinite;
    background-size: 200% 200%;
  }

  .animate-orbit {
    animation: orbit 3s linear infinite;
  }

  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced loading states */
  .loading-skeleton {
    background: linear-gradient(90deg, hsl(var(--muted)) 25%, hsl(var(--muted-foreground) / 0.1) 50%, hsl(var(--muted)) 75%);
    background-size: 200% 100%;
    animation: shimmer-wave 1.5s infinite;
  }

  .processing-indicator {
    position: relative;
    overflow: hidden;
  }

  .processing-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.3), transparent);
    animation: shimmer-wave 2s infinite;
  }
}
