#!/usr/bin/env python3
"""测试ReflectionAgent是否正常工作"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.agents.reflection_agent import ReflectionAgent
from src.models.state_models import AnalysisState
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def test_reflection_agent():
    """测试ReflectionAgent的各个功能"""
    print("🧠 测试ReflectionAgent功能...")
    
    # 创建测试状态
    test_state = AnalysisState(
        task_id=f"test_reflection_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        original_query="分析客户满意度调查数据，识别影响满意度的关键因素",
        dataframe_path="test_satisfaction_survey.csv",
        plan={
            "steps": [
                {"step": 1, "objective": "数据清洗", "description": "处理调查数据的缺失值和异常值"},
                {"step": 2, "objective": "满意度分析", "description": "分析各维度满意度分布"},
                {"step": 3, "objective": "影响因素分析", "description": "识别影响满意度的关键因素"},
                {"step": 4, "objective": "建议制定", "description": "基于分析结果制定改进建议"}
            ]
        },
        current_step=4,
        executed_steps=[
            {
                "step": 1,
                "code": "df.dropna(subset=['satisfaction_score'], inplace=True)\ndf = df[df['satisfaction_score'].between(1, 5)]",
                "result": "数据清洗完成，保留了4500条有效记录",
                "insights": ["数据质量良好，满意度评分集中在3-4分"]
            },
            {
                "step": 2,
                "code": "satisfaction_dist = df['satisfaction_score'].value_counts().sort_index()\nsatisfaction_dist.plot(kind='bar')",
                "result": "生成满意度分布图",
                "insights": ["满意度呈正态分布，平均分为3.2分"]
            },
            {
                "step": 3,
                "code": "correlation_matrix = df.corr()\nsns.heatmap(correlation_matrix, annot=True)",
                "result": "生成相关性分析热力图",
                "insights": ["服务质量与满意度相关性最强(r=0.78)"]
            },
            {
                "step": 4,
                "code": "recommendations = analyze_improvement_areas(df)\nprint(recommendations)",
                "result": "生成改进建议报告",
                "insights": ["建议重点改进服务响应速度和产品质量"]
            }
        ],
        chroma_collection_name="test_reflection",
        errors=[],
        insights=["客户满意度分析完成，识别出关键改进领域"],
        data_summary={
            "shape": [4500, 15],
            "columns": ["customer_id", "age", "gender", "region", "service_quality", "product_quality", "price_satisfaction", "response_time", "satisfaction_score", "recommend_likelihood", "complaint_count", "purchase_frequency", "tenure_months", "support_interactions", "feedback_category"],
            "dtypes": {
                "customer_id": "int64",
                "age": "int64",
                "gender": "object",
                "region": "object",
                "service_quality": "float64",
                "product_quality": "float64",
                "price_satisfaction": "float64",
                "response_time": "float64",
                "satisfaction_score": "float64",
                "recommend_likelihood": "float64",
                "complaint_count": "int64",
                "purchase_frequency": "float64",
                "tenure_months": "int64",
                "support_interactions": "int64",
                "feedback_category": "object"
            }
        },
        final_report={
            "title_and_abstract": "客户满意度调查分析报告\n\n本研究基于4500份客户满意度调查数据，分析了影响客户满意度的关键因素。",
            "introduction": "客户满意度是企业成功的重要指标。本研究旨在识别影响满意度的关键因素。",
            "data_description": "数据集包含客户基本信息、服务体验评价和满意度评分等15个维度。",
            "exploratory_analysis": "探索性分析发现满意度评分呈正态分布，平均分为3.2分。",
            "modeling_and_results": "相关性分析显示服务质量与满意度相关性最强。",
            "discussion": "分析结果表明服务质量是影响客户满意度的最重要因素。",
            "conclusion": "建议企业重点改进服务响应速度和产品质量以提升客户满意度。"
        }
    )
    
    # 创建反思智能体
    reflection_agent = ReflectionAgent()
    
    try:
        print(f"📊 任务ID: {test_state['task_id']}")
        print("=" * 60)
        
        # 测试1: 收集分析上下文
        print("🔍 测试1: 收集分析上下文")
        context = await reflection_agent._collect_analysis_context(test_state)
        print(f"✅ 上下文收集成功")
        print(f"  - 原始查询: {context.get('original_query', 'N/A')[:50]}...")
        print(f"  - 执行步骤数: {len(context.get('executed_steps', []))}")
        print(f"  - 生成图表数: {len(context.get('generated_images', []))}")
        print()
        
        # 测试2: 评估报告质量
        print("🔍 测试2: 评估报告质量")
        evaluation_result = await reflection_agent._evaluate_report(test_state, context)
        
        # 检查评估结果
        if evaluation_result.get("critical_issues") == ["评估结果解析失败"]:
            print("❌ 评估结果JSON解析失败")
            print("📋 原始响应可能包含无效的JSON格式")
            print("💡 建议检查提示词中的JSON格式模板")
        else:
            print("✅ 评估结果解析成功")
            print(f"  - 总体评分: {evaluation_result.get('overall_score', 'N/A')}/10")
            print(f"  - 维度评分数: {len(evaluation_result.get('dimension_scores', {}))}")
            print(f"  - 关键问题数: {len(evaluation_result.get('critical_issues', []))}")
            print(f"  - 改进优先级数: {len(evaluation_result.get('improvement_priorities', []))}")
        print()
        
        # 测试3: 生成反思
        print("🔍 测试3: 生成反思和改进建议")
        reflection_result = await reflection_agent._generate_reflection(test_state, context, evaluation_result)
        
        # 检查反思结果
        if not reflection_result.get("improvement_strategies"):
            print("❌ 反思结果可能解析失败")
            print("📋 improvement_strategies字段为空")
        else:
            print("✅ 反思结果解析成功")
            print(f"  - 根因分析数: {len(reflection_result.get('root_cause_analysis', {}))}")
            print(f"  - 改进策略数: {len(reflection_result.get('improvement_strategies', {}))}")
            print(f"  - 重写优先级数: {len(reflection_result.get('rewrite_priorities', []))}")
            print(f"  - 具体行动数: {len(reflection_result.get('specific_actions', []))}")
        print()
        
        # 测试4: 完整执行流程
        print("🔍 测试4: 完整执行反思审查流程")
        full_result = await reflection_agent.execute(test_state)
        
        print("✅ 完整流程执行完成")
        print(f"  - 反思评估: {'✅' if 'reflection_evaluation' in full_result else '❌'}")
        print(f"  - 反思洞察: {'✅' if 'reflection_insights' in full_result else '❌'}")
        print(f"  - 改进报告: {'✅' if 'improved_report_path' in full_result else '❌'}")
        
        if full_result.get('improved_report_path'):
            report_path = Path(full_result['improved_report_path'])
            if report_path.exists():
                print(f"  - 报告文件: ✅ 已生成 ({report_path.name})")
            else:
                print(f"  - 报告文件: ❌ 文件不存在")
        
        print()
        print("=" * 60)
        print("📊 测试结果总结:")
        
        # 总结测试结果
        tests_passed = 0
        total_tests = 4
        
        if context:
            tests_passed += 1
            print("✅ 上下文收集: 通过")
        else:
            print("❌ 上下文收集: 失败")
        
        if evaluation_result.get("critical_issues") != ["评估结果解析失败"]:
            tests_passed += 1
            print("✅ 评估功能: 通过")
        else:
            print("❌ 评估功能: JSON解析失败")
        
        if reflection_result.get("improvement_strategies"):
            tests_passed += 1
            print("✅ 反思功能: 通过")
        else:
            print("❌ 反思功能: 可能存在问题")
        
        if full_result.get('improved_report_path'):
            tests_passed += 1
            print("✅ 完整流程: 通过")
        else:
            print("❌ 完整流程: 存在问题")
        
        print(f"\n🎯 测试通过率: {tests_passed}/{total_tests} ({tests_passed/total_tests*100:.1f}%)")
        
        if tests_passed == total_tests:
            print("🎉 ReflectionAgent工作正常!")
            return True
        else:
            print("⚠️  ReflectionAgent存在问题，需要修复")
            return False
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        logger.error(f"ReflectionAgent测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始ReflectionAgent功能测试\n")
    
    success = await test_reflection_agent()
    
    if success:
        print("\n✅ 所有测试通过，ReflectionAgent工作正常!")
        return 0
    else:
        print("\n❌ 测试发现问题，请检查ReflectionAgent实现")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
