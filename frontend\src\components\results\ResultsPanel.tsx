import { <PERSON>roll<PERSON><PERSON> } from '@/components/ui/scroll-area';
import { ResultCard } from '@/types/analysis';
import { DataSummaryCard } from './cards/DataSummaryCard';
import { PlanCard, PlanApprovalCard } from './cards/PlanCard';
import { CodeCard } from './cards/CodeCard';
import { ExecutionResultCard } from './cards/ExecutionResultCard';
import { ErrorCard } from './cards/ErrorCard';
import { InsightCard } from './cards/InsightCard';
import { ReportCard } from './cards/ReportCard';
import { ReportGenerationCard } from './cards/ReportGenerationCard';
import { ReflectionCard } from './cards/ReflectionCard';
import { StepProgressCard } from './cards/StepProgressCard';
import { StepExecutionCard } from './cards/StepExecutionCard';
import ErrorBoundary from '@/components/ErrorBoundary';
import FileBrowserButton from '@/components/files/FileBrowserButton';
import { BarChart3, Zap, Goal, CheckCircle } from 'lucide-react';

interface ResultsPanelProps {
  cards: ResultCard[];
  selectedNodeId?: string;
  currentTaskId?: string;
}

export function ResultsPanel({ cards, selectedNodeId, currentTaskId }: ResultsPanelProps) {
  const renderCard = (card: ResultCard) => {
    const commonProps = {
      card,
      isHighlighted: selectedNodeId === card.node_id
    };

    const CardComponent = () => {
      switch (card.type) {
        case 'summary':
          return <DataSummaryCard {...commonProps} />;
        case 'plan':
          return <PlanCard {...commonProps} />;
        case 'code':
          return <CodeCard {...commonProps} />;
        case 'execution':
          return <ExecutionResultCard {...commonProps} />;
        case 'error':
          return <ErrorCard {...commonProps} />;
        case 'insight':
          return <InsightCard {...commonProps} />;
        case 'report':
          return <ReportCard {...commonProps} />;
        case 'report_generation':
          return <ReportGenerationCard taskId={currentTaskId || ''} isHighlighted={commonProps.isHighlighted} />;
        case 'reflection':
          return <ReflectionCard card={card} taskId={currentTaskId || ''} isHighlighted={commonProps.isHighlighted} />;
        case 'step_progress':
          return <StepProgressCard {...commonProps} />;
        case 'step_execution':
          return <StepExecutionCard {...commonProps} />;
        case 'plan_approval':
          return <PlanApprovalCard {...commonProps} taskId={currentTaskId || ''} />;
        default:
          return null;
      }
    };

    return (
      <ErrorBoundary key={card.id}>
        <CardComponent />
      </ErrorBoundary>
    );
  };

  // Filter cards based on selected node
  const filteredCards = selectedNodeId
    ? cards.filter(card => card.node_id === selectedNodeId)
    : cards;

  // 添加调试日志
  console.log('[DEBUG] ResultsPanel - selectedNodeId:', selectedNodeId);
  console.log('[DEBUG] ResultsPanel - 总卡片数:', cards.length);
  console.log('[DEBUG] ResultsPanel - 过滤后卡片数:', filteredCards.length);
  console.log('[DEBUG] ResultsPanel - 所有卡片:', cards.map(card => ({
    id: card.id,
    node_id: card.node_id,
    type: card.type,
    title: card.title,
    hasContent: !!card.content
  })));
  console.log('[DEBUG] ResultsPanel - 过滤后卡片:', filteredCards.map(card => ({
    id: card.id,
    node_id: card.node_id,
    type: card.type,
    title: card.title,
    hasContent: !!card.content,
    contentKeys: card.content ? Object.keys(card.content) : []
  })));

  // 特别检查 step_execution 类型的卡片
  const stepExecutionCards = cards.filter(card => card.type === 'step_execution');
  if (stepExecutionCards.length > 0) {
    console.log('[DEBUG] ResultsPanel - step_execution 卡片详情:', stepExecutionCards.map(card => ({
      id: card.id,
      node_id: card.node_id,
      isSelected: card.node_id === selectedNodeId,
      content: card.content
    })));
  }

  if (filteredCards.length === 0 && selectedNodeId) {
    // 检查是否是"完成检查"节点
    const isCompletionCheck = selectedNodeId === 'check_completion';

    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center mt-16">
          {isCompletionCheck ? (
            /* 完成检查节点的特殊动画 */
            <>
              <div className="relative w-32 h-32 mx-auto mb-6">
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-success/20 to-primary/20 animate-pulse" />
                <div className="absolute inset-2 rounded-full bg-gradient-to-br from-success/30 to-primary/30 animate-ping" />
                <div className="absolute inset-4 rounded-full bg-gradient-to-br from-success to-primary flex items-center justify-center">
                  <Goal className="w-8 h-8 text-white animate-bounce" />
                </div>
                {/* 成功完成的环形动画 */}
                <div className="absolute inset-0 animate-spin" style={{ animationDuration: '3s' }}>
                  <div className="absolute top-0 left-1/2 w-3 h-3 bg-success rounded-full transform -translate-x-1/2">
                    <CheckCircle className="w-3 h-3 text-white" />
                  </div>
                  <div className="absolute bottom-0 left-1/2 w-3 h-3 bg-success rounded-full transform -translate-x-1/2">
                    <CheckCircle className="w-3 h-3 text-white" />
                  </div>
                  <div className="absolute left-0 top-1/2 w-3 h-3 bg-success rounded-full transform -translate-y-1/2">
                    <CheckCircle className="w-3 h-3 text-white" />
                  </div>
                  <div className="absolute right-0 top-1/2 w-3 h-3 bg-success rounded-full transform -translate-y-1/2">
                    <CheckCircle className="w-3 h-3 text-white" />
                  </div>
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 bg-gradient-to-r from-success to-primary bg-clip-text text-transparent">
                智能体分析流程已结束
              </h3>
              <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
                请查阅报告文档
              </p>
              {/* 完成状态的脉冲动画 */}
              <div className="flex justify-center gap-2 mt-4">
                <div className="w-3 h-3 bg-success rounded-full animate-pulse" />
                <div className="w-3 h-3 bg-primary rounded-full animate-pulse" style={{ animationDelay: '0.5s' }} />
                <div className="w-3 h-3 bg-success rounded-full animate-pulse" style={{ animationDelay: '1s' }} />
              </div>
            </>
          ) : (
            /* 默认的处理中动画 */
            <>
              <div className="relative w-32 h-32 mx-auto mb-6">
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-accent/20 animate-pulse" />
                <div className="absolute inset-2 rounded-full bg-gradient-to-br from-primary/30 to-accent/30 animate-ping" />
                <div className="absolute inset-4 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center">
                  <BarChart3 className="w-8 h-8 text-white animate-bounce" />
                </div>
                {/* Orbiting dots */}
                <div className="absolute inset-0 animate-spin">
                  <div className="absolute top-0 left-1/2 w-2 h-2 bg-primary rounded-full transform -translate-x-1/2" />
                  <div className="absolute bottom-0 left-1/2 w-2 h-2 bg-accent rounded-full transform -translate-x-1/2" />
                  <div className="absolute left-0 top-1/2 w-2 h-2 bg-primary rounded-full transform -translate-y-1/2" />
                  <div className="absolute right-0 top-1/2 w-2 h-2 bg-accent rounded-full transform -translate-y-1/2" />
                </div>
              </div>
              <h3 className="text-xl font-bold mb-3 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
                正在处理数据...
              </h3>
              <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
                AI正在分析您的数据，请稍候片刻
              </p>
              {/* Loading dots */}
              <div className="flex justify-center gap-1 mt-4">
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            </>
          )}
        </div>
      </div>
    );
  }

  if (cards.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="relative w-32 h-32 mx-auto mb-6">
            <div className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/10 to-accent/10" />
            <div className="absolute inset-4 rounded-full bg-gradient-to-br from-primary to-accent flex items-center justify-center">
              <Zap className="w-8 h-8 text-white" />
            </div>
          </div>
          <h3 className="text-xl font-bold mb-3 bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
            准备开始分析
          </h3>
          <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">
            上传您的数据，开始AI驱动的智能分析之旅
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative h-[calc(100vh-4rem)]">
      <ScrollArea className="h-full">
        <div className="p-6 space-y-6">
          {filteredCards.map(renderCard)}
        </div>
      </ScrollArea>

      {/* 文件浏览器按钮 - 固定在左下角 */}
      {currentTaskId && (
        <div className="absolute bottom-6 left-6 z-10">
          <FileBrowserButton taskId={currentTaskId} />
        </div>
      )}
    </div>
  );
}