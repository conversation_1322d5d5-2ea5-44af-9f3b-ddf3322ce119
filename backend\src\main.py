"""FastAPI主应用"""

import uvicorn
from fastapi import FastAPI, WebSocket
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
from src.config import settings
from src.api.routes import router
from src.api.websocket import websocket_endpoint
from src.utils.logger import get_logger
from src.execution.jupyter_executor import jupyter_executor

logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时的初始化
    logger.info("AI智能体数据分析平台后端服务启动中...")
    
    # 确保数据目录存在
    import os
    os.makedirs(settings.data_directory, exist_ok=True)
    os.makedirs(settings.upload_directory, exist_ok=True)
    os.makedirs(settings.results_directory, exist_ok=True)
    os.makedirs(settings.chroma_persist_directory, exist_ok=True)
    
    logger.info("数据目录初始化完成")
    
    yield
    
    # 关闭时的清理
    logger.info("正在关闭服务...")
    
    # 关闭所有Jupyter内核
    await jupyter_executor.shutdown_all_kernels()
    
    logger.info("服务已关闭")


# 创建FastAPI应用
app = FastAPI(
    title="AI智能体数据分析平台",
    description="基于LangGraph和ChromaDB的智能数据分析平台",
    version="0.1.0",
    lifespan=lifespan
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 临时允许所有来源，用于调试
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# 注册API路由
app.include_router(router, prefix="/api/v1", tags=["分析任务"])

# WebSocket端点
@app.websocket("/ws/{task_id}")
async def websocket_handler(websocket: WebSocket, task_id: str):
    """WebSocket连接处理"""
    await websocket_endpoint(websocket, task_id)

# 静态文件服务（用于提供生成的图表等）
try:
    app.mount("/static", StaticFiles(directory=settings.results_directory), name="static")
except Exception as e:
    logger.warning(f"静态文件服务配置失败: {str(e)}")

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI智能体数据分析平台后端服务",
        "version": "0.1.0",
        "status": "running"
    }

# 健康检查
@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "ai-agent-analysis-backend"
    }


def main():
    """主函数"""
    logger.info(f"启动服务器: {settings.api_host}:{settings.api_port}")
    
    uvicorn.run(
        "src.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload,
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
