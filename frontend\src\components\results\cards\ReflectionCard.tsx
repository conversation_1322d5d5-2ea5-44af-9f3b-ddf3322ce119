import { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { But<PERSON> } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css';
import './ReportGeneration.css';
import {
  Brain,
  Search,
  AlertTriangle,
  Lightbulb,
  RefreshCw,
  CheckCircle,
  ChevronDown,
  ChevronUp,
  FileText,
  Download,
  Git<PERSON>ompare,
  Eye
} from 'lucide-react';
import { ResultCard } from '@/types/analysis';

interface ReflectionCardProps {
  card?: ResultCard;
  taskId: string;
  isHighlighted?: boolean;
}

interface ReflectionState {
  phase: 'starting' | 'evaluating' | 'reflecting' | 'regenerating' | 'complete' | 'error';
  evaluation?: {
    overall_score: number;
    dimension_scores: Record<string, { score: number; issues: string[] }>;
    critical_issues: string[];
    improvement_priorities: string[];
  };
  reflection?: {
    root_cause_analysis: Record<string, string>;
    improvement_strategies: Record<string, string>;
    rewrite_priorities: string[];
    quality_enhancement_directions: string[];
    specific_actions: Array<{
      action: string;
      target_section: string;
      expected_improvement: string;
    }>;
  };
  regeneration_progress?: {
    current_section: string;
    completed_sections: string[];
  };
  streaming_sections?: Record<string, {
    name: string;
    content: string;
    status: 'generating' | 'complete';
  }>;
  improved_report_path?: string;
  error?: string;
  // 新增：用于版本对比
  original_report?: Record<string, string>;
  improved_report?: Record<string, string>;
}

export function ReflectionCard({ card, taskId, isHighlighted }: ReflectionCardProps) {
  const [reflectionState, setReflectionState] = useState<ReflectionState>({
    phase: 'starting'
  });
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [forceUpdate, setForceUpdate] = useState(0);
  const [showComparison, setShowComparison] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const contentEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = useCallback(() => {
    if (contentEndRef.current) {
      contentEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      });
    }
  }, []);

  // 获取初始报告内容
  const fetchOriginalReport = useCallback(async () => {
    if (!taskId) return null;

    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
      const response = await fetch(`${apiBaseUrl}/api/v1/tasks/${taskId}/report-status`);

      if (response.ok) {
        const status = await response.json();
        if (status.sections) {
          const originalReport: Record<string, string> = {};
          Object.entries(status.sections).forEach(([sectionId, sectionData]: [string, any]) => {
            if (sectionData.content) {
              originalReport[sectionId] = sectionData.content;
            }
          });
          return originalReport;
        }
      }
    } catch (error) {
      console.error('获取初始报告失败:', error);
    }
    return null;
  }, [taskId]);

  // 获取改进报告内容
  const fetchImprovedReport = useCallback(async () => {
    if (!taskId) return null;

    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
      const response = await fetch(`${apiBaseUrl}/api/v1/tasks/${taskId}/files/improved_report.json/content`);

      if (response.ok) {
        const content = await response.text();
        const improvedReport = JSON.parse(content);
        return improvedReport;
      }
    } catch (error) {
      console.error('获取改进报告失败:', error);
      // 如果获取失败，尝试从streaming_sections构建改进报告
      if (reflectionState.streaming_sections) {
        const improvedReport: Record<string, string> = {};
        Object.entries(reflectionState.streaming_sections).forEach(([sectionId, section]) => {
          if (section.content) {
            // 移除前缀，获取原始章节ID
            const originalSectionId = sectionId.replace(/^(improved_|refined_)/, '');
            improvedReport[originalSectionId] = section.content;
          }
        });
        return Object.keys(improvedReport).length > 0 ? improvedReport : null;
      }
    }
    return null;
  }, [taskId, reflectionState.streaming_sections]);

  // 状态恢复机制 - 从localStorage恢复反思状态
  useEffect(() => {
    if (!taskId) return;

    const storageKey = `reflection_state_${taskId}`;
    const savedState = localStorage.getItem(storageKey);

    if (savedState) {
      try {
        const parsedState = JSON.parse(savedState);
        console.log('ReflectionCard 恢复状态:', parsedState);
        setReflectionState(parsedState);
        setForceUpdate(prev => prev + 1);
      } catch (error) {
        console.error('恢复反思状态失败:', error);
      }
    }
  }, [taskId]);

  // 状态持久化 - 保存反思状态到localStorage
  useEffect(() => {
    if (!taskId) return;

    const storageKey = `reflection_state_${taskId}`;
    localStorage.setItem(storageKey, JSON.stringify(reflectionState));
  }, [taskId, reflectionState]);

  const handleReflectionUpdate = useCallback((data: any) => {
    console.log('ReflectionCard 处理消息:', data.type, data);

    switch (data.type) {
      case 'reflection_start':
        console.log('反思开始');
        setReflectionState(prev => ({ ...prev, phase: 'evaluating' }));
        break;

      case 'evaluation_result':
        console.log('评估结果:', data.evaluation);
        setReflectionState(prev => ({
          ...prev,
          phase: 'reflecting',
          evaluation: data.evaluation
        }));
        break;

      case 'reflection_result':
        console.log('反思结果:', data.reflection);
        setReflectionState(prev => ({
          ...prev,
          phase: 'regenerating',
          reflection: data.reflection
        }));
        break;

      case 'regeneration_start':
        console.log('开始重新生成');
        setReflectionState(prev => ({
          ...prev,
          phase: 'regenerating',
          regeneration_progress: { current_section: '', completed_sections: [] }
        }));
        break;

      case 'regeneration_progress':
        console.log('重新生成进度:', data.section_name);
        setReflectionState(prev => {
          const completed = prev.regeneration_progress?.completed_sections || [];
          // 如果当前部分不在已完成列表中，添加上一个部分到已完成
          if (prev.regeneration_progress?.current_section &&
              !completed.includes(prev.regeneration_progress.current_section)) {
            completed.push(prev.regeneration_progress.current_section);
          }

          return {
            ...prev,
            regeneration_progress: {
              current_section: data.section_name,
              completed_sections: completed
            }
          };
        });
        break;

      case 'reflection_complete':
        console.log('反思完成:', data.report_path);
        setReflectionState(prev => ({
          ...prev,
          phase: 'complete',
          improved_report_path: data.report_path
        }));

        // 获取初始报告和改进报告内容用于对比
        (async () => {
          const originalReport = await fetchOriginalReport();
          const improvedReport = await fetchImprovedReport();

          setReflectionState(prev => ({
            ...prev,
            original_report: originalReport || undefined,
            improved_report: improvedReport || undefined
          }));
        })();
        break;

      case 'error':
        console.log('反思错误:', data.error || data.message);
        setReflectionState(prev => ({
          ...prev,
          phase: 'error',
          error: data.error || data.message
        }));
        break;

      // 处理流式生成的章节内容
      case 'section_start':
        if (data.section_id && (data.section_id.startsWith('improved_') || data.section_id.startsWith('refined_'))) {
          console.log('章节开始生成:', data.section_name);
          setReflectionState(prev => ({
            ...prev,
            streaming_sections: {
              ...prev.streaming_sections,
              [data.section_id]: {
                name: data.section_name,
                content: '',
                status: 'generating'
              }
            }
          }));
        }
        break;

      case 'section_content':
        if (data.section_id && (data.section_id.startsWith('improved_') || data.section_id.startsWith('refined_'))) {
          console.log('章节内容更新:', data.section_id, data.content?.length);
          setReflectionState(prev => ({
            ...prev,
            streaming_sections: {
              ...prev.streaming_sections,
              [data.section_id]: {
                ...prev.streaming_sections?.[data.section_id],
                name: prev.streaming_sections?.[data.section_id]?.name || data.section_id,
                content: data.content || '',
                status: data.is_partial ? 'generating' : 'complete'
              }
            }
          }));
          // 自动滚动到底部
          setTimeout(scrollToBottom, 100);
        }
        break;

      case 'section_complete':
        if (data.section_id && (data.section_id.startsWith('improved_') || data.section_id.startsWith('refined_'))) {
          console.log('章节生成完成:', data.section_name);
          setReflectionState(prev => ({
            ...prev,
            streaming_sections: {
              ...prev.streaming_sections,
              [data.section_id]: {
                ...prev.streaming_sections?.[data.section_id],
                name: data.section_name,
                status: 'complete'
              }
            }
          }));
        }
        break;

      default:
        // 忽略其他类型的消息
        break;
    }
  }, [scrollToBottom, fetchOriginalReport, fetchImprovedReport]);

  // SSE连接监听反思过程
  useEffect(() => {
    if (!taskId) return;

    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
    const sseUrl = `${apiBaseUrl}/api/v1/tasks/${taskId}/report-stream`;

    console.log('ReflectionCard 连接SSE:', sseUrl);
    const eventSource = new EventSource(sseUrl);

    eventSource.onopen = () => {
      console.log('ReflectionCard SSE连接已建立');
    };

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        console.log('ReflectionCard 收到SSE消息:', data);
        handleReflectionUpdate(data);
      } catch (error) {
        console.error('解析反思SSE消息失败:', error, '原始数据:', event.data);
      }
    };

    eventSource.onerror = (error) => {
      console.error('反思SSE连接错误:', error);
      console.log('SSE readyState:', eventSource.readyState);
      setReflectionState(prev => ({ ...prev, phase: 'error', error: '连接中断' }));
    };

    return () => {
      console.log('ReflectionCard 关闭SSE连接');
      eventSource.close();
    };
  }, [taskId, handleReflectionUpdate]);

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: prev[section] === false ? true : false
    }));
  };

  // 获取章节名称
  const getSectionName = (sectionId: string): string => {
    const sectionNames: Record<string, string> = {
      'title_and_abstract': '标题与摘要',
      'introduction': '引言/背景',
      'data_description': '数据描述',
      'exploratory_analysis': '探索性分析',
      'modeling_and_results': '建模与结果',
      'discussion': '讨论',
      'conclusion': '结论'
    };
    return sectionNames[sectionId] || sectionId;
  };

  // 确保重要部分默认展开
  useEffect(() => {
    if (reflectionState.evaluation && expandedSections.evaluation === undefined) {
      setExpandedSections(prev => ({ ...prev, evaluation: true }));
    }
    if (reflectionState.reflection && expandedSections.reflection === undefined) {
      setExpandedSections(prev => ({ ...prev, reflection: true }));
    }
    if (reflectionState.streaming_sections && Object.keys(reflectionState.streaming_sections).length > 0 && expandedSections.streaming === undefined) {
      setExpandedSections(prev => ({ ...prev, streaming: true }));
    }
  }, [reflectionState.evaluation, reflectionState.reflection, reflectionState.streaming_sections, expandedSections]);

  const getPhaseIcon = () => {
    switch (reflectionState.phase) {
      case 'starting':
      case 'evaluating':
        return <Search className="w-5 h-5 text-blue-500 animate-pulse" />;
      case 'reflecting':
        return <Brain className="w-5 h-5 text-purple-500 animate-pulse" />;
      case 'regenerating':
        return <RefreshCw className="w-5 h-5 text-orange-500 animate-spin" />;
      case 'complete':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertTriangle className="w-5 h-5 text-red-500" />;
      default:
        return <Brain className="w-5 h-5 text-gray-500" />;
    }
  };

  const getPhaseText = () => {
    switch (reflectionState.phase) {
      case 'starting':
        return '启动反思审查';
      case 'evaluating':
        return '评估报告质量';
      case 'reflecting':
        return '生成改进建议';
      case 'regenerating':
        return '重新生成报告';
      case 'complete':
        return '反思审查完成';
      case 'error':
        return '反思审查失败';
      default:
        return '反思审查';
    }
  };

  const getProgress = () => {
    switch (reflectionState.phase) {
      case 'starting':
        return 10;
      case 'evaluating':
        return 25;
      case 'reflecting':
        return 50;
      case 'regenerating':
        return 75;
      case 'complete':
        return 100;
      case 'error':
        return 0;
      default:
        return 0;
    }
  };

  const downloadImprovedReport = async () => {
    if (!reflectionState.improved_report_path) return;
    
    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
      const downloadUrl = `${apiBaseUrl}/api/v1/tasks/${taskId}/download/improved_report.md`;
      
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = 'improved_report.md';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('下载改进报告失败:', error);
    }
  };

  return (
    <Card className={cn(
      "result-card animate-fade-in-up",
      isHighlighted && "ring-2 ring-primary"
    )}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getPhaseIcon()}
          报告反思审查
          <Badge variant="outline" className="ml-auto">
            Reflexion框架
          </Badge>
          {reflectionState.phase === 'complete' && reflectionState.original_report && reflectionState.improved_report && (
            <Dialog open={showComparison} onOpenChange={setShowComparison}>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm" className="ml-2">
                  <GitCompare className="w-4 h-4 mr-1" />
                  版本对比
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-7xl w-[95vw] h-[90vh] p-0">
                <DialogHeader className="p-6 pb-0">
                  <DialogTitle className="flex items-center gap-2">
                    <GitCompare className="w-5 h-5" />
                    报告版本对比
                  </DialogTitle>
                </DialogHeader>
                <div className="flex h-full p-6 pt-0 gap-4">
                  {/* 初始版本 */}
                  <div className="flex-1 flex flex-col">
                    <div className="flex items-center gap-2 mb-3 p-2 bg-blue-50 dark:bg-blue-900/20 rounded">
                      <Eye className="w-4 h-4 text-blue-600" />
                      <span className="font-medium text-blue-700 dark:text-blue-300">初始版本</span>
                    </div>
                    <ScrollArea className="flex-1 border rounded-md p-4">
                      <div className="space-y-6">
                        {Object.entries(reflectionState.original_report).map(([sectionId, content]) => (
                          <div key={sectionId} className="space-y-2">
                            <h3 className="text-sm font-semibold text-muted-foreground border-b pb-1">
                              {getSectionName(sectionId)}
                            </h3>
                            <div className="prose prose-sm dark:prose-invert max-w-none">
                              <ReactMarkdown
                                remarkPlugins={[remarkMath]}
                                rehypePlugins={[rehypeKatex]}
                              >
                                {content}
                              </ReactMarkdown>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>

                  {/* 改进版本 */}
                  <div className="flex-1 flex flex-col">
                    <div className="flex items-center gap-2 mb-3 p-2 bg-green-50 dark:bg-green-900/20 rounded">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <span className="font-medium text-green-700 dark:text-green-300">改进版本</span>
                    </div>
                    <ScrollArea className="flex-1 border rounded-md p-4">
                      <div className="space-y-6">
                        {Object.entries(reflectionState.improved_report).map(([sectionId, content]) => (
                          <div key={sectionId} className="space-y-2">
                            <h3 className="text-sm font-semibold text-muted-foreground border-b pb-1">
                              {getSectionName(sectionId)}
                            </h3>
                            <div className="prose prose-sm dark:prose-invert max-w-none">
                              <ReactMarkdown
                                remarkPlugins={[remarkMath]}
                                rehypePlugins={[rehypeKatex]}
                              >
                                {content}
                              </ReactMarkdown>
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          )}
        </CardTitle>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm text-muted-foreground">
            <span>{getPhaseText()}</span>
            <span>{getProgress()}%</span>
          </div>
          <Progress value={getProgress()} className="h-2" />
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 错误状态 */}
        {reflectionState.phase === 'error' && (
          <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <div className="flex items-center gap-2 text-red-700 dark:text-red-400">
              <AlertTriangle className="w-4 h-4" />
              <span className="font-medium">反思审查失败</span>
            </div>
            <p className="mt-2 text-sm text-red-600 dark:text-red-300">
              {reflectionState.error}
            </p>
          </div>
        )}

        {/* 评估结果 */}
        {reflectionState.evaluation && (
          <Collapsible
            open={expandedSections.evaluation !== false}
            onOpenChange={() => toggleSection('evaluation')}
          >
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <div className="flex items-center gap-2">
                  <Search className="w-4 h-4 text-blue-500" />
                  <span className="font-medium">报告质量评估结果</span>
                  <Badge variant="secondary">
                    总分: {reflectionState.evaluation.overall_score}/10
                  </Badge>
                </div>
                {expandedSections.evaluation !== false ?
                  <ChevronUp className="w-4 h-4" /> :
                  <ChevronDown className="w-4 h-4" />
                }
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3 space-y-4">
              {/* 总体评分显示 */}
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="font-medium text-blue-700 dark:text-blue-400">总体质量评分</span>
                  <div className="flex items-center gap-2">
                    <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {reflectionState.evaluation.overall_score}
                    </div>
                    <div className="text-sm text-blue-500">/10</div>
                  </div>
                </div>
                <Progress
                  value={reflectionState.evaluation.overall_score * 10}
                  className="h-2"
                />
              </div>

              {/* 关键问题 */}
              {reflectionState.evaluation.critical_issues && reflectionState.evaluation.critical_issues.length > 0 && (
                <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
                  <div className="flex items-center gap-2 text-orange-700 dark:text-orange-400 mb-3">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="font-medium">发现的关键问题 ({reflectionState.evaluation.critical_issues.length}个)</span>
                  </div>
                  <ul className="space-y-2 text-sm text-orange-600 dark:text-orange-300">
                    {reflectionState.evaluation.critical_issues.map((issue, index) => (
                      <li key={index} className="flex items-start gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <span className="text-orange-500 mt-1 font-bold">{index + 1}.</span>
                        <span>{issue}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 改进优先级 */}
              {reflectionState.evaluation.improvement_priorities && reflectionState.evaluation.improvement_priorities.length > 0 && (
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <div className="flex items-center gap-2 text-yellow-700 dark:text-yellow-400 mb-3">
                    <Lightbulb className="w-4 h-4" />
                    <span className="font-medium">改进优先级</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {reflectionState.evaluation.improvement_priorities.map((priority, index) => (
                      <Badge key={index} variant="outline" className="text-yellow-700 dark:text-yellow-400">
                        {priority}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* 维度评分 */}
              {reflectionState.evaluation.dimension_scores && Object.keys(reflectionState.evaluation.dimension_scores).length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium text-sm flex items-center gap-2">
                    <Search className="w-4 h-4" />
                    各维度详细评分
                  </h4>
                  <div className="grid gap-2">
                    {Object.entries(reflectionState.evaluation.dimension_scores).map(([dimension, data]) => (
                      <div key={dimension} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium text-sm">{dimension}</span>
                          <Badge variant={data.score >= 7 ? "default" : data.score >= 5 ? "secondary" : "destructive"}>
                            {data.score}/10
                          </Badge>
                        </div>
                        {data.issues && data.issues.length > 0 && (
                          <div className="mt-2">
                            <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">具体问题:</div>
                            <ul className="space-y-1">
                              {data.issues.map((issue, issueIndex) => (
                                <li key={issueIndex} className="text-xs text-gray-700 dark:text-gray-300 flex items-start gap-1">
                                  <span className="text-gray-400 mt-0.5">•</span>
                                  <span>{issue}</span>
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* 反思结果 */}
        {reflectionState.reflection && (
          <Collapsible
            open={expandedSections.reflection !== false}
            onOpenChange={() => toggleSection('reflection')}
          >
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <div className="flex items-center gap-2">
                  <Brain className="w-4 h-4 text-purple-500" />
                  <span className="font-medium">深度反思分析结果</span>
                  <Badge variant="outline" className="text-purple-600 dark:text-purple-400">
                    Reflexion框架
                  </Badge>
                </div>
                {expandedSections.reflection !== false ?
                  <ChevronUp className="w-4 h-4" /> :
                  <ChevronDown className="w-4 h-4" />
                }
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3 space-y-4">
              {/* 根因分析 */}
              {reflectionState.reflection.root_cause_analysis && Object.keys(reflectionState.reflection.root_cause_analysis).length > 0 && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-center gap-2 text-red-700 dark:text-red-400 mb-3">
                    <AlertTriangle className="w-4 h-4" />
                    <span className="font-medium">问题根因分析</span>
                  </div>
                  <div className="space-y-2 text-sm">
                    {Object.entries(reflectionState.reflection.root_cause_analysis).map(([problem, analysis]) => (
                      <div key={problem} className="p-3 bg-white dark:bg-gray-800 rounded border">
                        <div className="font-medium text-red-600 dark:text-red-400 mb-1">{problem}</div>
                        <div className="text-gray-700 dark:text-gray-300">{analysis}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 改进策略 */}
              {reflectionState.reflection.improvement_strategies && Object.keys(reflectionState.reflection.improvement_strategies).length > 0 && (
                <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex items-center gap-2 text-blue-700 dark:text-blue-400 mb-3">
                    <Lightbulb className="w-4 h-4" />
                    <span className="font-medium">智能改进策略 ({Object.keys(reflectionState.reflection.improvement_strategies).length}个)</span>
                  </div>
                  <div className="space-y-3 text-sm">
                    {Object.entries(reflectionState.reflection.improvement_strategies).map(([strategy, description]) => (
                      <div key={strategy} className="p-3 bg-white dark:bg-gray-800 rounded border">
                        <div className="font-medium text-blue-600 dark:text-blue-400 mb-2">{strategy}</div>
                        <div className="text-gray-700 dark:text-gray-300 leading-relaxed">{description}</div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 质量提升方向 */}
              {reflectionState.reflection.quality_enhancement_directions && reflectionState.reflection.quality_enhancement_directions.length > 0 && (
                <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
                  <div className="flex items-center gap-2 text-green-700 dark:text-green-400 mb-3">
                    <CheckCircle className="w-4 h-4" />
                    <span className="font-medium">质量提升方向</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {reflectionState.reflection.quality_enhancement_directions.map((direction, index) => (
                      <Badge key={index} variant="outline" className="text-green-700 dark:text-green-400 bg-white dark:bg-gray-800">
                        {direction}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* 重写优先级 */}
              {reflectionState.reflection.rewrite_priorities && reflectionState.reflection.rewrite_priorities.length > 0 && (
                <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <div className="flex items-center gap-2 text-yellow-700 dark:text-yellow-400 mb-3">
                    <RefreshCw className="w-4 h-4" />
                    <span className="font-medium">重写优先级排序</span>
                  </div>
                  <div className="grid gap-2">
                    {reflectionState.reflection.rewrite_priorities.map((priority, index) => (
                      <div key={index} className="flex items-center gap-2 p-2 bg-white dark:bg-gray-800 rounded border">
                        <Badge variant="outline" className="text-yellow-700 dark:text-yellow-400">
                          {index + 1}
                        </Badge>
                        <span className="text-sm">{priority}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* 具体改进行动 */}
              {reflectionState.reflection.specific_actions && reflectionState.reflection.specific_actions.length > 0 && (
                <div className="space-y-3">
                  <h4 className="font-medium text-sm flex items-center gap-2">
                    <RefreshCw className="w-4 h-4" />
                    具体改进行动计划 ({reflectionState.reflection.specific_actions.length}项)
                  </h4>
                  <div className="space-y-2">
                    {reflectionState.reflection.specific_actions.map((action, index) => (
                      <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border-l-4 border-purple-500">
                        <div className="font-medium text-sm text-purple-600 dark:text-purple-400 mb-2">
                          行动 {index + 1}: {action.action}
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                          <div className="text-gray-600 dark:text-gray-400">
                            <span className="font-medium">目标部分:</span> {action.target_section}
                          </div>
                          <div className="text-gray-600 dark:text-gray-400">
                            <span className="font-medium">预期改进:</span> {action.expected_improvement}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* 重新生成进度 */}
        {reflectionState.phase === 'regenerating' && (
          <div className="space-y-4">
            <div className="p-3 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
              <div className="flex items-center gap-2 text-orange-700 dark:text-orange-400 mb-2">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span className="font-medium">重新生成进度</span>
              </div>
              {reflectionState.regeneration_progress?.current_section && (
                <div className="text-sm text-orange-600 dark:text-orange-300">
                  正在处理: {reflectionState.regeneration_progress.current_section}
                </div>
              )}
              {reflectionState.regeneration_progress?.completed_sections && reflectionState.regeneration_progress.completed_sections.length > 0 && (
                <div className="mt-2">
                  <div className="text-xs text-gray-600 dark:text-gray-400 mb-1">已完成部分:</div>
                  <div className="flex flex-wrap gap-1">
                    {reflectionState.regeneration_progress.completed_sections.map((section, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {section}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* 实时内容展示 */}
            {reflectionState.streaming_sections && Object.keys(reflectionState.streaming_sections).length > 0 && (
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">改进报告实时生成</h4>
                <div className="report-preview-area">
                  <ScrollArea className="h-[600px] w-full rounded-md border p-4 report-scroll" ref={scrollAreaRef}>
                    <div className="space-y-6 max-w-full">
                      {Object.entries(reflectionState.streaming_sections)
                        .sort(([a], [b]) => {
                          // 按章节顺序排序
                          const order = ['title_and_abstract', 'introduction', 'data_description', 'exploratory_analysis', 'modeling_and_results', 'discussion', 'conclusion'];
                          const aIndex = order.findIndex(key => a.includes(key));
                          const bIndex = order.findIndex(key => b.includes(key));
                          return aIndex - bIndex;
                        })
                        .filter(([, section]) => section.content)
                        .map(([sectionId, section]) => (
                          <div
                            key={sectionId}
                            className={`space-y-2 ${section.status === 'generating' ? 'generating-section' : ''}`}
                          >
                            <div className="flex items-center gap-2">
                              <h5 className="text-sm font-medium">{section.name}</h5>
                              {section.status === 'generating' ? (
                                <>
                                  <RefreshCw className="w-3 h-3 animate-spin text-blue-500" />
                                  <span className="text-xs text-blue-600 dark:text-blue-400">生成中...</span>
                                  <span className="typing-cursor text-primary"></span>
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="w-3 h-3 text-green-500" />
                                  <span className="text-xs text-green-600 dark:text-green-400">已完成</span>
                                </>
                              )}
                              {sectionId.startsWith('improved_') && (
                                <Badge variant="outline" className="text-xs text-blue-600 dark:text-blue-400">
                                  完全重写
                                </Badge>
                              )}
                              {sectionId.startsWith('refined_') && (
                                <Badge variant="outline" className="text-xs text-green-600 dark:text-green-400">
                                  微调优化
                                </Badge>
                              )}
                            </div>
                            <div className="report-content w-full max-w-full overflow-hidden">
                              <div className="prose prose-sm dark:prose-invert max-w-none prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-code:text-foreground prose-pre:bg-muted prose-pre:text-foreground">
                                <ReactMarkdown
                                  children={(() => {
                                    const originalContent = section.content || '';
                                    const processedContent = originalContent
                                      .replace(/\\\[([\s\S]*?)\\\]/g, '$$$$1$$') // \[...\] -> $$...$$
                                      .replace(/\\\(([\s\S]*?)\\\)/g, '$$$1$$'); // \(...\) -> $...$
                                    return processedContent;
                                  })()}
                                  remarkPlugins={[remarkMath]}
                                  rehypePlugins={[rehypeKatex]}
                                components={{
                                  h1: ({ children }) => <h1 className="text-lg font-semibold mb-2 text-foreground">{children}</h1>,
                                  h2: ({ children }) => <h2 className="text-base font-semibold mb-2 text-foreground">{children}</h2>,
                                  h3: ({ children }) => <h3 className="text-sm font-semibold mb-1 text-foreground">{children}</h3>,
                                  p: ({ children }) => <p className="text-sm leading-relaxed mb-2 text-foreground">{children}</p>,
                                  ul: ({ children }) => <ul className="text-sm space-y-1 mb-2 ml-4">{children}</ul>,
                                  ol: ({ children }) => <ol className="text-sm space-y-1 mb-2 ml-4">{children}</ol>,
                                  li: ({ children }) => <li className="text-foreground">{children}</li>,
                                  code: ({ children, className }) => {
                                    const isInline = !className;
                                    return isInline ? (
                                      <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono text-foreground">
                                        {children}
                                      </code>
                                    ) : (
                                      <code className={className}>{children}</code>
                                    );
                                  },
                                  pre: ({ children }) => (
                                    <pre className="bg-muted p-3 rounded-md overflow-x-auto text-xs font-mono text-foreground mb-2">
                                      {children}
                                    </pre>
                                  ),
                                  table: ({ children }) => (
                                    <div className="overflow-x-auto mb-2">
                                      <table className="min-w-full border-collapse border border-border text-xs">
                                        {children}
                                      </table>
                                    </div>
                                  ),
                                  th: ({ children }) => (
                                    <th className="border border-border px-2 py-1 bg-muted font-semibold text-left text-foreground">
                                      {children}
                                    </th>
                                  ),
                                  td: ({ children }) => (
                                    <td className="border border-border px-2 py-1 text-foreground">
                                      {children}
                                    </td>
                                  ),
                                  }}
                                />
                              </div>
                              {section.status === 'generating' && section.content && (
                                <span className="inline-block w-2 h-4 bg-blue-500 animate-pulse ml-1 align-middle"></span>
                              )}
                            </div>
                          </div>
                        ))}
                      <div ref={contentEndRef} />
                    </div>
                  </ScrollArea>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 流式生成内容显示 - 简化版本，仅在非重新生成阶段显示 */}
        {reflectionState.phase !== 'regenerating' && reflectionState.streaming_sections && Object.keys(reflectionState.streaming_sections).length > 0 && (
          <Collapsible
            open={expandedSections.streaming !== false}
            onOpenChange={() => toggleSection('streaming')}
          >
            <CollapsibleTrigger asChild>
              <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                <div className="flex items-center gap-2">
                  <FileText className="w-4 h-4 text-green-500" />
                  <span className="font-medium">改进报告章节概览</span>
                  <Badge variant="outline" className="text-green-600 dark:text-green-400">
                    {Object.keys(reflectionState.streaming_sections).length} 个章节
                  </Badge>
                </div>
                {expandedSections.streaming !== false ?
                  <ChevronUp className="w-4 h-4" /> :
                  <ChevronDown className="w-4 h-4" />
                }
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-3 space-y-2">
              <div className="text-xs text-muted-foreground mb-2">
                已生成的章节概览（详细内容请查看上方"重新生成进度"区域）
              </div>
              <div className="grid gap-2">
                {Object.entries(reflectionState.streaming_sections)
                  .sort(([a], [b]) => {
                    // 按章节顺序排序
                    const order = ['title_and_abstract', 'introduction', 'data_description', 'exploratory_analysis', 'modeling_and_results', 'discussion', 'conclusion'];
                    const aIndex = order.findIndex(key => a.includes(key));
                    const bIndex = order.findIndex(key => b.includes(key));
                    return aIndex - bIndex;
                  })
                  .map(([sectionId, section]) => (
                    <div key={sectionId} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded border">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{section.name}</span>
                        {sectionId.startsWith('improved_') && (
                          <Badge variant="outline" className="text-xs text-blue-600 dark:text-blue-400">
                            完全重写
                          </Badge>
                        )}
                        {sectionId.startsWith('refined_') && (
                          <Badge variant="outline" className="text-xs text-green-600 dark:text-green-400">
                            微调优化
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-2">
                        {section.status === 'generating' ? (
                          <>
                            <RefreshCw className="w-3 h-3 animate-spin text-blue-500" />
                            <span className="text-xs text-blue-600 dark:text-blue-400">生成中</span>
                          </>
                        ) : (
                          <>
                            <CheckCircle className="w-3 h-3 text-green-500" />
                            <span className="text-xs text-green-600 dark:text-green-400">已完成</span>
                          </>
                        )}
                        {section.content && (
                          <span className="text-xs text-gray-500">
                            {section.content.length} 字
                          </span>
                        )}
                      </div>
                    </div>
                  ))}
              </div>
            </CollapsibleContent>
          </Collapsible>
        )}

        {/* 完成状态 - 保留所有已生成的内容 */}
        {reflectionState.phase === 'complete' && (
          <div className="space-y-4">
            {/* 完成提示 */}
            <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg">
              <div className="flex items-center gap-2 text-green-700 dark:text-green-400 mb-3">
                <CheckCircle className="w-5 h-5" />
                <span className="font-medium">反思审查完成</span>
              </div>
              <p className="text-sm text-green-600 dark:text-green-300 mb-3">
                已成功生成改进版本的数据分析报告，质量得到显著提升。
              </p>
              {reflectionState.improved_report_path && (
                <Button
                  onClick={downloadImprovedReport}
                  size="sm"
                  className="w-full"
                  variant="outline"
                >
                  <Download className="w-4 h-4 mr-2" />
                  下载改进报告
                </Button>
              )}
            </div>

            {/* 保留改进后的报告内容显示 */}
            {reflectionState.streaming_sections && Object.keys(reflectionState.streaming_sections).length > 0 && (
              <div className="space-y-3">
                <h4 className="text-sm font-medium text-muted-foreground">改进后的报告内容</h4>
                <div className="report-preview-area">
                  <ScrollArea className="h-[600px] w-full rounded-md border p-4 report-scroll">
                    <div className="space-y-6 max-w-full">
                      {Object.entries(reflectionState.streaming_sections)
                        .sort(([a], [b]) => {
                          // 按章节顺序排序
                          const order = ['title_and_abstract', 'introduction', 'data_description', 'exploratory_analysis', 'modeling_and_results', 'discussion', 'conclusion'];
                          const aIndex = order.findIndex(key => a.includes(key));
                          const bIndex = order.findIndex(key => b.includes(key));
                          return aIndex - bIndex;
                        })
                        .filter(([, section]) => section.content)
                        .map(([sectionId, section]) => (
                          <div key={sectionId} className="space-y-2">
                            <div className="flex items-center gap-2">
                              <h5 className="text-sm font-medium">{section.name}</h5>
                              <CheckCircle className="w-3 h-3 text-green-500" />
                              <span className="text-xs text-green-600 dark:text-green-400">已完成</span>
                              {sectionId.startsWith('improved_') && (
                                <Badge variant="outline" className="text-xs text-blue-600 dark:text-blue-400">
                                  完全重写
                                </Badge>
                              )}
                              {sectionId.startsWith('refined_') && (
                                <Badge variant="outline" className="text-xs text-green-600 dark:text-green-400">
                                  微调优化
                                </Badge>
                              )}
                            </div>
                            <div className="report-content w-full max-w-full overflow-hidden">
                              <div className="prose prose-sm dark:prose-invert max-w-none prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-code:text-foreground prose-pre:bg-muted prose-pre:text-foreground">
                                <ReactMarkdown
                                  children={(() => {
                                    const originalContent = section.content || '';
                                    const processedContent = originalContent
                                      .replace(/\\\[([\s\S]*?)\\\]/g, '$$$$1$$') // \[...\] -> $$...$$
                                      .replace(/\\\(([\s\S]*?)\\\)/g, '$$$1$$'); // \(...\) -> $...$
                                    return processedContent;
                                  })()}
                                  remarkPlugins={[remarkMath]}
                                  rehypePlugins={[rehypeKatex]}
                                  components={{
                                    h1: ({ children }) => <h1 className="text-lg font-semibold mb-2 text-foreground">{children}</h1>,
                                    h2: ({ children }) => <h2 className="text-base font-semibold mb-2 text-foreground">{children}</h2>,
                                    h3: ({ children }) => <h3 className="text-sm font-semibold mb-1 text-foreground">{children}</h3>,
                                    p: ({ children }) => <p className="text-sm leading-relaxed mb-2 text-foreground">{children}</p>,
                                    ul: ({ children }) => <ul className="text-sm space-y-1 mb-2 ml-4">{children}</ul>,
                                    ol: ({ children }) => <ol className="text-sm space-y-1 mb-2 ml-4">{children}</ol>,
                                    li: ({ children }) => <li className="text-foreground">{children}</li>,
                                    code: ({ children, className }) => {
                                      const isInline = !className;
                                      return isInline ? (
                                        <code className="bg-muted px-1 py-0.5 rounded text-xs font-mono text-foreground">
                                          {children}
                                        </code>
                                      ) : (
                                        <code className={className}>{children}</code>
                                      );
                                    },
                                    pre: ({ children }) => (
                                      <pre className="bg-muted p-3 rounded-md overflow-x-auto text-xs font-mono text-foreground mb-2">
                                        {children}
                                      </pre>
                                    ),
                                    table: ({ children }) => (
                                      <div className="overflow-x-auto mb-2">
                                        <table className="min-w-full border-collapse border border-border text-xs">
                                          {children}
                                        </table>
                                      </div>
                                    ),
                                    th: ({ children }) => (
                                      <th className="border border-border px-2 py-1 bg-muted font-semibold text-left text-foreground">
                                        {children}
                                      </th>
                                    ),
                                    td: ({ children }) => (
                                      <td className="border border-border px-2 py-1 text-foreground">
                                        {children}
                                      </td>
                                    ),
                                  }}
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </ScrollArea>
                </div>
              </div>
            )}
          </div>
        )}

        {/* 进行中状态的提示 */}
        {['starting', 'evaluating', 'reflecting', 'regenerating'].includes(reflectionState.phase) && (
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <div className="flex items-center gap-2 text-blue-700 dark:text-blue-400">
              <Brain className="w-4 h-4" />
              <span className="font-medium">Reflexion框架工作中</span>
            </div>
            <p className="text-sm text-blue-600 dark:text-blue-300 mt-2">
              正在使用先进的Reflexion框架对报告进行深度反思和改进，这个过程包括质量评估、问题分析、策略制定和内容重新生成。
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
