"""服务器启动脚本"""

import os
import sys
import asyncio
from pathlib import Path

# 添加src目录到Python路径
backend_dir = Path(__file__).parent
src_dir = backend_dir / "src"
sys.path.insert(0, str(src_dir))

from src.main import main
from src.utils.logger import get_logger

logger = get_logger(__name__)


def check_environment():
    """检查环境配置"""
    logger.info("检查环境配置...")
    
    # 检查.env文件
    env_file = backend_dir / ".env"
    if not env_file.exists():
        logger.error(".env文件不存在，请先创建配置文件")
        return False
    
    # 检查必要的目录
    directories = [
        backend_dir / "data",
        backend_dir / "data" / "chroma",
        backend_dir / "data" / "uploads",
        backend_dir / "data" / "results"
    ]
    
    for directory in directories:
        directory.mkdir(parents=True, exist_ok=True)
        logger.info(f"目录检查/创建: {directory}")
    
    logger.info("环境配置检查完成")
    return True


def main_entry():
    """主入口函数"""
    logger.info("AI智能体数据分析平台后端服务启动...")
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    try:
        # 启动服务器
        main()
    except KeyboardInterrupt:
        logger.info("服务器被用户中断")
    except Exception as e:
        logger.error(f"服务器启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main_entry()
