
import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface AnimatedBackgroundProps {
  className?: string;
}

export function AnimatedBackground({ className }: AnimatedBackgroundProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size to fill parent
    const resizeCanvas = () => {
      const parent = canvas.parentElement;
      if (!parent) return;
      
      canvas.width = parent.offsetWidth * devicePixelRatio;
      canvas.height = parent.offsetHeight * devicePixelRatio;
      canvas.style.width = parent.offsetWidth + 'px';
      canvas.style.height = parent.offsetHeight + 'px';
      ctx.scale(devicePixelRatio, devicePixelRatio);
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // More particles for fuller effect
    const particles: Array<{
      x: number;
      y: number;
      vx: number;
      vy: number;
      opacity: number;
      size: number;
    }> = [];

    // Create more particles
    for (let i = 0; i < 80; i++) {
      particles.push({
        x: Math.random() * (canvas.offsetWidth || window.innerWidth),
        y: Math.random() * (canvas.offsetHeight || window.innerHeight),
        vx: (Math.random() - 0.5) * 0.8,
        vy: (Math.random() - 0.5) * 0.8,
        opacity: Math.random() * 0.6 + 0.2,
        size: Math.random() * 3 + 1,
      });
    }

    // More flowing lines for fuller coverage
    const lines: Array<{
      x: number;
      y: number;
      length: number;
      angle: number;
      speed: number;
      opacity: number;
    }> = [];

    for (let i = 0; i < 15; i++) {
      lines.push({
        x: Math.random() * (canvas.offsetWidth || window.innerWidth),
        y: Math.random() * (canvas.offsetHeight || window.innerHeight),
        length: Math.random() * 300 + 150,
        angle: Math.random() * Math.PI * 2,
        speed: Math.random() * 0.03 + 0.01,
        opacity: Math.random() * 0.4 + 0.2,
      });
    }

    let animationId: number;

    const animate = () => {
      const canvasWidth = canvas.offsetWidth || window.innerWidth;
      const canvasHeight = canvas.offsetHeight || window.innerHeight;
      
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);

      // Draw flowing lines with enhanced glow effect
      lines.forEach((line) => {
        ctx.save();
        ctx.translate(line.x, line.y);
        ctx.rotate(line.angle);

        // Main gradient line
        const gradient = ctx.createLinearGradient(0, 0, line.length, 0);
        gradient.addColorStop(0, `hsla(217, 91%, 60%, 0)`);
        gradient.addColorStop(0.3, `hsla(217, 91%, 70%, ${line.opacity * 0.8})`);
        gradient.addColorStop(0.7, `hsla(270, 95%, 75%, ${line.opacity * 0.6})`);
        gradient.addColorStop(1, `hsla(270, 95%, 75%, 0)`);

        // Glow effect
        ctx.shadowColor = `hsla(217, 91%, 70%, ${line.opacity})`;
        ctx.shadowBlur = 20;
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 3;
        ctx.beginPath();
        ctx.moveTo(0, 0);
        ctx.lineTo(line.length, 0);
        ctx.stroke();

        // Additional glow layer
        ctx.shadowBlur = 40;
        ctx.lineWidth = 1;
        ctx.stroke();

        ctx.restore();

        // Update line position
        line.x += Math.cos(line.angle) * 0.8;
        line.y += Math.sin(line.angle) * 0.8;
        line.angle += line.speed;

        // Wrap around screen with buffer
        if (line.x > canvasWidth + 200) line.x = -200;
        if (line.x < -200) line.x = canvasWidth + 200;
        if (line.y > canvasHeight + 200) line.y = -200;
        if (line.y < -200) line.y = canvasHeight + 200;
      });

      // Draw particles with enhanced glow
      particles.forEach((particle) => {
        ctx.save();
        
        // Glow effect for particles
        ctx.shadowColor = `hsla(217, 91%, 70%, ${particle.opacity})`;
        ctx.shadowBlur = 15;
        
        ctx.fillStyle = `hsla(217, 91%, 80%, ${particle.opacity})`;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fill();

        // Additional smaller bright core
        ctx.shadowBlur = 5;
        ctx.fillStyle = `hsla(217, 91%, 90%, ${particle.opacity * 1.2})`;
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size * 0.5, 0, Math.PI * 2);
        ctx.fill();

        ctx.restore();

        // Update particle position
        particle.x += particle.vx;
        particle.y += particle.vy;

        // Wrap around screen
        if (particle.x > canvasWidth) particle.x = 0;
        if (particle.x < 0) particle.x = canvasWidth;
        if (particle.y > canvasHeight) particle.y = 0;
        if (particle.y < 0) particle.y = canvasHeight;

        // Pulse opacity
        particle.opacity += (Math.random() - 0.5) * 0.03;
        particle.opacity = Math.max(0.2, Math.min(0.8, particle.opacity));
      });

      animationId = requestAnimationFrame(animate);
    };

    animate();

    return () => {
      window.removeEventListener('resize', resizeCanvas);
      cancelAnimationFrame(animationId);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className={cn('absolute inset-0 pointer-events-none w-full h-full', className)}
    />
  );
}
