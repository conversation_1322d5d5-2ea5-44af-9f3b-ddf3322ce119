
import { Brain, Download, <PERSON>ting<PERSON>, User, Home, BarChart3, Zap, FileText, Hash } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useNavigate } from 'react-router-dom';

interface HeaderProps {
  projectName: string;
  onProjectNameChange: (name: string) => void;
  onExportReport: () => void;
  canExport: boolean;
  // 新增的文件和任务信息
  fileName?: string;
  fileSize?: number;
  taskId?: string;
}

export function Header({
  projectName,
  onProjectNameChange,
  onExportReport,
  canExport,
  fileName,
  fileSize,
  taskId
}: HeaderProps) {
  const navigate = useNavigate();

  // 格式化文件大小
  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`;
  };


  return (
    <header className="h-16 border-b border-border bg-card/50 backdrop-blur-sm sticky top-0 z-50">
      <div className="flex items-center justify-between h-full px-6">
        {/* Logo and Project Name */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            {/* Professional Logo Design */}
            <div className="relative">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-primary to-primary-glow flex items-center justify-center shadow-lg">
                <BarChart3 className="w-5 h-5 text-white" />
              </div>
              <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-accent flex items-center justify-center">
                <Zap className="w-2.5 h-2.5 text-white" />
              </div>
            </div>
            <div className="flex flex-col">
              <span className="font-bold text-lg text-gradient">
                Agentic Analysis 
              </span>
              <span className="text-xs text-muted-foreground font-medium">
                AI智能体驱动的数据分析平台
              </span>
            </div>
          </div>

          <div className="h-8 w-px bg-border/60 mx-3" />

          {/* 文件信息和任务ID显示 */}
          <div className="flex flex-col gap-1">
            {/* 上传的文件信息 - 字体大一些 */}
            <div className="flex items-center gap-2">
              <FileText className="w-4 h-4 text-primary" />
              <span className="font-semibold text-base text-foreground">
                {fileName || '未知文件'}
                {fileSize && (
                  <span className="text-muted-foreground font-normal ml-2">
                    ({formatFileSize(fileSize)})
                  </span>
                )}
              </span>
            </div>

            {/* 任务ID - 字体小一些 */}
            <div className="flex items-center gap-1">
              <Hash className="w-3 h-3 text-muted-foreground" />
              <span className="text-xs text-muted-foreground font-mono">
                ID: {taskId || '未知任务'}
              </span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center gap-3">
          <Button
            onClick={() => navigate('/')}
            variant="outline"
            size="sm"
            className="hover:bg-primary/10 hover:border-primary/30 hover:text-current transition-all duration-200"
          >
            <Home className="w-4 h-4 mr-2" />
            返回首页
          </Button>


          <div className="h-6 w-px bg-border/60 mx-1" />

          <ThemeToggle />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="rounded-full w-10 h-10 p-0 hover:bg-primary/10 transition-all duration-200">
                <Avatar className="w-9 h-9">
                  <AvatarFallback className="bg-gradient-to-br from-primary to-accent text-white font-semibold">
                    <User className="w-4 h-4" />
                  </AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56 shadow-xl border-border/50">
              <DropdownMenuItem className="hover:bg-primary/10 transition-colors">
                <User className="w-4 h-4 mr-2" />
                个人资料
              </DropdownMenuItem>
              <DropdownMenuItem className="hover:bg-primary/10 transition-colors">
                <Settings className="w-4 h-4 mr-2" />
                设置
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-destructive hover:bg-destructive/10 transition-colors">
                退出登录
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}