"""数据摘要智能体"""

import pandas as pd
import numpy as np
from typing import Dict, Any
from langchain_core.messages import HumanMessage, SystemMessage
from src.agents.base_agent import BaseAgent
from src.models.state_models import AnalysisState
from src.utils.file_utils import load_dataframe


class SummarizerAgent(BaseAgent):
    """数据摘要智能体"""
    
    def __init__(self):
        super().__init__(
            name="数据摘要智能体",
            description="分析数据集的基本信息，包括数据形状、列类型、缺失值、基本统计信息等"
        )
    
    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行数据摘要分析"""
        self._log_execution("开始数据摘要分析")
        
        try:
            # 加载数据
            df = load_dataframe(state["dataframe_path"])
            
            # 生成基本摘要
            summary = self._generate_basic_summary(df)
            
            # 使用LLM生成详细分析
            detailed_analysis = await self._generate_detailed_analysis(df, summary)
            
            # 将详细分析添加到摘要中
            summary["detailed_analysis"] = detailed_analysis

            # 更新状态
            updated_state = {
                "data_summary": summary,
                "insights": state.get("insights", []) + [detailed_analysis]
            }
            
            self._log_execution("数据摘要分析完成", f"数据形状: {df.shape}")
            return updated_state
            
        except Exception as e:
            error_msg = self._format_error_message(str(e))
            self._log_execution("数据摘要分析失败", str(e))
            return {
                "errors": state.get("errors", []) + [error_msg]
            }
    
    def _generate_basic_summary(self, df: pd.DataFrame) -> Dict[str, Any]:
        """生成基本数据摘要"""
        # 转换NumPy数据类型为Python原生类型
        missing_values = df.isnull().sum()
        missing_values_dict = {k: int(v) for k, v in missing_values.items()}

        summary = {
            "shape": df.shape,
            "columns": df.columns.tolist(),
            "dtypes": df.dtypes.astype(str).to_dict(),
            "missing_values": missing_values_dict,
            "memory_usage": int(df.memory_usage(deep=True).sum()),
        }
        
        # 数值列统计
        numeric_cols = df.select_dtypes(include=['number']).columns
        if len(numeric_cols) > 0:
            numeric_stats = df[numeric_cols].describe().to_dict()
            # 转换NumPy类型为Python原生类型
            summary["numeric_stats"] = {}
            for col, stats in numeric_stats.items():
                summary["numeric_stats"][col] = {k: float(v) if pd.notna(v) else None for k, v in stats.items()}

        # 分类列统计
        categorical_cols = df.select_dtypes(include=['object', 'category']).columns
        if len(categorical_cols) > 0:
            summary["categorical_stats"] = {}
            for col in categorical_cols:
                value_counts = df[col].value_counts().head(5)
                summary["categorical_stats"][col] = {
                    "unique_count": int(df[col].nunique()),
                    "top_values": {k: int(v) for k, v in value_counts.items()}
                }
        
        return summary
    
    async def _generate_detailed_analysis(self, df: pd.DataFrame, summary: Dict[str, Any]) -> str:
        """使用LLM生成详细分析"""
        system_prompt = self._create_system_prompt(
            "数据分析师",
            """
基于提供的数据摘要信息，请生成一个详细的数据分析报告。

报告应包括：
1. 数据集概览（行数、列数、数据类型分布）
2. 数据质量评估（缺失值情况、数据完整性）
3. 关键特征识别（重要的数值特征和分类特征）
4. 潜在的数据问题和建议
5. 后续分析的建议方向

请用专业但易懂的语言描述，并使用Markdown格式输出，包括：
- 使用 # ## ### 等标题层级
- 使用 **粗体** 强调重要信息
- 使用 - 或 1. 创建列表
- 使用 `代码` 标记列名和数据类型
- 使用表格展示关键统计信息
            """
        )
        
        # 构建数据摘要文本
        summary_text = f"""
数据形状: {summary['shape']}
列名: {', '.join(summary['columns'])}
数据类型: {summary['dtypes']}
缺失值: {summary['missing_values']}
内存使用: {summary['memory_usage'] / 1024 / 1024:.2f} MB

数值列统计:
{summary.get('numeric_stats', '无数值列')}

分类列统计:
{summary.get('categorical_stats', '无分类列')}
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"请分析以下数据摘要：\n\n{summary_text}")
        ]
        
        response = await self.llm.ainvoke(messages)
        return response.content
