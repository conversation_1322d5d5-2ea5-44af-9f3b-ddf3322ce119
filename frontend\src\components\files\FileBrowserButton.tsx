import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Folder, FolderOpen } from 'lucide-react';
import FileBrowser from './FileBrowser';

interface FileBrowserButtonProps {
  taskId: string;
  className?: string;
}

const FileBrowserButton: React.FC<FileBrowserButtonProps> = ({ taskId, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              variant="outline"
              size="icon"
              onClick={() => setIsOpen(true)}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              className={`
                relative overflow-hidden transition-all duration-300 ease-in-out
                w-12 h-12 rounded-full border-2
                bg-card/90 backdrop-blur-sm
                border-border/80 hover:border-primary/60
                hover:scale-110 hover:bg-primary/5
                active:scale-95
                shadow-lg hover:shadow-xl hover:shadow-primary/20
                dark:bg-card/95 dark:border-primary/20
                dark:hover:border-primary/70 dark:hover:bg-primary/10
                dark:shadow-xl dark:hover:shadow-primary/30
                ${className}
              `}
            >
              {/* 外部光晕 - 增强深色模式效果 */}
              <div className={`
                absolute -inset-3 rounded-full blur-xl
                bg-primary/20 dark:bg-primary/40
                transition-all duration-300 ease-in-out
                ${isHovered ? 'opacity-100 scale-110' : 'opacity-0 scale-100'}
              `} />

              {/* 中层光圈 */}
              <div className={`
                absolute -inset-1 rounded-full blur-md
                bg-primary/15 dark:bg-primary/30
                transition-all duration-300 ease-in-out
                ${isHovered ? 'opacity-100 scale-105' : 'opacity-0 scale-100'}
              `} />

              {/* 内部光圈 */}
              <div className={`
                absolute inset-1 rounded-full
                bg-primary/10 dark:bg-primary/25
                transition-all duration-300 ease-in-out
                ${isHovered ? 'opacity-100' : 'opacity-0'}
              `} />

              {/* 默认状态的微弱光圈 - 增强深色模式可见性 */}
              <div className={`
                absolute inset-0 rounded-full
                bg-primary/5 dark:bg-primary/15
                transition-all duration-300 ease-in-out
                ${isHovered ? 'opacity-0' : 'opacity-100'}
              `} />

              {/* 文件夹图标 */}
              <div className={`
                relative z-10 transition-all duration-300 ease-in-out
                ${isHovered ? 'scale-110 rotate-3' : 'scale-100 rotate-0'}
              `}>
                {isHovered ? (
                  <FolderOpen className="w-5 h-5 text-primary dark:text-primary" />
                ) : (
                  <Folder className="w-5 h-5 text-foreground/80 dark:text-primary/90" />
                )}
              </div>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>查看任务生成的文件</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <FileBrowser
        taskId={taskId}
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
      />
    </>
  );
};

export default FileBrowserButton;
