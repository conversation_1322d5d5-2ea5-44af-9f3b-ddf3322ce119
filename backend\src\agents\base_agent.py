"""基础智能体类"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from langchain_openai import ChatOpenAI
from src.config import settings
from src.utils.logger import get_logger
from src.models.state_models import AnalysisState

logger = get_logger(__name__)


class BaseAgent(ABC):
    """基础智能体抽象类"""
    
    def __init__(self, name: str = None, description: str = None):
        """初始化智能体"""
        self.name = name or self.__class__.__name__
        self.description = description or ""
        self.logger = get_logger(f"{__name__}.{self.__class__.__name__}")
        self.llm = self._create_llm()
    
    def _create_llm(self) -> ChatOpenAI:
        """创建LLM实例"""
        return ChatOpenAI(
            base_url=settings.openai_api_base,
            api_key=settings.openai_api_key,
            model=settings.openai_model_name,
            temperature=1,
            max_tokens=4000
        )


    
    @abstractmethod
    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行智能体任务"""
        pass
    
    def _create_system_prompt(self, role: str, context: str = "") -> str:
        """创建系统提示"""
        base_prompt = f"""你是一个专业的{role}。

你的任务是：
{self.description}

请遵循以下原则：
1. 提供准确、专业的分析和建议
2. 生成的代码应该是可执行的Python代码
3. 考虑数据的实际情况和特点
4. 提供清晰的解释和说明
5. 如果遇到问题，提供解决方案

{context}

请用中文回复。"""
        return base_prompt
    
    def _format_error_message(self, error: str) -> str:
        """格式化错误消息"""
        return f"[{self.name}] 执行出错: {error}"
    
    def _log_execution(self, action: str, details: str = "") -> None:
        """记录执行日志"""
        log_msg = f"[{self.name}] {action}"
        if details:
            log_msg += f": {details}"
        logger.info(log_msg)
