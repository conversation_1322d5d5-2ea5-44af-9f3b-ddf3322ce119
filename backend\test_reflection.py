#!/usr/bin/env python3
"""测试反思审查功能"""

import asyncio
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.agents.reflection_agent import ReflectionAgent
from src.models.state_models import AnalysisState
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def test_reflection_agent():
    """测试反思智能体"""
    print("🧠 开始测试反思审查功能...")
    
    # 创建测试状态
    test_state = AnalysisState(
        task_id="test_reflection_001",
        original_query="分析销售数据，找出影响销售额的关键因素",
        dataframe_path="test_data.csv",
        plan={
            "steps": [
                {"step": 1, "objective": "数据清洗", "description": "处理缺失值和异常值"},
                {"step": 2, "objective": "探索性分析", "description": "分析数据分布和相关性"},
                {"step": 3, "objective": "建模分析", "description": "建立预测模型"}
            ]
        },
        current_step=3,
        executed_steps=[
            {
                "step": 1,
                "code": "df.isnull().sum()",
                "result": "检查缺失值",
                "insights": ["数据质量良好，缺失值较少"]
            },
            {
                "step": 2, 
                "code": "df.corr()",
                "result": "相关性分析",
                "insights": ["价格与销量呈负相关"]
            },
            {
                "step": 3,
                "code": "from sklearn.linear_model import LinearRegression",
                "result": "建立线性回归模型",
                "insights": ["模型R²得分为0.85，效果良好"]
            }
        ],
        chroma_collection_name="test_collection",
        errors=[],
        insights=["数据分析完成"],
        final_report={
            "title_and_abstract": "销售数据分析报告\n\n本报告分析了销售数据的关键特征。",
            "introduction": "销售数据分析对于业务决策至关重要。",
            "data_description": "数据集包含销售记录，共1000条记录。",
            "exploratory_analysis": "通过探索性分析发现了数据的基本特征。",
            "modeling_and_results": "使用线性回归模型进行预测分析。",
            "discussion": "模型结果显示价格是影响销量的重要因素。",
            "conclusion": "建议优化定价策略以提升销售业绩。"
        },
        data_summary={
            "shape": [1000, 5],
            "columns": ["date", "product", "price", "quantity", "revenue"],
            "dtypes": {"date": "datetime", "product": "object", "price": "float64", "quantity": "int64", "revenue": "float64"}
        }
    )
    
    # 创建反思智能体
    reflection_agent = ReflectionAgent()
    
    try:
        print("📊 执行反思审查...")
        result = await reflection_agent.execute(test_state)
        
        print("✅ 反思审查完成!")
        print(f"📈 评估结果: {result.get('reflection_evaluation', {}).get('overall_score', 'N/A')}")
        print(f"💡 改进洞察: {len(result.get('reflection_insights', {}).get('specific_actions', []))} 个具体改进行动")
        print(f"📄 改进报告路径: {result.get('improved_report_path', 'N/A')}")
        
        # 显示评估详情
        evaluation = result.get('reflection_evaluation', {})
        if evaluation:
            print("\n📋 评估详情:")
            print(f"  总体评分: {evaluation.get('overall_score', 'N/A')}/10")
            
            critical_issues = evaluation.get('critical_issues', [])
            if critical_issues:
                print(f"  关键问题: {len(critical_issues)} 个")
                for i, issue in enumerate(critical_issues[:3], 1):
                    print(f"    {i}. {issue}")
        
        # 显示反思详情
        reflection = result.get('reflection_insights', {})
        if reflection:
            print("\n🧠 反思详情:")
            strategies = reflection.get('improvement_strategies', {})
            print(f"  改进策略: {len(strategies)} 个")
            for strategy, description in list(strategies.items())[:2]:
                print(f"    • {strategy}: {description[:50]}...")
            
            priorities = reflection.get('rewrite_priorities', [])
            if priorities:
                print(f"  重写优先级: {', '.join(priorities[:3])}")
        
        return True
        
    except Exception as e:
        print(f"❌ 反思审查失败: {str(e)}")
        logger.error(f"反思测试失败: {str(e)}")
        return False


async def test_context_collection():
    """测试上下文信息收集"""
    print("\n📚 测试上下文信息收集...")
    
    reflection_agent = ReflectionAgent()
    
    test_state = AnalysisState(
        task_id="test_context_001",
        original_query="测试查询",
        dataframe_path="test.csv",
        plan={"steps": []},
        current_step=0,
        executed_steps=[],
        chroma_collection_name="test_collection",
        errors=[],
        insights=[],
        data_summary={"shape": [100, 3]},
        final_report={"title_and_abstract": "测试报告"}
    )
    
    try:
        context = await reflection_agent._collect_analysis_context(test_state)
        
        print("✅ 上下文收集完成!")
        print(f"  原始查询: {context.get('original_query', 'N/A')}")
        print(f"  数据摘要: {bool(context.get('data_summary'))}")
        print(f"  执行步骤: {len(context.get('executed_steps', []))}")
        print(f"  生成图片: {len(context.get('generated_images', []))}")
        print(f"  ChromaDB洞察: {len(context.get('chroma_insights', []))}")
        
        return True
        
    except Exception as e:
        print(f"❌ 上下文收集失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始反思审查功能测试\n")
    
    # 测试上下文收集
    context_success = await test_context_collection()
    
    # 测试完整反思流程
    reflection_success = await test_reflection_agent()
    
    print(f"\n📊 测试结果:")
    print(f"  上下文收集: {'✅ 成功' if context_success else '❌ 失败'}")
    print(f"  反思审查: {'✅ 成功' if reflection_success else '❌ 失败'}")
    
    if context_success and reflection_success:
        print("\n🎉 所有测试通过! 反思审查功能正常工作。")
        return 0
    else:
        print("\n⚠️  部分测试失败，请检查日志。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
