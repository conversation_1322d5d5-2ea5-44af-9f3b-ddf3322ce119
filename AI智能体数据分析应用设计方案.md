好的，遵照您的要求，这是一份极其详尽、专业且可直接用于开发的全流程AI智能体数据分析Web应用设计方案。方案融合了现代化的设计理念、详尽的技术实现路径，并深度整合了Plan-and-Execute与ReAct思考框架。

---

### **全流程AI智能体数据分析平台：详细设计方案**

#### **1. 总体愿景与设计哲学**

我们的目标是构建一个**“透明可交互的AI数据科学家”**。它不仅仅是一个自动化工具，更是一个智能协作伙伴。用户能够清晰地看到AI的“思考过程”，并在关键节点进行干预，实现人机协同的深度数据洞察。

*   **设计哲学一：过程透明化 (Transparent AI)**
    *   拒绝黑盒。AI的每一步规划、每一次代码生成、每一次执行结果、每一次纠错，都将以直观、可视化的方式呈现给用户。左侧的工作流面板是这一哲学的核心体现。
*   **设计哲学二：结果可交互 (Interactive Analysis)**
    *   分析结果不是静态的报告。图表可以缩放、筛选；代码可以查看、复制代码；洞察可以追溯其来源数据。右侧的结果展示面板是交互的核心。
*   **设计哲学三：智能与框架驱动 (Framework-Driven Intelligence)**
    *   以**Plan-and-Execute**作为顶层战略框架，确保分析方向的正确性和全局性。
    *   在具体的执行节点（如数据预处理、代码生成）内部，融入**ReAct (Reasoning and Acting)**框架，让智能体具备“思考->行动->观察->再思考”的微观循环能力，极大提升其处理复杂和未知情况的鲁棒性。

---

#### **2. 系统架构总览**

  **(这是一个概念图，实际开发中应绘制更详细的C4模型图)**

<div align="center">

**[前端 (Vite+React)]** <=> **[后端 (FastAPI)]** <=> **[AI核心 (LangGraph)]** <=> **[执行引擎 (Jupyter)]** <=> **[记忆/元数据 (ChromaDB)]**

</div>

*   **前端 (Client)**: 用户的交互界面，负责数据上传、目标输入、流程监控和结果可视化。
*   **后端 (Backend API Server)**: FastAPI应用，作为网关，处理HTTP请求和WebSocket连接，管理任务队列。
*   **AI核心 (AI Core)**: LangGraph构建的多智能体协作系统，是整个分析流程的大脑。
*   **执行引擎 (Execution Engine)**: 通过`jupyter_client`与独立的IPython内核通信，安全地执行由AI生成的代码。
*   **记忆与元数据存储 (Memory/Metadata Store)**: ChromaDB，存储每一次分析任务中产生的关键信息（代码、日志、洞察、中间结果摘要），用于上下文检索和未来分析的参考。

---

#### **3. 前端详细设计 (Vite + React + TailwindCSS + Shadcn UI)**

##### **3.1. 页面核心布局**

采用经典的双栏布局：**左侧为工作流面板，右侧为结果展示面板**。

*   **Header**: 包含应用Logo、项目名称（可编辑）、用户状态、主题切换（明/暗模式）和导出报告按钮。
*   **左侧面板 (Workflow Visualizer - 1/3宽度)**:
    *   固定宽度，可折叠。
    *   用于实时、可视化地展示AI智能体的工作流程图。
*   **右侧面板 (Results & Interaction - 2/3宽度)**:
    *   主内容区域，动态展示当前选中工作流节点的结果。

##### **3.2. 左侧：智能体工作流可视化面板**

这是一个动态生成的有向无环图 (DAG) 或垂直时间线。

*   **节点 (Node)**: 代表LangGraph中的一个Agent或一个关键步骤。每个节点应包含：
    *   **图标**: 直观表示节点类型（如：规划-大脑图标，预处理-清洗图标，代码-</>图标）。
    *   **标题**: 节点名称（如：“规划分析路径”、“数据清洗与预处理”）。
    *   **状态指示器**:
        *   `Pending` (灰色): 等待执行。
        *   `Running` (蓝色，带动画): 正在执行。
        *   `Success` (绿色): 成功完成。
        *   `Warning` (橙色): 完成但有警告（如：部分数据被丢弃）。
        *   `Error` (红色): 执行失败。
    *   **时间戳**: 节点完成的时间。
*   **边 (Edge)**: 连接节点的箭头，表示数据和控制流的走向。如果是条件分支（如成功/失败），应有不同颜色的线条或标签。
*   **交互**:
    *   **点击节点**: 右侧面板会滚动并高亮显示该节点对应的详细结果。
    *   **悬浮提示 (Tooltip)**: 鼠标悬浮在节点上时，显示该节点的简要摘要（如：“生成了3个清洗步骤的代码”）。
    *   **可折叠的子步骤**: 对于复杂的节点（如“执行分析代码”），可以点击展开，显示其内部的ReAct循环（思考->行动->观察...）。

##### **3.3. 右侧：结果与交互展示面板**

这是一个可滚动的长内容区域，由多个**卡片 (Card)**组成，每个卡片对应左侧工作流中的一个节点。

*   **卡片类型 (使用Shadcn UI组件构建)**:
    *   **数据摘要卡 (Data Summary Card)**:
        *   使用`Table`组件展示数据的前几行和后几行。
        *   使用`Card`和`Progress`组件展示基本统计信息：行数、列数、特征类型（数值/分类）、缺失值比例等。
    *   **规划路径卡 (Plan Card)**:
        *   以有序列表或Checklist的形式，展示Planner Agent生成的分析步骤。每个步骤都是一个清晰的指令。
    *   **代码卡 (Code Card)**:
        *   使用类似VS Code的`Code Block`组件（如`react-syntax-highlighter`），支持Python语法高亮。
        *   右上角提供“一键复制”和“查看执行日志”按钮。
    *   **执行结果卡 (Execution Result Card)**:
        *   **文本输出**: 直接显示代码执行的print输出或日志。
        *   **图表**: 使用**ECharts**或**Plotly.js**渲染交互式图表。图表应可下载为PNG/SVG。
        *   **表格**: 对于返回DataFrame的结果，使用可排序、可分页的`DataTable`组件展示。
    *   **错误与纠错卡 (Error & Correction Card)**:
        *   以`Alert`组件（`AlertDestructive`变体）醒目地展示错误信息。
        *   清晰地展示原始错误代码、错误日志、AI的修正思路（“我发现了一个`KeyError`，原因是列名错误，我将尝试修正列名”），以及修正后的代码。这是一个关键的透明化设计。
    *   **洞察卡 (Insights Card)**:
        *   使用格式化的文本块，突出关键发现。例如，使用引言(Blockquote)或自定义的`Insight`组件。
        *   洞察中的关键指标（如“R²分数为0.85”）应可点击，点击后高亮其来源的图表或数据卡片。
    *   **最终报告预览卡 (Final Report Preview Card)**:
        *   将所有结果和洞察汇总成一个Markdown渲染的页面，支持导出为PDF或HTML。

##### **3.4. 核心页面与用户流程**

1.  **欢迎页/项目列表页**: 用户可以在此创建新分析项目或打开历史项目。
2.  **数据上传与配置页**:
    *   通过拖拽或文件选择器上传CSV/Excel文件。
    *   用户用**自然语言**输入核心分析目标，例如：“请分析我的销售数据，找出影响销售额的关键因素，并预测下个季度的销售额。”
    *   点击“开始分析”，前端将文件和目标发送到后端，进入主分析界面。
3.  **主分析界面**:
    *   立即显示左右双栏布局。
    *   左侧工作流面板出现第一个节点“初始化任务”，状态为`Running`。
    *   通过**WebSocket**实时接收后端推送的更新，动态构建左侧的工作流图，并在右侧追加结果卡片。
    *   整个过程自动化进行，用户可以随时滚动查看，但默认视图会跟随最新的结果。
4.  **分析完成**:
    *   左侧所有节点变为最终状态（绿色/红色）。
    *   右上角的“导出报告”按钮变为可用状态。

---

#### **4. 后端详细设计 (FastAPI + LangGraph + Jupyter + ChromaDB)**

##### **4.1. API 接口与通信协议**

*   **HTTP Endpoints (FastAPI)**:
    *   `POST /api/v1/tasks`: 创建一个新的分析任务。
        *   **Request**: `multipart/form-data`，包含`file`和`prompt` (分析目标)。
        *   **Response**: `{"task_id": "unique_task_id"}`。此接口会立即返回，并在后台触发LangGraph流程。
    *   `GET /api/v1/tasks/{task_id}`: 获取一个任务的完整历史结果（用于重新加载页面）。
        *   **Response**: 返回一个包含所有步骤和结果的JSON对象。
    *   `GET /api/v1/tasks`: 获取所有历史任务列表。
*   **WebSocket (FastAPI)**:
    *   `WS /ws/v1/tasks/{task_id}/stream`: 客户端通过此接口订阅特定任务的实时更新。
    *   **消息格式 (Server -> Client)**:
        ```json
        {
          "event": "node_update", // 事件类型: node_update, task_complete, error
          "payload": {
            "node_id": "preprocess_agent_1",
            "node_name": "数据预处理",
            "status": "Success", // Pending, Running, Success, Error
            "content": { // 对应右侧面板卡片的数据
              "type": "code_result", // card_type: 'summary', 'plan', 'code', etc.
              "data": {
                "code": "df.dropna(inplace=True)",
                "stdout": "处理了15个缺失值。",
                "execution_time": "0.5s"
              }
            }
          }
        }
        ```

##### **4.2. AI智能体核心架构 (LangGraph)**

**A. 顶层思考框架：Plan-and-Execute**

整个流程由一个宏观的计划驱动。

1.  **用户输入**: "分析销售数据，找出关键因素，并预测下季度销售额。"
2.  **Planner Agent (规划智能体)**: 接收用户输入和数据初步摘要，输出一个结构化的JSON计划。
    *   **输出示例**:
        ```json
        {
          "plan": [
            { "step": 1, "objective": "数据基本摘要", "agent": "SummarizerAgent", "dependencies": [] },
            { "step": 2, "objective": "处理数据缺失值和异常值", "agent": "PreprocessAgent", "dependencies": [1] },
            { "step": 3, "objective": "进行探索性数据分析(EDA)，找出特征与销售额的关系", "agent": "AnalysisAgent", "dependencies": [2] },
            { "step": 4, "objective": "构建线性回归模型进行销售额预测", "agent": "ModelingAgent", "dependencies": [2] },
            { "step": 5, "objective": "评估模型性能并解释关键影响因素", "agent": "EvaluationAgent", "dependencies": [4] },
            { "step": 6, "objective": "汇总所有发现并生成报告", "agent": "ReportAgent", "dependencies": [1,2,3,4,5] }
          ]
        }
        ```

**B. LangGraph StateGraph 定义**

这是在图的节点之间传递的状态对象。

```python
from typing import TypedDict, List, Dict

class AnalysisState(TypedDict):
    task_id: str
    original_query: str
    dataframe_path: str  # 指向磁盘上当前数据集的路径
    plan: Dict # Planner Agent生成的计划
    current_step: int # 当前执行到计划的第几步
    executed_steps: List[Dict] # 已完成步骤的结果摘要
    chroma_collection_name: str # 此任务在ChromaDB中对应的集合名
    errors: List[str]
    insights: List[str]
```

**C. Agent 节点与角色设计 (融合ReAct)**

每个Agent都是图中的一个节点，接收`AnalysisState`，执行任务，然后返回更新后的`AnalysisState`。

*   **1. Initializer Node**:
    *   接收API请求，创建`task_id`，将数据加载到pandas DataFrame并保存为初始文件（如Parquet），初始化`AnalysisState`，创建对应的ChromaDB集合。
*   **2. Summarizer Agent Node**:
    *   读取`dataframe_path`指向的数据。
    *   生成数据摘要（.info(), .describe()等）。
    *   将摘要信息存入ChromaDB。
    *   **输出**: 文本摘要。
*   **3. Planner Agent Node**:
    *   接收用户`original_query`和数据摘要。
    *   调用LLM生成上述的结构化JSON计划。
    *   更新`AnalysisState`中的`plan`字段。
*   **4. Executor Loop (核心循环)**: 这是一个子图，根据`plan`逐一执行步骤。
    *   **a. Preprocess/Analysis/Modeling Agent (代码生成器)**:
        *   **内部使用ReAct框架**。
        *   **Thought**: "我需要处理缺失值。首先，我需要检查哪些列有缺失值以及它们的比例。"
        *   **Action (Tool Call)**: `generate_code("df.isnull().sum() / len(df)")`
        *   **b. Code Execution Node (执行与纠错)**:
            *   接收生成的代码。
            *   通过`jupyter_client`在隔离的内核中执行。
            *   **Observation**: 捕获`stdout`, `stderr`, 和图表数据。
            *   **如果成功**: 返回结果，ReAct循环继续或结束。
            *   **如果失败 (e.g., `SyntaxError`, `NameError`)**:
                *   **触发纠错子流程**: 将错误信息、代码、上下文（之前的成功代码）打包，再次调用LLM。
                *   **LLM (Corrector Mode) Thought**: "错误是`NameError: 'pd' is not defined`。我忘记导入pandas了。我需要在我生成的代码前加上`import pandas as pd`。"
                *   LLM返回修正后的代码，重新进入执行步骤。这个“尝试-失败-修正”的循环是鲁棒性的关键。
    *   **c. ChromaDB Logger Node**:
        *   在每个成功的代码执行后，将**代码、执行输出、生成的洞察（如果有）和对应的Embedding**存入ChromaDB。
        *   **Schema in ChromaDB**:
            *   **Payload**: `{"task_id": ..., "step": 3, "type": "python_code", "content": "sns.heatmap(...)"}`
            *   **Payload**: `{"task_id": ..., "step": 3, "type": "insight", "content": "销售额和广告投入呈显著正相关。"}`
            *   **Embedding**: 对`content`进行嵌入。

*   **5. Report Agent Node**:
    *   当所有计划步骤完成后触发。
    *   从`AnalysisState`和ChromaDB中检索整个流程的所有关键信息（摘要、代码、图表、洞察、错误日志）。
    *   构建一个完整的、条理清晰的Markdown报告。
    *   调用LLM对报告进行语言润色，使其更专业、更具可读性。

**D. LangGraph 流程图 (逻辑)**

```plaintext
Start -> Initializer -> Summarizer -> Planner -> [Executor_Loop] -> ReportAgent -> End

Executor_Loop:
    (For each step in plan)
    --> Code_Generator_Agent (ReAct inside)
    --> Code_Executor_Node --(on_success)--> ChromaDB_Logger --> (Next step)
           |
           --(on_error)--> Corrector_Agent --> Code_Executor_Node (retry)
```

##### **4.3. 执行引擎 (Jupyter Kernel Management)**

*   为每个`task_id`启动一个独立的IPython内核进程。这确保了用户会话之间的完全隔离。
*   使用`jupyter_client`库异步发送代码执行请求 (`execute_request`) 并监听IO Pub/Sub通道以接收结果。
*   能够捕获所有类型的消息：`stream` (stdout/stderr), `display_data` (图表、富文本), `execute_result` (最终表达式结果), 和 `error`。
*   当任务完成或超时后，内核进程被安全终止。

##### **4.4. 向量数据库集成 (ChromaDB)**

*   **用途**: 作为AI智能体的**长期记忆和上下文检索工具**。
*   **存储内容**:
    *   用户原始问题 (Prompt)。
    *   生成的分析计划 (Plan)。
    *   每一步生成的代码片段。
    *   代码执行后的文本输出和错误日志。
    *   由LLM从图表和数据中提取的文本洞察。
*   **使用场景**:
    *   在生成报告时，Report Agent可以执行语义搜索，找到与“关键驱动因素”相关的_所有_代码和洞察，而不仅仅是最后一步的结果。
    *   在纠错时，可以检索相似的、过去成功执行过的代码片段作为参考。
    *   未来可用于跨任务的知识迁移（例如，一个新任务可以从过去相似的分析任务中学习规划）。

---

#### **5. 前后端交互协议总结**

1.  **[用户]** 上传文件和目标 -> **[前端]**
2.  **[前端]** `POST /api/v1/tasks` -> **[后端]**
3.  **[后端]** 返回 `task_id`，并异步启动LangGraph流程。
4.  **[前端]** 使用`task_id`建立WebSocket连接 `ws://.../{task_id}/stream`。
5.  **[后端 LangGraph]** 每完成一个节点，通过WebSocket推送`node_update`消息。
6.  **[前端]** 接收消息，更新左侧工作流图，并在右侧渲染新的结果卡片。
7.  **(循环步骤5-6)** 直到任务完成。
8.  **[后端 LangGraph]** 任务结束，推送`task_complete`消息，关闭WebSocket。
9.  **[用户]** 查看完整报告，点击“导出”。
10. **[前端]** `GET /api/v1/tasks/{task_id}/report` (一个专门生成报告的接口) -> **[后端]** -> 下载文件。

---

#### **6. 技术选型与工具链**

*   **项目管理**: `uv` (替代pip和venv，提供更快的依赖解析和环境管理)。
*   **前端**: Vite, React, TypeScript, TailwindCSS, shadcn/ui, ECharts for Apache/Plotly.js, react-flow (用于工作流可视化)。
*   **后端**: FastAPI, Uvicorn, Pydantic (用于数据校验)。
*   **AI与编排**: LangChain, LangGraph, OpenAI/Claude/Gemini API。
*   **执行环境**: Jupyter Client, IPython Kernel。
*   **数据库**: ChromaDB (向量存储), 本地文件系统 (Parquet格式存储数据集)。
*   **通信**: HTTP (REST), WebSocket。

此设计方案提供了从宏观架构到微观交互的全面蓝图，确保前后端工程师有清晰、一致的开发目标，能够构建出一个功能强大、体验优秀且技术领先的数据分析智能体应用。