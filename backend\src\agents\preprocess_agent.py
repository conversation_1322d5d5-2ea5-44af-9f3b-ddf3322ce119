"""数据预处理智能体"""

import pandas as pd
from typing import Dict, Any
from langchain_core.messages import HumanMessage, SystemMessage
from src.agents.base_agent import BaseAgent
from src.models.state_models import AnalysisState
from src.utils.file_utils import load_dataframe, save_dataframe
from src.execution.jupyter_executor import jupyter_executor


class PreprocessAgent(BaseAgent):
    """数据预处理智能体"""
    
    def __init__(self):
        super().__init__(
            name="数据预处理智能体",
            description="处理数据清洗、缺失值处理、异常值检测、数据类型转换等预处理任务"
        )
    
    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行数据预处理"""
        self._log_execution("开始数据预处理")
        
        try:
            # 生成预处理代码
            preprocess_code = await self._generate_preprocess_code(state)
            
            # 执行预处理代码
            execution_result = await jupyter_executor.execute_code(
                state["task_id"], 
                preprocess_code
            )
            
            if execution_result.success:
                insights = [
                    "数据预处理完成",
                    f"执行时间: {execution_result.execution_time:.2f}秒"
                ]
                
                if execution_result.stdout:
                    insights.append(f"处理结果: {execution_result.stdout}")
                
                updated_state = {
                    "insights": state.get("insights", []) + insights
                }
                
                self._log_execution("数据预处理完成")
                return updated_state
            else:
                error_msg = self._format_error_message(execution_result.stderr or "预处理执行失败")
                return {
                    "errors": state.get("errors", []) + [error_msg]
                }
                
        except Exception as e:
            error_msg = self._format_error_message(str(e))
            self._log_execution("数据预处理失败", str(e))
            return {
                "errors": state.get("errors", []) + [error_msg]
            }
    
    async def _generate_preprocess_code(self, state: AnalysisState) -> str:
        """生成数据预处理代码"""
        system_prompt = self._create_system_prompt(
            "数据预处理专家",
            """
你需要生成Python代码来进行数据预处理。

预处理任务可能包括：
1. 加载数据
2. 检查数据质量
3. 处理缺失值（删除、填充、插值等）
4. 处理异常值（检测、处理）
5. 数据类型转换
6. 重复值处理
7. 数据标准化/归一化
8. 特征编码（如独热编码）

请生成完整的Python代码，包含：
- 必要的导入语句
- 数据加载
- 预处理步骤
- 结果输出和统计信息
- 适当的注释说明

只返回Python代码，不要包含其他解释。
            """
        )
        
        # 构建上下文
        context_parts = [
            f"数据文件路径: {state['dataframe_path']}",
            f"原始查询: {state['original_query']}"
        ]
        
        # 添加数据摘要信息
        if state.get("data_summary"):
            summary = state["data_summary"]
            context_parts.extend([
                f"数据形状: {summary.get('shape', '未知')}",
                f"列名: {', '.join(summary.get('columns', []))}",
                f"缺失值情况: {summary.get('missing_values', {})}"
            ])
        
        context = "\n".join(context_parts)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"请为以下数据生成预处理代码：\n\n{context}")
        ]
        
        response = await self.llm.ainvoke(messages)
        return response.content.strip()
