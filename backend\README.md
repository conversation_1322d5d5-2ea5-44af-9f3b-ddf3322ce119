# AI智能体数据分析平台后端

基于LangGraph和ChromaDB的智能数据分析平台后端服务。

## 功能特性

- 🤖 **智能体系统**: 基于LangGraph实现的多智能体协作分析
- 📊 **数据分析**: 自动化的数据预处理、探索性分析和可视化
- 🔄 **实时通信**: WebSocket实时推送分析进度和结果
- 💾 **向量存储**: ChromaDB存储分析历史和知识
- 🐍 **代码执行**: Jupyter内核执行Python分析代码
- 🚀 **高性能**: FastAPI异步框架，支持并发处理

## 技术栈

- **框架**: FastAPI + Uvicorn
- **AI**: LangGraph + LangChain + OpenAI API
- **数据库**: ChromaDB (向量数据库)
- **执行引擎**: Jupyter Client
- **数据处理**: Pandas + NumPy + Scikit-learn
- **可视化**: Matplotlib + Seaborn + Plotly
- **包管理**: UV

## 快速开始

### 1. 安装依赖

```bash
# 使用UV安装依赖
uv sync
```

### 2. 配置环境

确保`.env`文件已正确配置AI模型API信息。

### 3. 启动服务器

```bash
# 使用启动脚本
python run_server.py

# 或直接运行
python main.py
```

### 4. 测试系统

```bash
# 运行系统测试
python test_system.py
```

服务器启动后访问 http://localhost:8001 查看API文档。

## API接口

- **创建分析任务**: `POST /api/v1/tasks`
- **获取任务状态**: `GET /api/v1/tasks/{task_id}`
- **获取任务列表**: `GET /api/v1/tasks`
- **获取分析结果**: `GET /api/v1/tasks/{task_id}/results`
- **WebSocket连接**: `ws://localhost:8001/ws/{task_id}`

## 智能体工作流

1. **SummarizerAgent**: 分析数据基本信息
2. **PlannerAgent**: 制定分析计划
3. **CodeAgent**: 生成和执行代码
4. **PreprocessAgent**: 数据预处理
5. **AnalysisAgent**: 探索性分析