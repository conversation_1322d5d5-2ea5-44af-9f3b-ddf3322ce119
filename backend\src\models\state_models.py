"""LangGraph状态模型"""

from typing import TypedDict, List, Dict, Any, Optional
from datetime import datetime


class AnalysisState(TypedDict):
    """LangGraph分析状态"""
    task_id: str
    original_query: str
    dataframe_path: str
    plan: Optional[Dict[str, Any]]
    current_step: int
    executed_steps: List[Dict[str, Any]]
    chroma_collection_name: str
    errors: List[str]
    insights: List[str]
    messages: List[Dict[str, Any]]

    # 数据相关
    data_summary: Optional[Dict[str, Any]]

    # 执行相关
    jupyter_kernel_id: Optional[str]
    execution_results: List[Dict[str, Any]]

    # 用户交互相关
    waiting_for_user: bool
    user_approved_plan: bool
    user_modified_plan: Optional[Dict[str, Any]]
    library_preferences: Optional[Dict[str, Any]]

    # 时间戳
    created_at: datetime
    updated_at: datetime


class StepResult(TypedDict):
    """步骤执行结果"""
    step_id: str
    step_name: str
    status: str
    code: Optional[str]
    result: Optional[Any]
    error: Optional[str]
    execution_time: float
    timestamp: datetime


class ChromaDocument(TypedDict):
    """ChromaDB文档结构"""
    task_id: str
    step: int
    type: str  # "python_code", "insight", "error", "result"
    content: str
    metadata: Dict[str, Any]
    timestamp: datetime
