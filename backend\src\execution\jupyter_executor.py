"""Jupyter代码执行器"""

import asyncio
import json
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from jupyter_client import KernelManager
from jupyter_client.kernelspec import KernelSpecManager
from src.config import settings
from src.utils.logger import get_logger
from src.models.api_models import ExecutionResult

logger = get_logger(__name__)


class JupyterExecutor:
    """Jupyter代码执行器"""
    
    def __init__(self):
        """初始化执行器"""
        self.kernels: Dict[str, KernelManager] = {}
        self.kernel_spec_manager = KernelSpecManager()
        self.last_executed_code: Dict[str, str] = {}  # 保存每个任务的最近执行代码
    
    async def create_kernel(self, task_id: str) -> str:
        """为任务创建独立的内核"""
        try:
            kernel_manager = KernelManager(
                kernel_name=settings.jupyter_kernel_name
            )
            kernel_manager.start_kernel()
            
            # 等待内核启动
            await asyncio.sleep(2)
            
            # 存储内核管理器
            self.kernels[task_id] = kernel_manager
            
            logger.info(f"内核创建成功: {task_id}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建内核失败: {task_id}, 错误: {str(e)}")
            raise
    
    async def execute_code(
        self, 
        task_id: str, 
        code: str,
        timeout: Optional[int] = None
    ) -> ExecutionResult:
        """执行代码"""
        if task_id not in self.kernels:
            await self.create_kernel(task_id)
        
        kernel_manager = self.kernels[task_id]
        client = kernel_manager.client()
        
        start_time = datetime.now()
        
        try:
            # 保存执行的代码用于图表类型推断
            self.last_executed_code[task_id] = code

            # 执行代码
            msg_id = client.execute(code)
            
            # 收集执行结果
            stdout_lines = []
            stderr_lines = []
            result = None
            plots = []
            
            timeout_seconds = timeout or settings.jupyter_timeout
            
            # 等待执行完成
            while True:
                try:
                    msg = client.get_iopub_msg(timeout=timeout_seconds)
                    
                    if msg['parent_header'].get('msg_id') == msg_id:
                        msg_type = msg['msg_type']
                        content = msg['content']
                        
                        if msg_type == 'stream':
                            if content['name'] == 'stdout':
                                stdout_lines.append(content['text'])
                            elif content['name'] == 'stderr':
                                stderr_lines.append(content['text'])
                        
                        elif msg_type == 'execute_result':
                            result = content.get('data', {}).get('text/plain', '')
                        
                        elif msg_type == 'display_data':
                            # 处理图表数据
                            data = content.get('data', {})
                            if 'image/png' in data:
                                # 保存图表
                                plot_path = await self._save_plot(task_id, data['image/png'])
                                plots.append(plot_path)
                            elif 'application/vnd.plotly.v1+json' in data:
                                # 处理plotly图表
                                plot_path = await self._save_plotly_chart(task_id, data['application/vnd.plotly.v1+json'])
                                if plot_path:
                                    plots.append(plot_path)
                            elif 'text/html' in data and 'plotly' in data['text/html'].lower():
                                # 处理plotly HTML输出（备用方案）
                                try:
                                    # 尝试从HTML中提取plotly数据
                                    html_content = data['text/html']
                                    if 'Plotly.newPlot' in html_content or 'plotly-div' in html_content:
                                        logger.info("检测到plotly HTML输出，但无法直接转换为图片")
                                        # 这种情况下我们无法直接转换，需要用户使用fig.to_image()方法
                                except Exception as e:
                                    logger.debug(f"处理plotly HTML输出时出错: {str(e)}")
                        
                        elif msg_type == 'error':
                            # 处理错误信息的编码问题
                            traceback_lines = content.get('traceback', [])
                            for line in traceback_lines:
                                # 确保错误信息是UTF-8编码
                                if isinstance(line, bytes):
                                    line = line.decode('utf-8', errors='replace')
                                elif isinstance(line, str):
                                    # 移除ANSI转义序列
                                    import re
                                    line = re.sub(r'\x1b\[[0-9;]*m', '', line)
                                stderr_lines.append(line)
                        
                        elif msg_type == 'status' and content['execution_state'] == 'idle':
                            break
                
                except Exception as e:
                    if "timeout" in str(e).lower():
                        stderr_lines.append(f"代码执行超时 ({timeout_seconds}秒)")
                        break
                    else:
                        raise
            
            execution_time = (datetime.now() - start_time).total_seconds()

            # 分析stderr内容，区分错误和警告
            stderr_text = '\n'.join(stderr_lines) if stderr_lines else None
            warnings_text = None
            errors_text = None

            if stderr_text:
                warnings_lines = []
                error_lines = []

                for line in stderr_lines:
                    # 检查是否为真正的错误（包含错误关键字）
                    if any(keyword in line.lower() for keyword in ['traceback', 'error:', 'exception:', 'syntaxerror', 'nameerror', 'typeerror', 'valueerror', 'keyerror', 'indexerror', 'attributeerror']):
                        error_lines.append(line)
                    # 检查是否为警告
                    elif any(keyword in line.lower() for keyword in ['warning:', 'userwarning', 'deprecationwarning', 'futurewarning', 'runtimewarning']):
                        warnings_lines.append(line)
                    # 如果包含文件路径和行号的警告格式
                    elif ':' in line and 'warning' in line.lower():
                        warnings_lines.append(line)
                    else:
                        # 默认归类为错误（保守处理）
                        error_lines.append(line)

                # 设置警告和错误文本
                if warnings_lines:
                    warnings_text = '\n'.join(warnings_lines)
                if error_lines:
                    errors_text = '\n'.join(error_lines)

            # 判断执行是否成功（只有真正的错误才算失败）
            success = errors_text is None or len(errors_text.strip()) == 0

            result_obj = ExecutionResult(
                success=success,
                stdout='\n'.join(stdout_lines) if stdout_lines else None,
                stderr=errors_text,  # 只包含真正的错误
                warnings=warnings_text,  # 警告信息
                result=result,
                execution_time=execution_time,
                plots=plots
            )
            
            logger.info(f"代码执行完成: {task_id}, 成功: {success}, 耗时: {execution_time:.2f}秒")
            return result_obj
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"代码执行失败: {task_id}, 错误: {str(e)}")
            
            return ExecutionResult(
                success=False,
                stdout=None,
                stderr=str(e),
                result=None,
                execution_time=execution_time,
                plots=[]
            )
    
    async def _save_plot(self, task_id: str, image_data: str) -> str:
        """保存图表"""
        import base64
        import os

        # 创建任务特定的目录
        task_results_dir = os.path.join(settings.results_directory, task_id)
        os.makedirs(task_results_dir, exist_ok=True)

        # 分析代码推断图表类型
        chart_type = 'plot'
        if task_id in self.last_executed_code:
            chart_type = self._analyze_code_for_chart_type(self.last_executed_code[task_id])

        # 生成有意义的文件名
        plot_id = str(uuid.uuid4())[:8]
        filename = f"{chart_type}_{plot_id}.png"
        filepath = os.path.join(task_results_dir, filename)

        # 解码并保存图片
        image_bytes = base64.b64decode(image_data)
        with open(filepath, 'wb') as f:
            f.write(image_bytes)

        logger.info(f"图表保存成功: {filepath}")
        # 返回相对于静态文件服务的路径（包含任务ID文件夹）
        return f"{task_id}/{filename}"

    async def _save_plotly_chart(self, task_id: str, plotly_data: dict) -> Optional[str]:
        """保存plotly图表为PNG"""
        try:
            import json
            import base64
            import os

            # 创建任务特定的目录
            task_results_dir = os.path.join(settings.results_directory, task_id)
            os.makedirs(task_results_dir, exist_ok=True)

            # 分析代码推断图表类型
            chart_type = 'plotly'
            if task_id in self.last_executed_code:
                analyzed_type = self._analyze_code_for_chart_type(self.last_executed_code[task_id])
                chart_type = f"plotly_{analyzed_type}" if analyzed_type != 'plot' else 'plotly'

            # 生成有意义的文件名
            plot_id = str(uuid.uuid4())[:8]
            filename = f"{chart_type}_{plot_id}.png"
            filepath = os.path.join(task_results_dir, filename)

            # 使用kaleido转换plotly图表为PNG
            try:
                import plotly.graph_objects as go
                import plotly.io as pio

                # 从JSON数据创建图表对象
                if isinstance(plotly_data, str):
                    fig_dict = json.loads(plotly_data)
                else:
                    fig_dict = plotly_data

                # 创建plotly图表对象
                fig = go.Figure(fig_dict)

                # 转换为PNG字节
                img_bytes = fig.to_image(format="png", width=800, height=600)

                # 保存图片
                with open(filepath, 'wb') as f:
                    f.write(img_bytes)

                logger.info(f"Plotly图表保存成功: {filepath}")
                # 返回相对于静态文件服务的路径（包含任务ID文件夹）
                return f"{task_id}/{filename}"

            except ImportError as e:
                logger.warning(f"无法导入plotly或kaleido: {str(e)}")
                return None
            except Exception as e:
                logger.error(f"转换plotly图表失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"保存plotly图表失败: {str(e)}")
            return None

    def _analyze_code_for_chart_type(self, code: str) -> str:
        """分析代码推断图表类型"""
        import re

        code_lower = code.lower()

        # 定义图表类型匹配模式
        chart_patterns = {
            'histogram': [
                r'\.hist\s*\(',
                r'plt\.hist\s*\(',
                r'go\.histogram\s*\(',
                r'px\.histogram\s*\(',
                r'sns\.histplot\s*\('
            ],
            'scatter': [
                r'\.scatter\s*\(',
                r'plt\.scatter\s*\(',
                r'go\.scatter\s*\(',
                r'px\.scatter\s*\(',
                r'sns\.scatterplot\s*\('
            ],
            'bar': [
                r'\.bar\s*\(',
                r'plt\.bar\s*\(',
                r'go\.bar\s*\(',
                r'px\.bar\s*\(',
                r'sns\.barplot\s*\(',
                r'\.plot\s*\(.*kind\s*=\s*[\'"]bar[\'"]'
            ],
            'line': [
                r'\.plot\s*\(',
                r'plt\.plot\s*\(',
                r'go\.scatter.*mode.*line',
                r'px\.line\s*\(',
                r'sns\.lineplot\s*\('
            ],
            'boxplot': [
                r'\.boxplot\s*\(',
                r'plt\.boxplot\s*\(',
                r'go\.box\s*\(',
                r'px\.box\s*\(',
                r'sns\.boxplot\s*\('
            ],
            'heatmap': [
                r'sns\.heatmap\s*\(',
                r'go\.heatmap\s*\(',
                r'px\.imshow\s*\(',
                r'plt\.imshow\s*\('
            ],
            'violin': [
                r'\.violinplot\s*\(',
                r'plt\.violinplot\s*\(',
                r'go\.violin\s*\(',
                r'px\.violin\s*\(',
                r'sns\.violinplot\s*\('
            ],
            'pie': [
                r'\.pie\s*\(',
                r'plt\.pie\s*\(',
                r'go\.pie\s*\(',
                r'px\.pie\s*\('
            ]
        }

        # 按优先级检查图表类型
        for chart_type, patterns in chart_patterns.items():
            for pattern in patterns:
                if re.search(pattern, code_lower):
                    return chart_type

        # 默认返回通用类型
        return 'plot'
    
    async def shutdown_kernel(self, task_id: str) -> None:
        """关闭内核"""
        if task_id in self.kernels:
            try:
                kernel_manager = self.kernels[task_id]
                kernel_manager.shutdown_kernel()
                del self.kernels[task_id]
                logger.info(f"内核关闭成功: {task_id}")
            except Exception as e:
                logger.error(f"关闭内核失败: {task_id}, 错误: {str(e)}")
    
    async def shutdown_all_kernels(self) -> None:
        """关闭所有内核"""
        for task_id in list(self.kernels.keys()):
            await self.shutdown_kernel(task_id)


# 创建全局执行器实例
jupyter_executor = JupyterExecutor()
