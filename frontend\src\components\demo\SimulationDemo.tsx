import { useState, useEffect } from 'react';
import { ArrowRight, Brain, Code, BarChart3, CheckCircle2, AlertCircle, Lightbulb, User, Bot, Target, TrendingUp, Cpu, RefreshCw, Eye, Map, Database, Filter, Play, Pause, FileText, PieChart, Settings, MessageSquare, Search, Zap, Activity } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

interface AnalysisStep {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<any>;
  active: boolean;
  completed: boolean;
  progress: number;
  code?: string;
  result?: string;
}

export function SimulationDemo() {
  const [currentPhase, setCurrentPhase] = useState(0);
  const [currentStep, setCurrentStep] = useState(0);
  const [isPlaying, setIsPlaying] = useState(true);
  const [analysisSteps, setAnalysisSteps] = useState<AnalysisStep[]>([]);
  const [dataPoints, setDataPoints] = useState<{ x: number; y: number; value: number }[]>([]);
  const [codeOutput, setCodeOutput] = useState<string[]>([]);
  const [insights, setInsights] = useState<string[]>([]);
  const [showFinalResults, setShowFinalResults] = useState(false);
  const [aiThinking, setAiThinking] = useState('');
  const [chartData, setChartData] = useState<number[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // 定义AI数据分析的完整步骤
  const steps: AnalysisStep[] = [
    {
      id: 'data-understanding',
      title: '数据理解',
      description: '分析数据结构和质量',
      icon: Search,
      active: false,
      completed: false,
      progress: 0,
      code: 'import pandas as pd\ndf = pd.read_csv("sales_data.csv")\nprint(df.info())',
      result: '数据集包含10,000条销售记录，5个特征列'
    },
    {
      id: 'data-preparation',
      title: '数据预处理',
      description: '清洗和转换数据',
      icon: Settings,
      active: false,
      completed: false,
      progress: 0,
      code: 'df.dropna(inplace=True)\ndf["date"] = pd.to_datetime(df["date"])\nscaler = StandardScaler()',
      result: '处理缺失值，标准化数值特征'
    },
    {
      id: 'feature-engineering',
      title: '特征工程',
      description: '创建和选择关键特征',
      icon: Brain,
      active: false,
      completed: false,
      progress: 0,
      code: 'df["month"] = df["date"].dt.month\ndf["trend"] = df["sales"].rolling(7).mean()',
      result: '生成时间特征和趋势指标'
    },
    {
      id: 'modeling',
      title: '模型训练',
      description: '训练机器学习模型',
      icon: Cpu,
      active: false,
      completed: false,
      progress: 0,
      code: 'from sklearn.ensemble import RandomForestRegressor\nmodel = RandomForestRegressor(n_estimators=100)\nmodel.fit(X_train, y_train)',
      result: '模型训练完成，准确率：92.3%'
    },
    {
      id: 'evaluation',
      title: '模型评估',
      description: '评估模型性能',
      icon: BarChart3,
      active: false,
      completed: false,
      progress: 0,
      code: 'from sklearn.metrics import mean_squared_error\nmse = mean_squared_error(y_test, predictions)',
      result: 'MSE: 0.15, R²: 0.923'
    },
    {
      id: 'insights',
      title: '洞察生成',
      description: '生成业务洞察',
      icon: Lightbulb,
      active: false,
      completed: false,
      progress: 0,
      code: 'feature_importance = model.feature_importances_\ntop_features = get_top_features(feature_importance)',
      result: '发现关键影响因素：季节性(35%)，促销活动(28%)'
    }
  ];

  // AI思考过程
  const thinkingPhases = [
    '正在理解问题背景...',
    '分析数据分布特征...',
    '制定分析策略...',
    '选择最优算法...',
    '验证模型假设...',
    '解释结果含义...'
  ];

  // 初始化动画
  useEffect(() => {
    setAnalysisSteps(steps);
    
    // 生成模拟数据点
    const points = Array.from({ length: 50 }, (_, i) => ({
      x: Math.random() * 100,
      y: Math.random() * 100,
      value: Math.random() * 10
    }));
    setDataPoints(points);

    // 开始动画序列
    startAnalysisSequence();
  }, []);

  const startAnalysisSequence = async () => {
    setIsAnalyzing(true);
    setCurrentStep(-1); // 开始时没有步骤激活
    setCodeOutput([]);
    setInsights([]);
    setChartData([]);
    
    // 等待1秒再开始
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 逐步执行每个分析阶段，每个步骤持续3秒
    for (let i = 0; i < steps.length; i++) {
      // 激活当前步骤
      setCurrentStep(i);
      setAnalysisSteps(prev => prev.map((step, idx) => ({
        ...step,
        active: idx === i,
        completed: false,
        progress: 0
      })));

      // AI思考过程
      setAiThinking(thinkingPhases[i] || '正在分析...');
      
      // 1. 首先清空并显示当前步骤的代码（0.5秒后开始）
      setTimeout(() => {
        setCodeOutput([]); // 清空之前的代码
        if (steps[i].code) {
          const codeLines = steps[i].code!.split('\n');
          codeLines.forEach((line, lineIndex) => {
            setTimeout(() => {
              setCodeOutput(prev => [...prev, line]);
            }, lineIndex * 300);
          });
        }
      }, 500);

      // 2. 显示进度条动画（1秒后开始）
      setTimeout(() => {
        let progress = 0;
        const progressInterval = setInterval(() => {
          progress += 10;
          setAnalysisSteps(prev => prev.map((step, idx) => 
            idx === i ? { ...step, progress: Math.min(progress, 100) } : step
          ));
          if (progress >= 100) {
            clearInterval(progressInterval);
          }
        }, 100);
      }, 1000);

      // 3. 清空并显示当前步骤的结果（2秒后）
      setTimeout(() => {
        if (steps[i].result) {
          setInsights([steps[i].result!]); // 只显示当前步骤的结果
        }
        
        // 如果是模型训练阶段，生成图表数据
        if (i === 3) {
          const newData = Array.from({ length: 12 }, () => Math.random() * 100 + 20);
          setChartData(newData);
        }
      }, 2000);

      // 4. 完成当前步骤（2.5秒后）
      setTimeout(() => {
        setAnalysisSteps(prev => prev.map((step, idx) => 
          idx === i ? { ...step, completed: true, active: false, progress: 100 } : step
        ));
      }, 2500);

      // 等待3秒后进入下一步
      await new Promise(resolve => setTimeout(resolve, 3000));
    }

    // 显示最终结果
    setShowFinalResults(true);
    setIsAnalyzing(false);
    setAiThinking('分析完成！');

    // 8秒后重置动画
    setTimeout(() => {
      resetAnimation();
    }, 8000);
  };

  const resetAnimation = () => {
    setCurrentStep(0);
    setCurrentPhase(0);
    setCodeOutput([]);
    setInsights([]);
    setShowFinalResults(false);
    setChartData([]);
    setAnalysisSteps(steps.map(step => ({ 
      ...step, 
      active: false, 
      completed: false, 
      progress: 0 
    })));
    
    // 重新开始动画
    setTimeout(() => {
      startAnalysisSequence();
    }, 2000);
  };

  return (
    <div className="relative w-full h-[800px] overflow-hidden bg-gradient-to-br from-background via-background/95 to-primary/5 border border-border/30 rounded-3xl">
      {/* 科技风格背景 */}
      <div className="absolute inset-0">
        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.3)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.3)_1px,transparent_1px)] bg-[size:40px_40px]" />
        </div>
        
        {/* 动态数据点 */}
        <div className="absolute inset-0">
          {dataPoints.slice(0, 15).map((point, i) => (
            <div
              key={i}
              className="absolute w-1 h-1 bg-primary/30 rounded-full animate-pulse"
              style={{
                left: `${point.x}%`,
                top: `${point.y}%`,
                animationDelay: `${i * 200}ms`
              }}
            />
          ))}
        </div>
      </div>

      <div className="relative z-10 p-6 h-full flex flex-col justify-between">
        {/* 标题区域 */}
        <div className="text-center mb-6">
          <div className="mt-4 flex items-center justify-center gap-2">
            <Bot className="w-5 h-5 text-accent" />
            <span className="text-sm text-accent font-medium">{aiThinking}</span>
            {isAnalyzing && <Activity className="w-4 h-4 text-accent animate-pulse" />}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="flex-1 grid grid-cols-1 lg:grid-cols-3 gap-6 overflow-auto">
          {/* 左侧：分析步骤 */}
          <div className="lg:col-span-1">
            <Card className="h-[600px] bg-card/60 backdrop-blur-sm border-primary/30">
              <CardContent className="p-4">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold text-primary">分析流程</h3>
                  <p className="text-xs text-muted-foreground">AI执行步骤</p>
                </div>
                
                <div className="space-y-3">
                  {analysisSteps.slice(0, currentStep + 1).map((step, index) => (
                    <div
                      key={step.id}
                      className={`p-3 rounded-lg border transition-all duration-500 animate-fade-in ${
                        step.active 
                          ? 'bg-primary/10 border-primary/50 scale-105' 
                          : step.completed 
                          ? 'bg-success/5 border-success/30' 
                          : 'bg-muted/30 border-border/30'
                      }`}
                      style={{ animationDelay: `${index * 200}ms` }}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-colors ${
                          step.completed ? 'bg-success/20' : step.active ? 'bg-primary/20' : 'bg-muted/50'
                        }`}>
                          {step.completed ? (
                            <CheckCircle2 className="w-4 h-4 text-success" />
                          ) : (
                            <step.icon className={`w-4 h-4 ${step.active ? 'text-primary' : 'text-muted-foreground'}`} />
                          )}
                        </div>
                        <div className="flex-1 min-w-0">
                          <h4 className={`text-sm font-semibold ${step.active ? 'text-primary' : step.completed ? 'text-success' : 'text-foreground'}`}>
                            {step.title}
                          </h4>
                          <p className="text-xs text-muted-foreground">{step.description}</p>
                        </div>
                      </div>
                      
                      {step.active && step.progress > 0 && (
                        <div className="mt-2">
                          <div className="h-1 bg-muted rounded-full overflow-hidden">
                            <div 
                              className="h-full bg-primary transition-all duration-300"
                              style={{ width: `${step.progress}%` }}
                            />
                          </div>
                          <div className="text-xs text-primary mt-1">{step.progress}%</div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 中间：代码执行 */}
          <div className="lg:col-span-1">
            <Card className="h-[600px] bg-card/60 backdrop-blur-sm border-accent/30">
              <CardContent className="p-4">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold text-accent">代码执行</h3>
                  <p className="text-xs text-muted-foreground">实时代码生成</p>
                </div>
                
                {/* 代码输出窗口 */}
                <div className="bg-black/90 rounded-lg p-3 h-80 overflow-y-auto font-mono text-xs">
                  <div className="flex items-center gap-2 mb-2 pb-2 border-b border-gray-700">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-gray-400 ml-2">terminal</span>
                  </div>
                  
                  {codeOutput.map((line, i) => (
                    <div key={i} className="text-green-400 animate-fade-in" style={{ animationDelay: `${i * 100}ms` }}>
                      <span className="text-blue-400">{'>>>'} </span>{line}
                    </div>
                  ))}
                  
                  {isAnalyzing && (
                    <div className="flex items-center gap-1 mt-2">
                      <span className="text-green-400">{'>>>'} </span>
                      <div className="w-2 h-4 bg-green-400 animate-pulse"></div>
                    </div>
                  )}
                </div>

                {/* 图表区域 */}
                {chartData.length > 0 && (
                  <div className="mt-4 p-3 bg-background/50 rounded-lg">
                    <h4 className="text-sm font-semibold mb-2">模型性能</h4>
                    <div className="h-20 flex items-end justify-between gap-1">
                      {chartData.map((value, i) => (
                        <div
                          key={i}
                          className="bg-gradient-to-t from-accent to-accent/60 rounded-t transition-all duration-500"
                          style={{ 
                            height: `${value}%`,
                            animationDelay: `${i * 50}ms`
                          }}
                        />
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 右侧：结果与洞察 */}
          <div className="lg:col-span-1">
            <Card className="h-[600px] bg-card/60 backdrop-blur-sm border-success/30">
              <CardContent className="p-4">
                <div className="text-center mb-4">
                  <h3 className="text-lg font-bold text-success">分析结果</h3>
                  <p className="text-xs text-muted-foreground">智能洞察生成</p>
                </div>
                
                {/* 洞察列表 */}
                <div className="space-y-3">
                  {insights.map((insight, i) => (
                    <div key={i} className="p-3 bg-success/5 border border-success/20 rounded-lg animate-fade-in">
                      <div className="flex items-start gap-2">
                        <Lightbulb className="w-4 h-4 text-success mt-0.5 flex-shrink-0" />
                        <div>
                          <p className="text-xs text-muted-foreground mb-1">当前步骤结果</p>
                          <p className="text-sm text-foreground">{insight}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 最终结果摘要 */}
                {showFinalResults && (
                  <div className="mt-6 animate-fade-in">
                    <div className="p-4 bg-gradient-to-r from-success/10 to-accent/10 rounded-lg border border-success/30">
                      <h4 className="text-sm font-bold text-success mb-2 flex items-center gap-2">
                        <TrendingUp className="w-4 h-4" />
                        核心发现
                      </h4>
                      <ul className="space-y-1 text-xs">
                        <li>• 模型准确率达到 <span className="font-bold text-success">92.3%</span></li>
                        <li>• 季节性因素影响最大 (<span className="font-bold">35%</span>)</li>
                        <li>• 预测销售增长 <span className="font-bold text-accent">+23.5%</span></li>
                        <li>• 建议优化促销策略</li>
                      </ul>
                    </div>
                    
                    <div className="mt-4 text-center">
                      <Button 
                        size="sm" 
                        className="bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90"
                      >
                        <Target className="w-4 h-4 mr-2" />
                        查看详细报告
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* 底部状态栏 */}
        <div className="mt-auto flex items-center justify-between p-4 bg-background/60 rounded-xl border border-border/30 backdrop-blur-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <User className="w-5 h-5 text-primary" />
              <span className="text-sm text-muted-foreground">数据科学家</span>
            </div>
            <ArrowRight className="w-4 h-4 text-muted-foreground" />
            <div className="flex items-center gap-2">
              <Bot className="w-5 h-5 text-accent" />
              <span className="text-sm text-muted-foreground">AI助手</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <Badge variant="outline" className="text-xs">
              第 {currentStep + 1} / {steps.length} 步
            </Badge>
            {isAnalyzing && (
              <div className="flex gap-1">
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse"></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse" style={{ animationDelay: '200ms' }}></div>
                <div className="w-1 h-1 bg-primary rounded-full animate-pulse" style={{ animationDelay: '400ms' }}></div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}