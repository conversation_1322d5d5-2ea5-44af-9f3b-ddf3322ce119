"""SSE (Server-Sent Events) 流式传输支持"""

import json
import asyncio
from datetime import datetime
from typing import Dict, Any, AsyncGenerator, Optional
from fastapi import Request
from fastapi.responses import StreamingResponse
from src.utils.logger import get_logger

logger = get_logger(__name__)


class SSEManager:
    """SSE连接管理器"""
    
    def __init__(self):
        self.active_streams: Dict[str, Dict[str, Any]] = {}
    
    def create_stream(self, task_id: str, stream_id: str) -> None:
        """创建新的SSE流"""
        if task_id not in self.active_streams:
            self.active_streams[task_id] = {}
        
        self.active_streams[task_id][stream_id] = {
            "queue": asyncio.Queue(),
            "active": True
        }
        logger.info(f"[SSE] 创建流 - 任务ID: {task_id}, 流ID: {stream_id}")
    
    def close_stream(self, task_id: str, stream_id: str) -> None:
        """关闭SSE流"""
        if task_id in self.active_streams and stream_id in self.active_streams[task_id]:
            self.active_streams[task_id][stream_id]["active"] = False
            logger.info(f"[SSE] 关闭流 - 任务ID: {task_id}, 流ID: {stream_id}")
    
    async def send_data(self, task_id: str, data: Dict[str, Any]) -> None:
        """向任务的所有活跃流发送数据"""
        if task_id not in self.active_streams:
            return
        
        for stream_id, stream_info in self.active_streams[task_id].items():
            if stream_info["active"]:
                try:
                    await stream_info["queue"].put(data)
                except Exception as e:
                    logger.error(f"[SSE] 发送数据失败 - 任务ID: {task_id}, 流ID: {stream_id}, 错误: {str(e)}")
    
    async def get_stream_generator(self, task_id: str, stream_id: str, request: Request) -> AsyncGenerator[str, None]:
        """获取SSE流生成器"""
        try:
            if task_id not in self.active_streams or stream_id not in self.active_streams[task_id]:
                yield f"data: {json.dumps({'error': 'Stream not found'})}\n\n"
                return
            
            stream_info = self.active_streams[task_id][stream_id]
            queue = stream_info["queue"]
            
            # 发送连接确认
            yield f"data: {json.dumps({'type': 'connected', 'task_id': task_id, 'stream_id': stream_id})}\n\n"
            
            while stream_info["active"]:
                # 检查客户端是否断开连接
                if await request.is_disconnected():
                    logger.info(f"[SSE] 客户端断开连接 - 任务ID: {task_id}, 流ID: {stream_id}")
                    break
                
                try:
                    # 等待数据，设置超时以便定期检查连接状态
                    data = await asyncio.wait_for(queue.get(), timeout=5.0)
                    
                    # 格式化SSE数据
                    sse_data = self._format_sse_data(data)
                    yield sse_data
                    
                    # 标记任务完成
                    queue.task_done()
                    
                except asyncio.TimeoutError:
                    # 发送心跳
                    yield f"data: {json.dumps({'type': 'heartbeat', 'timestamp': asyncio.get_event_loop().time()})}\n\n"
                    continue
                except Exception as e:
                    logger.error(f"[SSE] 流处理错误 - 任务ID: {task_id}, 流ID: {stream_id}, 错误: {str(e)}")
                    yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
                    break
            
            # 发送流结束信号
            yield f"data: {json.dumps({'type': 'stream_end', 'task_id': task_id})}\n\n"
            
        except Exception as e:
            logger.error(f"[SSE] 流生成器错误 - 任务ID: {task_id}, 流ID: {stream_id}, 错误: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': str(e)})}\n\n"
        finally:
            # 清理流
            self.close_stream(task_id, stream_id)
    
    def _format_sse_data(self, data: Dict[str, Any]) -> str:
        """格式化SSE数据"""
        try:
            json_data = json.dumps(data, ensure_ascii=False)
            return f"data: {json_data}\n\n"
        except Exception as e:
            logger.error(f"[SSE] 数据格式化失败: {str(e)}")
            return f"data: {json.dumps({'type': 'error', 'message': 'Data formatting failed'})}\n\n"
    
    def cleanup_task_streams(self, task_id: str) -> None:
        """清理任务的所有流"""
        if task_id in self.active_streams:
            for stream_id in list(self.active_streams[task_id].keys()):
                self.close_stream(task_id, stream_id)
            del self.active_streams[task_id]
            logger.info(f"[SSE] 清理任务流 - 任务ID: {task_id}")


# 全局SSE管理器实例
sse_manager = SSEManager()


async def create_sse_response(task_id: str, stream_id: str, request: Request) -> StreamingResponse:
    """创建SSE响应"""
    # 创建流
    sse_manager.create_stream(task_id, stream_id)
    
    # 创建流生成器
    generator = sse_manager.get_stream_generator(task_id, stream_id, request)
    
    # 返回StreamingResponse
    return StreamingResponse(
        generator,
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        }
    )


class ReportStreamManager:
    """报告生成流管理器"""

    def __init__(self):
        self.report_streams: Dict[str, Dict[str, Any]] = {}
        self.report_states: Dict[str, Dict] = {}  # 存储报告状态
    
    async def start_report_stream(self, task_id: str) -> None:
        """开始报告生成流"""
        stream_id = f"report_{task_id}"
        sse_manager.create_stream(task_id, stream_id)
        
        self.report_streams[task_id] = {
            "stream_id": stream_id,
            "current_section": None,
            "sections_completed": [],
            "started_at": asyncio.get_event_loop().time()
        }
        
        # 发送开始信号
        await sse_manager.send_data(task_id, {
            "type": "report_start",
            "task_id": task_id,
            "message": "开始生成数据分析报告"
        })
    
    async def send_section_start(self, task_id: str, section: str, section_name: str) -> None:
        """发送章节开始信号"""
        if task_id in self.report_streams:
            self.report_streams[task_id]["current_section"] = section
            
            await sse_manager.send_data(task_id, {
                "type": "section_start",
                "task_id": task_id,
                "section": section,
                "section_name": section_name,
                "message": f"开始生成: {section_name}"
            })
    
    async def send_section_content(self, task_id: str, section: str, content: str, is_partial: bool = False) -> None:
        """发送章节内容"""
        # 添加调试日志
        content_preview = content[:100] + "..." if len(content) > 100 else content
        logger.debug(f"[SSE] 发送章节内容 - 任务ID: {task_id}, 章节: {section}, 部分内容: {is_partial}, 长度: {len(content)}")
        logger.debug(f"[SSE] 内容预览: {content_preview}")

        await sse_manager.send_data(task_id, {
            "type": "section_content",
            "task_id": task_id,
            "section": section,
            "content": content,
            "is_partial": is_partial,
            "timestamp": datetime.now().isoformat()
        })

        # 更新状态
        self._update_section_state(task_id, section, content, is_partial)
    
    async def send_section_complete(self, task_id: str, section: str, section_name: str) -> None:
        """发送章节完成信号"""
        if task_id in self.report_streams:
            self.report_streams[task_id]["sections_completed"].append(section)
            self.report_streams[task_id]["current_section"] = None
            
            await sse_manager.send_data(task_id, {
                "type": "section_complete",
                "task_id": task_id,
                "section": section,
                "section_name": section_name,
                "message": f"完成生成: {section_name}"
            })
    
    async def send_report_complete(self, task_id: str, report_path: str) -> None:
        """发送报告完成信号"""
        if task_id in self.report_streams:
            completed_time = asyncio.get_event_loop().time()
            start_time = self.report_streams[task_id]["started_at"]
            duration = completed_time - start_time
            
            await sse_manager.send_data(task_id, {
                "type": "report_complete",
                "task_id": task_id,
                "report_path": report_path,
                "duration": duration,
                "sections_count": len(self.report_streams[task_id]["sections_completed"]),
                "message": "数据分析报告生成完成"
            })
            
            # 清理流
            del self.report_streams[task_id]
    
    async def send_error(self, task_id: str, error_message: str) -> None:
        """发送错误信息"""
        await sse_manager.send_data(task_id, {
            "type": "report_error",
            "task_id": task_id,
            "error": error_message,
            "message": f"报告生成出错: {error_message}"
        })

    def _update_section_state(self, task_id: str, section: str, content: str, is_partial: bool):
        """更新章节状态"""
        if task_id not in self.report_states:
            self.report_states[task_id] = {
                "overall_status": "generating",
                "progress": 0,
                "sections": {},
                "current_section": section,
                "report_path": None
            }

        # 更新章节内容和状态
        if section not in self.report_states[task_id]["sections"]:
            self.report_states[task_id]["sections"][section] = {}

        self.report_states[task_id]["sections"][section]["content"] = content
        self.report_states[task_id]["sections"][section]["status"] = "generating" if is_partial else "complete"
        self.report_states[task_id]["current_section"] = section if is_partial else None

        # 计算进度
        total_sections = 7  # 总章节数
        completed_sections = sum(1 for s in self.report_states[task_id]["sections"].values()
                               if s.get("status") == "complete")
        self.report_states[task_id]["progress"] = (completed_sections / total_sections) * 100

    def get_report_status(self, task_id: str) -> Optional[Dict]:
        """获取报告状态"""
        return self.report_states.get(task_id)

    def set_report_complete(self, task_id: str, report_path: str):
        """设置报告完成"""
        if task_id in self.report_states:
            self.report_states[task_id]["overall_status"] = "complete"
            self.report_states[task_id]["progress"] = 100
            self.report_states[task_id]["current_section"] = None
            self.report_states[task_id]["report_path"] = report_path

    # 反思相关方法
    async def send_reflection_start(self, task_id: str):
        """发送反思开始信号"""
        await sse_manager.send_data(task_id, {
            "type": "reflection_start",
            "task_id": task_id,
            "message": "开始反思审查流程",
            "timestamp": asyncio.get_event_loop().time()
        })
        logger.info(f"[SSE] 发送反思开始信号 - 任务ID: {task_id}")

    async def send_evaluation_result(self, task_id: str, evaluation: Dict[str, Any]):
        """发送评估结果"""
        await sse_manager.send_data(task_id, {
            "type": "evaluation_result",
            "task_id": task_id,
            "evaluation": evaluation,
            "message": f"报告评估完成，总体评分: {evaluation.get('overall_score', 'N/A')}",
            "timestamp": asyncio.get_event_loop().time()
        })
        logger.info(f"[SSE] 发送评估结果 - 任务ID: {task_id}")

    async def send_reflection_result(self, task_id: str, reflection: Dict[str, Any]):
        """发送反思结果"""
        await sse_manager.send_data(task_id, {
            "type": "reflection_result",
            "task_id": task_id,
            "reflection": reflection,
            "message": "反思分析完成，生成改进建议",
            "timestamp": asyncio.get_event_loop().time()
        })
        logger.info(f"[SSE] 发送反思结果 - 任务ID: {task_id}")

    async def send_regeneration_start(self, task_id: str):
        """发送重新生成开始信号"""
        await sse_manager.send_data(task_id, {
            "type": "regeneration_start",
            "task_id": task_id,
            "message": "开始重新生成改进报告",
            "timestamp": asyncio.get_event_loop().time()
        })
        logger.info(f"[SSE] 发送重新生成开始信号 - 任务ID: {task_id}")

    async def send_regeneration_progress(self, task_id: str, section_name: str):
        """发送重新生成进度"""
        await sse_manager.send_data(task_id, {
            "type": "regeneration_progress",
            "task_id": task_id,
            "section_name": section_name,
            "message": f"正在重新生成: {section_name}",
            "timestamp": asyncio.get_event_loop().time()
        })
        logger.info(f"[SSE] 发送重新生成进度 - 任务ID: {task_id}, 部分: {section_name}")

    async def send_reflection_complete(self, task_id: str, report_path: str):
        """发送反思完成信号"""
        await sse_manager.send_data(task_id, {
            "type": "reflection_complete",
            "task_id": task_id,
            "report_path": report_path,
            "message": "反思审查完成，改进报告已生成",
            "timestamp": asyncio.get_event_loop().time()
        })

        # 更新报告状态
        if task_id in self.report_states:
            self.report_states[task_id]["reflection_complete"] = True
            self.report_states[task_id]["improved_report_path"] = report_path

        logger.info(f"[SSE] 发送反思完成信号 - 任务ID: {task_id}")

    async def send_error(self, task_id: str, error_msg: str):
        """发送错误信号"""
        await sse_manager.send_data(task_id, {
            "type": "error",
            "task_id": task_id,
            "error": error_msg,
            "message": f"错误: {error_msg}",
            "timestamp": asyncio.get_event_loop().time()
        })
        logger.error(f"[SSE] 发送错误信号 - 任务ID: {task_id}, 错误: {error_msg}")


# 全局报告流管理器实例
report_stream_manager = ReportStreamManager()
