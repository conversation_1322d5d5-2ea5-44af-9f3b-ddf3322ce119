import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Folder, 
  FolderOpen, 
  FileImage, 
  FileText, 
  Download, 
  Eye,
  X,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { apiService } from '@/lib/api';

interface FileItem {
  name: string;
  path: string;
  type: 'image' | 'markdown' | 'html' | 'text' | 'other';
  size: number;
  created: number;
  modified: number;
}

interface FileBrowserProps {
  taskId: string;
  isOpen: boolean;
  onClose: () => void;
}

const FileBrowser: React.FC<FileBrowserProps> = ({ taskId, isOpen, onClose }) => {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [previewFile, setPreviewFile] = useState<FileItem | null>(null);
  const [previewContent, setPreviewContent] = useState<string>('');
  const [previewLoading, setPreviewLoading] = useState(false);

  // 获取文件列表
  const fetchFiles = async () => {
    if (!taskId) return;
    
    setLoading(true);
    try {
      const response = await apiService.getTaskFiles(taskId);
      setFiles(response.files || []);
    } catch (error) {
      console.error('获取文件列表失败:', error);
      toast.error('获取文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 预览文件
  const handlePreview = async (file: FileItem) => {
    setPreviewFile(file);
    setPreviewLoading(true);

    try {
      if (file.type === 'image') {
        // 图片文件使用API服务的基础URL
        const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
        const imageUrl = `${apiBaseUrl}/api/v1/tasks/${taskId}/files/${encodeURIComponent(file.name)}`;
        console.log('图片URL:', imageUrl);
        setPreviewContent(imageUrl);
      } else {
        // 文本文件获取内容
        const content = await apiService.getFileContent(taskId, file.name);
        setPreviewContent(content);
      }
    } catch (error) {
      console.error('预览文件失败:', error);
      toast.error('预览文件失败');
      setPreviewFile(null);
    } finally {
      setPreviewLoading(false);
    }
  };

  // 下载文件
  const handleDownload = async (file: FileItem) => {
    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
      const url = `${apiBaseUrl}/api/v1/tasks/${taskId}/files/${encodeURIComponent(file.name)}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = file.name;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('文件下载已开始');
    } catch (error) {
      console.error('下载文件失败:', error);
      toast.error('下载文件失败');
    }
  };

  // 获取文件图标
  const getFileIcon = (type: string) => {
    switch (type) {
      case 'image':
        return <FileImage className="w-4 h-4 text-green-500" />;
      case 'markdown':
      case 'html':
      case 'text':
        return <FileText className="w-4 h-4 text-blue-500" />;
      default:
        return <FileText className="w-4 h-4 text-gray-500" />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 获取文件类型标签颜色
  const getTypeColor = (type: string) => {
    switch (type) {
      case 'image':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'markdown':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'html':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'text':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchFiles();
    }
  }, [isOpen, taskId]);

  return (
    <>
      {/* 文件列表对话框 */}
      <Dialog open={isOpen && !previewFile} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl max-h-[80vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FolderOpen className="w-5 h-5 text-primary" />
              任务文件 ({files.length} 个文件)
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex flex-col h-[60vh]">
            {loading ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  加载文件列表...
                </div>
              </div>
            ) : files.length === 0 ? (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Folder className="w-12 h-12 mx-auto mb-2 opacity-50" />
                  <p>暂无文件</p>
                </div>
              </div>
            ) : (
              <ScrollArea className="flex-1">
                <div className="space-y-2">
                  {files.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 rounded-lg border bg-card hover:bg-accent/50 transition-colors"
                    >
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        {getFileIcon(file.type)}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{file.name}</span>
                            <Badge variant="outline" className={getTypeColor(file.type)}>
                              {file.type}
                            </Badge>
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {formatFileSize(file.size)} • {new Date(file.created * 1000).toLocaleString()}
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handlePreview(file)}
                          className="h-8 w-8 p-0"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDownload(file)}
                          className="h-8 w-8 p-0"
                        >
                          <Download className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* 文件预览对话框 */}
      <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
        <DialogContent className="max-w-6xl max-h-[90vh]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {previewFile && getFileIcon(previewFile.type)}
              {previewFile?.name}
            </DialogTitle>
          </DialogHeader>
          
          <div className="flex-1 overflow-hidden">
            {previewLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="flex items-center gap-2 text-muted-foreground">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  加载预览...
                </div>
              </div>
            ) : previewFile?.type === 'image' ? (
              <div className="flex justify-center p-4">
                <img
                  src={previewContent}
                  alt={previewFile.name}
                  className="max-w-full max-h-[70vh] object-contain rounded-lg shadow-lg"
                  onError={(e) => {
                    console.error('图片加载失败:', previewContent);
                    toast.error('图片加载失败');
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                  }}
                  onLoad={() => {
                    console.log('图片加载成功:', previewContent);
                  }}
                />
              </div>
            ) : (
              <ScrollArea className="h-[70vh]">
                <pre className="p-4 text-sm bg-muted rounded-lg whitespace-pre-wrap">
                  {previewContent}
                </pre>
              </ScrollArea>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default FileBrowser;
