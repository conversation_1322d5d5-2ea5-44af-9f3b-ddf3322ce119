"""
高级数据分析智能体
利用最新的机器学习和数据分析库进行深度分析
"""

import logging
from typing import Dict, Any, List, Optional
import pandas as pd
import polars as pl
import numpy as np
from datetime import datetime

# 机器学习库
import xgboost as xgb
import lightgbm as lgb
import catboost as cb
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.preprocessing import StandardScaler, LabelEncoder

# 统计分析库
import scipy.stats as stats
import statsmodels.api as sm
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.arima.model import ARIMA

# 时间序列预测库
try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False

try:
    from darts import TimeSeries
    from darts.models import ExponentialSmoothing, ARIMA as DartsARIMA
    DARTS_AVAILABLE = True
except ImportError:
    DARTS_AVAILABLE = False

from .base_agent import BaseAgent

logger = logging.getLogger(__name__)


class AdvancedAnalysisAgent(BaseAgent):
    """高级数据分析智能体"""
    
    def __init__(self):
        super().__init__(
            name="AdvancedAnalysisAgent",
            description="使用最新机器学习和统计分析库进行高级数据分析"
        )
        
    async def execute(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """执行高级数据分析"""
        try:
            logger.info(f"{self.name} 开始执行高级数据分析")
            
            # 获取数据
            df = state.get('cleaned_data')
            if df is None:
                raise ValueError("未找到清洗后的数据")
            
            analysis_results = {}
            
            # 1. 使用Polars进行高性能数据处理
            polars_results = await self._polars_analysis(df)
            analysis_results['polars_analysis'] = polars_results
            
            # 2. 高级统计分析
            stats_results = await self._advanced_statistics(df)
            analysis_results['statistical_analysis'] = stats_results
            
            # 3. 机器学习模型比较
            if self._has_numeric_target(df):
                ml_results = await self._machine_learning_comparison(df)
                analysis_results['ml_comparison'] = ml_results
            
            # 4. 时间序列分析（如果有时间列）
            if self._has_time_column(df):
                ts_results = await self._time_series_analysis(df)
                analysis_results['time_series'] = ts_results
            
            # 5. 异常检测
            anomaly_results = await self._anomaly_detection(df)
            analysis_results['anomaly_detection'] = anomaly_results
            
            # 更新状态
            state['advanced_analysis'] = analysis_results
            
            logger.info(f"{self.name} 高级数据分析完成")
            return state
            
        except Exception as e:
            logger.error(f"{self.name} 执行失败: {str(e)}")
            raise
    
    async def _polars_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """使用Polars进行高性能数据分析"""
        try:
            # 转换为Polars DataFrame
            pl_df = pl.from_pandas(df)
            
            results = {
                'shape': pl_df.shape,
                'memory_usage': pl_df.estimated_size('mb'),
                'column_stats': {},
                'performance_comparison': {}
            }
            
            # 数值列统计
            numeric_cols = pl_df.select(pl.col(pl.NUMERIC_DTYPES)).columns
            if numeric_cols:
                stats_df = pl_df.select([
                    pl.col(col).mean().alias(f"{col}_mean") for col in numeric_cols
                ] + [
                    pl.col(col).std().alias(f"{col}_std") for col in numeric_cols
                ] + [
                    pl.col(col).median().alias(f"{col}_median") for col in numeric_cols
                ])
                
                results['column_stats'] = stats_df.to_dict(as_series=False)
            
            # 性能对比示例
            import time
            
            # Pandas操作
            start_time = time.time()
            pandas_result = df.groupby(df.columns[0] if len(df.columns) > 0 else df.index).size()
            pandas_time = time.time() - start_time
            
            # Polars操作
            start_time = time.time()
            if len(pl_df.columns) > 0:
                polars_result = pl_df.group_by(pl_df.columns[0]).len()
            polars_time = time.time() - start_time
            
            results['performance_comparison'] = {
                'pandas_time': pandas_time,
                'polars_time': polars_time,
                'speedup': pandas_time / polars_time if polars_time > 0 else 1
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Polars分析失败: {str(e)}")
            return {'error': str(e)}
    
    async def _advanced_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """高级统计分析"""
        try:
            results = {}
            
            # 数值列
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            
            if len(numeric_cols) >= 2:
                # 相关性分析
                corr_matrix = df[numeric_cols].corr()
                results['correlation_analysis'] = {
                    'correlation_matrix': corr_matrix.to_dict(),
                    'high_correlations': self._find_high_correlations(corr_matrix)
                }
                
                # 主成分分析
                from sklearn.decomposition import PCA
                pca = PCA()
                pca_result = pca.fit_transform(df[numeric_cols].fillna(0))
                results['pca_analysis'] = {
                    'explained_variance_ratio': pca.explained_variance_ratio_.tolist(),
                    'cumulative_variance': np.cumsum(pca.explained_variance_ratio_).tolist()
                }
                
                # 统计检验
                if len(numeric_cols) >= 2:
                    col1, col2 = numeric_cols[0], numeric_cols[1]
                    data1 = df[col1].dropna()
                    data2 = df[col2].dropna()
                    
                    # 正态性检验
                    shapiro_stat1, shapiro_p1 = stats.shapiro(data1.sample(min(5000, len(data1))))
                    shapiro_stat2, shapiro_p2 = stats.shapiro(data2.sample(min(5000, len(data2))))
                    
                    # 相关性检验
                    pearson_corr, pearson_p = stats.pearsonr(data1, data2)
                    spearman_corr, spearman_p = stats.spearmanr(data1, data2)
                    
                    results['statistical_tests'] = {
                        'normality_tests': {
                            col1: {'statistic': shapiro_stat1, 'p_value': shapiro_p1},
                            col2: {'statistic': shapiro_stat2, 'p_value': shapiro_p2}
                        },
                        'correlation_tests': {
                            'pearson': {'correlation': pearson_corr, 'p_value': pearson_p},
                            'spearman': {'correlation': spearman_corr, 'p_value': spearman_p}
                        }
                    }
            
            return results
            
        except Exception as e:
            logger.error(f"高级统计分析失败: {str(e)}")
            return {'error': str(e)}
    
    async def _machine_learning_comparison(self, df: pd.DataFrame) -> Dict[str, Any]:
        """机器学习模型比较"""
        try:
            results = {}
            
            # 准备数据
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            if len(numeric_cols) < 2:
                return {'error': '数值列不足，无法进行机器学习分析'}
            
            # 选择目标变量（最后一个数值列）
            target_col = numeric_cols[-1]
            feature_cols = numeric_cols[:-1]
            
            # 准备训练数据
            X = df[feature_cols].fillna(0)
            y = df[target_col].fillna(y.mean())
            
            if len(X) < 10:
                return {'error': '数据量不足，无法进行机器学习分析'}
            
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # 标准化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            models = {}
            
            # XGBoost
            try:
                xgb_model = xgb.XGBRegressor(random_state=42, n_estimators=100)
                xgb_model.fit(X_train, y_train)
                xgb_pred = xgb_model.predict(X_test)
                models['XGBoost'] = {
                    'mse': mean_squared_error(y_test, xgb_pred),
                    'mae': mean_absolute_error(y_test, xgb_pred),
                    'r2': r2_score(y_test, xgb_pred),
                    'feature_importance': dict(zip(feature_cols, xgb_model.feature_importances_))
                }
            except Exception as e:
                models['XGBoost'] = {'error': str(e)}
            
            # LightGBM
            try:
                lgb_model = lgb.LGBMRegressor(random_state=42, n_estimators=100, verbose=-1)
                lgb_model.fit(X_train, y_train)
                lgb_pred = lgb_model.predict(X_test)
                models['LightGBM'] = {
                    'mse': mean_squared_error(y_test, lgb_pred),
                    'mae': mean_absolute_error(y_test, lgb_pred),
                    'r2': r2_score(y_test, lgb_pred),
                    'feature_importance': dict(zip(feature_cols, lgb_model.feature_importances_))
                }
            except Exception as e:
                models['LightGBM'] = {'error': str(e)}
            
            # CatBoost
            try:
                cb_model = cb.CatBoostRegressor(random_state=42, iterations=100, verbose=False)
                cb_model.fit(X_train, y_train)
                cb_pred = cb_model.predict(X_test)
                models['CatBoost'] = {
                    'mse': mean_squared_error(y_test, cb_pred),
                    'mae': mean_absolute_error(y_test, cb_pred),
                    'r2': r2_score(y_test, cb_pred),
                    'feature_importance': dict(zip(feature_cols, cb_model.feature_importances_))
                }
            except Exception as e:
                models['CatBoost'] = {'error': str(e)}
            
            results['model_comparison'] = models
            results['best_model'] = self._find_best_model(models)
            
            return results
            
        except Exception as e:
            logger.error(f"机器学习比较失败: {str(e)}")
            return {'error': str(e)}
    
    def _find_high_correlations(self, corr_matrix: pd.DataFrame, threshold: float = 0.7) -> List[Dict]:
        """找出高相关性的变量对"""
        high_corrs = []
        for i in range(len(corr_matrix.columns)):
            for j in range(i+1, len(corr_matrix.columns)):
                corr_val = corr_matrix.iloc[i, j]
                if abs(corr_val) > threshold:
                    high_corrs.append({
                        'var1': corr_matrix.columns[i],
                        'var2': corr_matrix.columns[j],
                        'correlation': corr_val
                    })
        return high_corrs
    
    def _find_best_model(self, models: Dict) -> Optional[str]:
        """找出最佳模型"""
        best_model = None
        best_r2 = -float('inf')
        
        for model_name, metrics in models.items():
            if 'error' not in metrics and 'r2' in metrics:
                if metrics['r2'] > best_r2:
                    best_r2 = metrics['r2']
                    best_model = model_name
        
        return best_model
    
    def _has_numeric_target(self, df: pd.DataFrame) -> bool:
        """检查是否有数值目标变量"""
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        return len(numeric_cols) >= 2
    
    def _has_time_column(self, df: pd.DataFrame) -> bool:
        """检查是否有时间列"""
        time_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        return len(time_cols) > 0
    
    async def _time_series_analysis(self, df: pd.DataFrame) -> Dict[str, Any]:
        """时间序列分析"""
        # 这里可以添加Prophet和Darts的时间序列分析
        return {'message': '时间序列分析功能待实现'}
    
    async def _anomaly_detection(self, df: pd.DataFrame) -> Dict[str, Any]:
        """异常检测"""
        try:
            from sklearn.ensemble import IsolationForest
            
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            if not numeric_cols:
                return {'error': '没有数值列进行异常检测'}
            
            # 使用Isolation Forest进行异常检测
            X = df[numeric_cols].fillna(0)
            
            iso_forest = IsolationForest(contamination=0.1, random_state=42)
            anomalies = iso_forest.fit_predict(X)
            
            anomaly_count = (anomalies == -1).sum()
            anomaly_ratio = anomaly_count / len(df)
            
            return {
                'total_anomalies': int(anomaly_count),
                'anomaly_ratio': float(anomaly_ratio),
                'anomaly_indices': np.where(anomalies == -1)[0].tolist()
            }
            
        except Exception as e:
            logger.error(f"异常检测失败: {str(e)}")
            return {'error': str(e)}
