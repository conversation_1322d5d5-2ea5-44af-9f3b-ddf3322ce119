"""ChromaDB客户端"""

import chromadb
from typing import List, Dict, Any, Optional
from datetime import datetime
from src.config import settings
from src.utils.logger import get_logger
from src.models.state_models import ChromaDocument

logger = get_logger(__name__)


class ChromaDBClient:
    """ChromaDB客户端类"""
    
    def __init__(self):
        """初始化ChromaDB客户端"""
        self.client = None
        self._initialize_client()
    
    def _initialize_client(self):
        """初始化ChromaDB客户端"""
        try:
            # 使用持久化客户端
            self.client = chromadb.PersistentClient(
                path=settings.chroma_persist_directory
            )
            logger.info(f"ChromaDB客户端初始化成功，数据目录: {settings.chroma_persist_directory}")
        except Exception as e:
            logger.error(f"ChromaDB客户端初始化失败: {str(e)}")
            raise
    
    def create_collection(self, collection_name: str) -> chromadb.Collection:
        """创建或获取集合"""
        try:
            collection = self.client.get_or_create_collection(
                name=collection_name,
                metadata={"created_at": datetime.now().isoformat()}
            )
            logger.info(f"集合创建/获取成功: {collection_name}")
            return collection
        except Exception as e:
            logger.error(f"创建集合失败: {collection_name}, 错误: {str(e)}")
            raise
    
    def add_document(
        self, 
        collection_name: str, 
        document: ChromaDocument
    ) -> None:
        """添加文档到集合"""
        try:
            collection = self.create_collection(collection_name)
            
            # 生成文档ID
            doc_id = f"{document['task_id']}_{document['step']}_{document['type']}_{datetime.now().timestamp()}"
            
            collection.add(
                ids=[doc_id],
                documents=[document['content']],
                metadatas=[{
                    "task_id": document['task_id'],
                    "step": document['step'],
                    "type": document['type'],
                    "timestamp": document['timestamp'].isoformat(),
                    **document['metadata']
                }]
            )
            
            logger.info(f"文档添加成功: {doc_id}")
            
        except Exception as e:
            logger.error(f"添加文档失败: {str(e)}")
            raise
    
    def search_documents(
        self, 
        collection_name: str, 
        query: str, 
        n_results: int = 5,
        where: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            collection = self.client.get_collection(collection_name)
            
            results = collection.query(
                query_texts=[query],
                n_results=n_results,
                where=where
            )
            
            # 格式化结果
            formatted_results = []
            for i in range(len(results['ids'][0])):
                formatted_results.append({
                    'id': results['ids'][0][i],
                    'document': results['documents'][0][i],
                    'metadata': results['metadatas'][0][i],
                    'distance': results['distances'][0][i] if 'distances' in results else None
                })
            
            logger.info(f"搜索完成，找到 {len(formatted_results)} 个结果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"搜索文档失败: {str(e)}")
            return []
    
    def get_task_documents(
        self, 
        collection_name: str, 
        task_id: str
    ) -> List[Dict[str, Any]]:
        """获取特定任务的所有文档"""
        try:
            collection = self.client.get_collection(collection_name)
            
            results = collection.get(
                where={"task_id": task_id}
            )
            
            # 格式化结果
            formatted_results = []
            for i in range(len(results['ids'])):
                formatted_results.append({
                    'id': results['ids'][i],
                    'document': results['documents'][i],
                    'metadata': results['metadatas'][i]
                })
            
            logger.info(f"获取任务文档完成: {task_id}, 共 {len(formatted_results)} 个文档")
            return formatted_results
            
        except Exception as e:
            logger.error(f"获取任务文档失败: {task_id}, 错误: {str(e)}")
            return []
    
    def delete_collection(self, collection_name: str) -> None:
        """删除集合"""
        try:
            self.client.delete_collection(collection_name)
            logger.info(f"集合删除成功: {collection_name}")
        except Exception as e:
            logger.error(f"删除集合失败: {collection_name}, 错误: {str(e)}")
            raise


# 创建全局ChromaDB客户端实例
chroma_client = ChromaDBClient()
