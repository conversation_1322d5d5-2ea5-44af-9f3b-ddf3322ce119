import { useState } from 'react';
import { FileText, Download, Eye, EyeOff, Co<PERSON> } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { ResultCard } from '@/types/analysis';
import { cn } from '@/lib/utils';

interface ReportCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

interface ReportData {
  title: string;
  summary: string;
  sections: Array<{
    title: string;
    content: string;
    type: 'text' | 'code' | 'insights';
  }>;
  conclusions: string[];
  recommendations: string[];
  metadata: {
    generated_at: string;
    word_count: number;
    analysis_duration: string;
  };
}

export function ReportCard({ card, isHighlighted }: ReportCardProps) {
  const [expanded, setExpanded] = useState(false);
  const [copied, setCopied] = useState(false);
  const data = card.content as ReportData;

  const handleCopy = async () => {
    const reportText = [
      data.title,
      '',
      'SUMMARY',
      data.summary,
      '',
      ...data.sections.flatMap(section => [
        section.title.toUpperCase(),
        section.content,
        ''
      ]),
      'CONCLUSIONS',
      ...data.conclusions.map((c, i) => `${i + 1}. ${c}`),
      '',
      'RECOMMENDATIONS',
      ...data.recommendations.map((r, i) => `${i + 1}. ${r}`)
    ].join('\n');

    await navigator.clipboard.writeText(reportText);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  const handleExport = () => {
    // This would typically trigger a PDF/HTML export
    console.log('Export report functionality would be implemented here');
  };

  return (
    <Card className={cn(
      "result-card animate-fade-in-up",
      isHighlighted && "ring-2 ring-primary"
    )}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="w-5 h-5 text-primary" />
          Final Report
          <Badge variant="outline" className="ml-auto">
            {data.metadata.word_count} words
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Report Header */}
        <div className="space-y-2">
          <h3 className="text-lg font-bold">{data.title}</h3>
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <span>Generated: {data.metadata.generated_at}</span>
            <span>Duration: {data.metadata.analysis_duration}</span>
          </div>
        </div>

        <Separator />

        {/* Summary */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm">Executive Summary</h4>
          <p className="text-sm text-muted-foreground leading-relaxed">
            {data.summary}
          </p>
        </div>

        {/* Preview/Full Report Toggle */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setExpanded(!expanded)}
            className="text-primary"
          >
            {expanded ? (
              <>
                <EyeOff className="w-4 h-4 mr-2" />
                Show Preview
              </>
            ) : (
              <>
                <Eye className="w-4 h-4 mr-2" />
                Show Full Report
              </>
            )}
          </Button>

          <div className="flex gap-2">
            <Button variant="outline" size="sm" onClick={handleCopy}>
              <Copy className="w-4 h-4 mr-2" />
              {copied ? 'Copied!' : 'Copy'}
            </Button>
            <Button variant="default" size="sm" onClick={handleExport} className="btn-gradient-primary">
              <Download className="w-4 h-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Report Content */}
        {expanded ? (
          <ScrollArea className="h-[600px] p-4 border rounded-lg bg-muted/20">
            <div className="space-y-6 text-sm">
              {data.sections.map((section, index) => (
                <div key={index} className="space-y-2">
                  <h4 className="font-semibold text-base">{section.title}</h4>
                  {section.type === 'code' ? (
                    <div className="code-block">
                      <pre className="whitespace-pre-wrap">{section.content}</pre>
                    </div>
                  ) : (
                    <div className="prose prose-sm max-w-none">
                      <p className="leading-relaxed">{section.content}</p>
                    </div>
                  )}
                </div>
              ))}

              <Separator />

              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-base mb-2">Key Conclusions</h4>
                  <ul className="space-y-1">
                    {data.conclusions.map((conclusion, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-primary font-medium mt-0.5">•</span>
                        <span>{conclusion}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-semibold text-base mb-2">Recommendations</h4>
                  <ul className="space-y-1">
                    {data.recommendations.map((recommendation, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-accent font-medium mt-0.5">{index + 1}.</span>
                        <span>{recommendation}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </div>
          </ScrollArea>
        ) : (
          <div className="space-y-4 p-4 border rounded-lg bg-muted/20">
            <div>
              <h4 className="font-semibold text-sm mb-2">Quick Overview</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <div className="font-medium mb-1">Sections</div>
                  <ul className="text-muted-foreground space-y-0.5">
                    {data.sections.slice(0, 3).map((section, index) => (
                      <li key={index}>• {section.title}</li>
                    ))}
                    {data.sections.length > 3 && (
                      <li>• +{data.sections.length - 3} more sections</li>
                    )}
                  </ul>
                </div>
                <div>
                  <div className="font-medium mb-1">Key Outcomes</div>
                  <ul className="text-muted-foreground space-y-0.5">
                    <li>• {data.conclusions.length} conclusions</li>
                    <li>• {data.recommendations.length} recommendations</li>
                    <li>• Complete analysis report</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}