import { useState } from 'react';
import { <PERSON>, Copy, Play, Clock, Check } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { ResultCard, CodeExecution } from '@/types/analysis';
import { cn } from '@/lib/utils';

interface CodeCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

export function CodeCard({ card, isHighlighted }: CodeCardProps) {
  const [copied, setCopied] = useState(false);
  const data = card.content as CodeExecution;

  const handleCopy = async () => {
    await navigator.clipboard.writeText(data.code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  return (
    <Card className={cn(
      "result-card animate-fade-in-up",
      isHighlighted && "ring-2 ring-primary"
    )}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Code className="w-5 h-5 text-primary" />
          Generated Code
          <Badge variant="outline" className="ml-auto">
            {data.language}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="relative">
          <div className="absolute top-3 right-3 z-10">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleCopy}
              className="bg-background/80 backdrop-blur-sm"
            >
              {copied ? (
                <Check className="w-4 h-4 text-success" />
              ) : (
                <Copy className="w-4 h-4" />
              )}
            </Button>
          </div>
          
          <div className="rounded-lg overflow-hidden border">
            <SyntaxHighlighter
              language={data.language.toLowerCase()}
              style={oneDark}
              customStyle={{
                margin: 0,
                borderRadius: 0,
                background: 'hsl(var(--secondary))',
                fontSize: '14px',
              }}
            >
              {data.code}
            </SyntaxHighlighter>
          </div>
        </div>

        {data.execution_time && (
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="w-4 h-4" />
            Execution time: {data.execution_time}
          </div>
        )}
      </CardContent>
    </Card>
  );
}