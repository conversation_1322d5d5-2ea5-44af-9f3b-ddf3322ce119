"""API请求响应模型"""

from typing import Dict, Any, List, Optional
from enum import Enum
from pydantic import BaseModel, Field
from datetime import datetime
import json
import numpy as np


def convert_numpy_types(obj):
    """递归转换NumPy数据类型为Python原生类型"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    else:
        return obj


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NodeStatus(str, Enum):
    """节点状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    SUCCESS = "success"
    WARNING = "warning"
    ERROR = "error"


class TaskCreateRequest(BaseModel):
    """创建任务请求模型"""
    prompt: str
    file_name: Optional[str] = None


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: TaskStatus
    created_at: datetime
    updated_at: datetime
    prompt: str
    file_name: Optional[str] = None
    error_message: Optional[str] = None


class NodeUpdate(BaseModel):
    """节点更新模型"""
    node_id: str
    node_name: str
    status: NodeStatus
    content: Dict[str, Any]
    timestamp: datetime
    execution_time: Optional[float] = None

    def model_dump(self, **kwargs):
        """自定义字典序列化，处理datetime和NumPy对象"""
        data = super().model_dump(**kwargs)
        # 将datetime转换为ISO格式字符串
        if 'timestamp' in data and isinstance(data['timestamp'], datetime):
            data['timestamp'] = data['timestamp'].isoformat()
        # 转换NumPy数据类型
        data = convert_numpy_types(data)
        return data

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class WebSocketMessage(BaseModel):
    """WebSocket消息模型"""
    event: str  # node_update, task_complete, error
    payload: Dict[str, Any]


class ExecutionResult(BaseModel):
    """代码执行结果模型"""
    success: bool
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    warnings: Optional[str] = None  # 警告信息
    result: Optional[Any] = None
    execution_time: float
    plots: List[str] = []  # 图表文件路径列表


class DataSummary(BaseModel):
    """数据摘要模型"""
    shape: tuple
    columns: List[str]
    dtypes: Dict[str, str]
    missing_values: Dict[str, int]
    basic_stats: Dict[str, Any]


class AnalysisPlan(BaseModel):
    """分析计划模型"""
    steps: List[Dict[str, Any]]
    estimated_time: int
    complexity: str


class PlanApprovalRequest(BaseModel):
    """计划审批请求模型"""
    approved: bool
    modified_plan: Optional[Dict[str, Any]] = None
    library_preferences: Optional[Dict[str, Any]] = None


class UserInteractionResponse(BaseModel):
    """用户交互响应模型"""
    success: bool
    message: str
