"""文件处理工具"""

import os
import pandas as pd
from typing import Optional
from fastapi import UploadFile
from src.config import settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def save_uploaded_file(file: UploadFile, task_id: str) -> str:
    """保存上传的文件"""
    # 确保上传目录存在
    os.makedirs(settings.upload_directory, exist_ok=True)
    
    # 生成文件路径
    file_extension = os.path.splitext(file.filename)[1] if file.filename else ".csv"
    file_path = os.path.join(settings.upload_directory, f"{task_id}{file_extension}")
    
    # 保存文件
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    logger.info(f"文件已保存: {file_path}")
    return file_path


def load_dataframe(file_path: str) -> pd.DataFrame:
    """加载数据文件为DataFrame"""
    try:
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == ".csv":
            df = pd.read_csv(file_path)
        elif file_extension in [".xlsx", ".xls"]:
            df = pd.read_excel(file_path)
        else:
            raise ValueError(f"不支持的文件格式: {file_extension}")
        
        logger.info(f"数据加载成功: {file_path}, 形状: {df.shape}")
        return df
        
    except Exception as e:
        logger.error(f"加载数据文件失败: {file_path}, 错误: {str(e)}")
        raise


def save_dataframe(df: pd.DataFrame, file_path: str) -> None:
    """保存DataFrame到文件"""
    try:
        # 确保目录存在
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        file_extension = os.path.splitext(file_path)[1].lower()
        
        if file_extension == ".csv":
            df.to_csv(file_path, index=False)
        elif file_extension == ".parquet":
            df.to_parquet(file_path, index=False)
        else:
            # 默认保存为parquet格式
            df.to_parquet(file_path + ".parquet", index=False)
        
        logger.info(f"DataFrame已保存: {file_path}")
        
    except Exception as e:
        logger.error(f"保存DataFrame失败: {file_path}, 错误: {str(e)}")
        raise
