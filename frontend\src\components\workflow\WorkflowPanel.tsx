import { useState } from 'react';
import {
  Brain,
  Database,
  Code,
  BarChart3,
  Cog,
  FileText,
  ChevronLeft,
  ChevronRight,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Loader2,
  Target,
  LineChart,
  UserRoundPen,
  Goal
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { WorkflowNode } from '@/types/analysis';
import { cn } from '@/lib/utils';

interface WorkflowPanelProps {
  nodes: WorkflowNode[];
  selectedNodeId?: string;
  onNodeSelect: (nodeId: string) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
}

const nodeIcons = {
  summary: Database,
  plan: Brain,
  preprocess: Cog,
  analysis: UserRoundPen,
  modeling: Code,
  evaluation: CheckCircle,
  report: FileText,
  data: Database,
  planning: Target,
  code: Code,
  visualization: LineChart,
  completion: Goal,
};

const statusIcons = {
  pending: Clock,
  running: Loader2,
  success: CheckCircle,
  warning: AlertTriangle,
  error: XCircle,
};

export function WorkflowPanel({
  nodes,
  selectedNodeId,
  onNodeSelect,
  isCollapsed,
  onToggleCollapse
}: WorkflowPanelProps) {
  return (
    <div className={cn(
      "border-r border-border bg-card/30 backdrop-blur-sm transition-all duration-300 relative",
      isCollapsed ? "w-22" : "w-80"
    )}>
      {/* Header */}
      <div className="h-16 border-b border-border flex items-center justify-between px-4 bg-gradient-to-r from-card/50 to-card/30">
        {!isCollapsed && (
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-primary animate-pulse" />
            <h2 className="font-bold text-lg bg-gradient-to-r from-foreground to-muted-foreground bg-clip-text text-transparent">
              工作流程
            </h2>
          </div>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className="rounded-full w-9 h-9 p-0 hover:bg-primary/10 transition-all duration-200 hover:scale-105"
        >
          {isCollapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
        </Button>
      </div>

      {/* Workflow Nodes */}
      <ScrollArea className="h-[calc(100vh-8rem)]">
        <div className="p-4 space-y-4">
          {nodes.map((node, index) => {
            const NodeIcon = nodeIcons[node.type] || Cog;
            const StatusIcon = statusIcons[node.status] || Clock;
            const isSelected = selectedNodeId === node.id;

            return (
              <TooltipProvider key={node.id}>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div
                      onClick={() => onNodeSelect(node.id)}
                      className={cn(
                        "workflow-node cursor-pointer group hover:scale-[1.02] transition-all duration-200",
                        node.status,
                        isSelected && "ring-2 ring-primary shadow-lg shadow-primary/20",
                        isCollapsed && "p-3"
                      )}
                    >
                      {/* Connection Line with Animation */}
                      {index > 0 && (
                        <div className={cn(
                          "absolute -top-4 w-0.5 bg-gradient-to-b from-border to-primary/30 transition-all duration-300",
                          isCollapsed ? "left-2 h-4" : "left-8 h-4",
                          node.status === 'running' && "animate-pulse"
                        )} />
                      )}

                      <div className="flex items-start gap-4">
                        {/* Enhanced Icon Container */}
                        <div className={cn(
                          "relative flex items-center justify-center transition-all duration-300 group-hover:scale-110",
                          // 确保折叠时是正方形，展开时也是正方形但更大
                          isCollapsed ? "w-8 h-8 rounded-lg" : "w-12 h-12 rounded-xl",
                          node.status === 'success' && "bg-gradient-to-br from-success/20 to-success/10 text-success shadow-lg shadow-success/20",
                          node.status === 'running' && "bg-gradient-to-br from-primary/20 to-primary/10 text-primary shadow-lg shadow-primary/20 animate-processing-glow",
                          node.status === 'warning' && "bg-gradient-to-br from-warning/20 to-warning/10 text-warning shadow-lg shadow-warning/20",
                          node.status === 'error' && "bg-gradient-to-br from-destructive/20 to-destructive/10 text-destructive shadow-lg shadow-destructive/20",
                          node.status === 'pending' && "bg-gradient-to-br from-muted/30 to-muted/10 text-muted-foreground"
                        )}>
                          <NodeIcon className={cn(
                            // 确保图标在容器中完全居中，折叠时稍小一些
                            isCollapsed ? "w-5 h-5" : "w-6 h-6",
                            "transition-transform duration-200 group-hover:scale-110 flex-shrink-0"
                          )} />

                          {/* Enhanced Status Indicator */}
                          <div className="absolute -top-1 -right-1">
                            <div className={cn(
                              "w-4 h-4 rounded-full flex items-center justify-center shadow-sm",
                              node.status === 'pending' && "bg-muted-foreground/80",
                              node.status === 'running' && "bg-primary animate-pulse",
                              node.status === 'success' && "bg-success",
                              node.status === 'warning' && "bg-warning",
                              node.status === 'error' && "bg-destructive"
                            )}>
                              <StatusIcon className="w-2.5 h-2.5 text-white" />
                            </div>
                          </div>
                        </div>

                        {/* Enhanced Content */}
                        {!isCollapsed && (
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between mb-2">
                              <h4 className="font-semibold text-sm truncate text-foreground group-hover:text-primary transition-colors">
                                {node.name}
                              </h4>
                              <Badge
                                variant="outline"
                                className={cn(
                                  "text-xs font-medium border-0 shadow-sm",
                                  node.status === 'pending' && "bg-muted/50 text-muted-foreground",
                                  node.status === 'running' && "bg-primary/10 text-primary",
                                  node.status === 'success' && "bg-success/10 text-success",
                                  node.status === 'warning' && "bg-warning/10 text-warning",
                                  node.status === 'error' && "bg-destructive/10 text-destructive"
                                )}
                              >
                                {node.status === 'pending' ? '等待中' :
                                 node.status === 'running' ? '运行中' :
                                 node.status === 'success' ? '成功' :
                                 node.status === 'warning' ? '警告' : '错误'}
                              </Badge>
                            </div>

                            {node.description && (
                              <p className="text-xs text-muted-foreground mb-3 line-clamp-2 leading-relaxed">
                                {node.description}
                              </p>
                            )}

                            {node.timestamp && (
                              <div className="flex items-center gap-1.5 text-xs text-muted-foreground mb-2">
                                <Clock className="w-3 h-3" />
                                <span className="font-mono">{node.timestamp}</span>
                              </div>
                            )}

                            {/* Enhanced Progress Bar */}
                            {node.status === 'running' && node.progress !== undefined && (
                              <div className="mt-3">
                                <div className="w-full bg-muted/50 rounded-full h-2 overflow-hidden">
                                  <div
                                    className="bg-gradient-to-r from-primary to-primary-glow h-2 rounded-full transition-all duration-500 animate-pulse"
                                    style={{ width: `${node.progress}%` }}
                                  />
                                </div>
                                <div className="text-xs text-muted-foreground mt-1.5 font-medium">
                                  {node.progress}% 完成
                                </div>
                              </div>
                            )}

                            {/* Running Animation */}
                            {node.status === 'running' && (
                              <div className="mt-2 flex items-center gap-1">
                                <div className="flex gap-1">
                                  <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                                  <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                                  <div className="w-1 h-1 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                                </div>
                                <span className="text-xs text-primary font-medium ml-2">处理中...</span>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </TooltipTrigger>
                  {isCollapsed && (
                    <TooltipContent side="right" className="max-w-xs z-50">
                      <div>
                        <div className="font-medium">{node.name}</div>
                        {node.description && (
                          <div className="text-xs text-muted-foreground mt-1">
                            {node.description}
                          </div>
                        )}
                        <div className="text-xs text-muted-foreground mt-1">
                          状态: {node.status === 'pending' ? '等待中' : 
                                 node.status === 'running' ? '运行中' : 
                                 node.status === 'success' ? '成功' : 
                                 node.status === 'warning' ? '警告' : '错误'}
                        </div>
                      </div>
                    </TooltipContent>
                  )}
                </Tooltip>
              </TooltipProvider>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
}