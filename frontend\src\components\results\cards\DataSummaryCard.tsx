import { Database, BarChart, FileText, TrendingUp, Info } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { ResultCard } from '@/types/analysis';
import { cn } from '@/lib/utils';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import './AnalysisReport.css';

interface DataSummaryCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

export function DataSummaryCard({ card, isHighlighted }: DataSummaryCardProps) {
  // 处理来自WebSocket的数据结构
  const summaryData = card.content.summary || card.content;

  // 安全地处理数据结构
  if (!summaryData || typeof summaryData !== 'object') {
    return (
      <Card className="result-card border-destructive/50">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            数据摘要格式错误，无法显示
          </div>
        </CardContent>
      </Card>
    );
  }

  // 处理数据
  const rows = summaryData.shape ? summaryData.shape[0] : 0;
  const columns = summaryData.shape ? summaryData.shape[1] : summaryData.columns?.length || 0;
  const totalMissingValues = summaryData.missing_values
    ? Object.values(summaryData.missing_values).reduce((sum: number, count: any) => sum + (Number(count) || 0), 0)
    : 0;
  const missingPercentage = rows > 0 && columns > 0
    ? (totalMissingValues / (rows * columns)) * 100
    : 0;
  const memoryUsageMB = summaryData.memory_usage ? (summaryData.memory_usage / 1024 / 1024).toFixed(2) : '0';

  return (
    <Card className={cn(
      "result-card animate-fade-in-up hover:shadow-xl transition-all duration-300",
      isHighlighted && "ring-2 ring-primary shadow-lg shadow-primary/20"
    )}>
      <CardHeader className="pb-4 bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/50">
        <CardTitle className="flex items-center gap-3">
          <div className="relative">
            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary to-primary-glow flex items-center justify-center shadow-lg">
              <Database className="w-5 h-5 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-success flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full" />
            </div>
          </div>
          <div className="flex-1">
            <div className="font-bold text-foreground">数据摘要</div>
            <div className="text-sm text-muted-foreground font-normal">数据集基础信息概览</div>
          </div>
          <Badge variant="outline" className="bg-white/50 border-primary/20 text-primary font-semibold shadow-sm">
            {rows.toLocaleString()} × {columns}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="overview">概览</TabsTrigger>
            <TabsTrigger value="statistics">统计</TabsTrigger>
            <TabsTrigger value="analysis">分析报告</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="space-y-6 mt-4">
            {/* 关键指标卡片 - 重新设计 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
                <div className="relative text-center p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-blue-200/50 dark:border-blue-800/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-1">{rows.toLocaleString()}</div>
                  <div className="text-sm font-medium text-blue-700 dark:text-blue-300">数据行数</div>
                </div>
              </div>

              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-green-600/20 rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
                <div className="relative text-center p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-green-200/50 dark:border-green-800/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-1">{columns}</div>
                  <div className="text-sm font-medium text-green-700 dark:text-green-300">特征列数</div>
                </div>
              </div>

              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-br from-orange-500/20 to-orange-600/20 rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
                <div className="relative text-center p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-orange-200/50 dark:border-orange-800/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-1">{totalMissingValues.toLocaleString()}</div>
                  <div className="text-sm font-medium text-orange-700 dark:text-orange-300">缺失值数量</div>
                </div>
              </div>

              <div className="relative group">
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-xl blur-sm group-hover:blur-none transition-all duration-300"></div>
                <div className="relative text-center p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-purple-200/50 dark:border-purple-800/50 shadow-lg hover:shadow-xl transition-all duration-300">
                  <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-1">{memoryUsageMB} MB</div>
                  <div className="text-sm font-medium text-purple-700 dark:text-purple-300">内存占用</div>
                </div>
              </div>
            </div>

            {/* 数据完整性 - 重新设计 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/10 to-accent/10 rounded-xl blur-sm"></div>
              <div className="relative p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-border/50 shadow-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-lg font-semibold text-foreground">数据完整性</span>
                  <span className="text-2xl font-bold text-primary">
                    {(100 - missingPercentage).toFixed(1)}%
                  </span>
                </div>
                <Progress value={100 - missingPercentage} className="h-3 shadow-inner" />
              </div>
            </div>

            {/* 列信息概览 - 表格形式 */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-br from-slate-500/10 to-slate-600/10 rounded-xl blur-sm"></div>
              <div className="relative p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-border/50 shadow-lg">
                <h3 className="text-lg font-semibold text-foreground mb-4 flex items-center gap-2">
                  <Database className="w-5 h-5 text-primary" />
                  列信息概览
                </h3>
                <div className="max-h-64 overflow-y-auto">
                  <div className="relative">
                    {/* 透明表格背景 */}
                    <div className="absolute inset-0 bg-gradient-to-br from-muted/5 to-muted/10 rounded-lg blur-sm"></div>
                    <div className="relative rounded-lg bg-card/60 backdrop-blur-sm border border-border/30 overflow-hidden">
                      <Table>
                        <TableHeader>
                          <TableRow className="bg-muted/20 hover:bg-muted/30 border-border/50">
                            <TableHead className="font-semibold text-foreground/90 h-10">列名</TableHead>
                            <TableHead className="font-semibold text-foreground/90 h-10">数据类型</TableHead>
                            <TableHead className="font-semibold text-foreground/90 h-10 text-center">缺失值</TableHead>
                            <TableHead className="font-semibold text-foreground/90 h-10 text-center">缺失率</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {summaryData.columns?.map((column: string, index: number) => {
                            const dtype = summaryData.dtypes?.[column] || 'unknown';
                            const missingCount = summaryData.missing_values?.[column] || 0;
                            const missingRate = rows > 0 ? ((missingCount / rows) * 100).toFixed(1) : '0';

                            return (
                              <TableRow
                                key={index}
                                className="hover:bg-muted/20 transition-colors border-border/30"
                              >
                                <TableCell className="font-mono text-sm font-medium text-foreground py-3">
                                  {column}
                                </TableCell>
                                <TableCell className="py-3">
                                  <Badge
                                    variant="secondary"
                                    className="text-xs bg-muted/40 hover:bg-muted/60 border-border/30 transition-colors"
                                  >
                                    {dtype}
                                  </Badge>
                                </TableCell>
                                <TableCell className="text-center py-3">
                                  <span className="font-mono text-sm text-muted-foreground">
                                    {missingCount}
                                  </span>
                                </TableCell>
                                <TableCell className="text-center py-3">
                                  <span className={cn(
                                    "font-semibold text-sm px-2 py-1 rounded-md",
                                    Number(missingRate) > 10
                                      ? "text-destructive bg-destructive/10"
                                      : Number(missingRate) > 5
                                      ? "text-warning bg-warning/10"
                                      : "text-success bg-success/10"
                                  )}>
                                    {missingRate}%
                                  </span>
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabsContent>



          <TabsContent value="statistics" className="space-y-6 mt-4">
            {/* 数值统计 - 重新设计 */}
            {summaryData.numeric_stats && Object.keys(summaryData.numeric_stats).length > 0 && (
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-indigo-500/5 rounded-xl blur-sm"></div>
                <div className="relative p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-border/50 shadow-lg">
                  <h4 className="text-lg font-semibold mb-4 flex items-center gap-2 text-foreground">
                    <TrendingUp className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    数值特征统计
                  </h4>
                  <div className="space-y-4">
                    {Object.entries(summaryData.numeric_stats).map(([column, stats]: [string, any]) => (
                      <div key={column} className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-muted/20 to-muted/10 rounded-lg blur-sm group-hover:blur-none transition-all duration-300"></div>
                        <div className="relative p-3 rounded-lg bg-card/60 backdrop-blur-sm border border-border/30">
                          <div className="font-semibold text-sm mb-3 text-foreground">{column}</div>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                            <div className="text-center p-2 rounded-md bg-blue-50/80 dark:bg-blue-950/30 border border-blue-200/50 dark:border-blue-800/50">
                              <div className="text-lg font-bold font-mono text-blue-700 dark:text-blue-300">{stats.mean?.toFixed(2) || 'N/A'}</div>
                              <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">均值</div>
                            </div>
                            <div className="text-center p-2 rounded-md bg-green-50/80 dark:bg-green-950/30 border border-green-200/50 dark:border-green-800/50">
                              <div className="text-lg font-bold font-mono text-green-700 dark:text-green-300">{stats.std?.toFixed(2) || 'N/A'}</div>
                              <div className="text-xs text-green-600 dark:text-green-400 font-medium">标准差</div>
                            </div>
                            <div className="text-center p-2 rounded-md bg-orange-50/80 dark:bg-orange-950/30 border border-orange-200/50 dark:border-orange-800/50">
                              <div className="text-lg font-bold font-mono text-orange-700 dark:text-orange-300">{stats.min?.toFixed(2) || 'N/A'}</div>
                              <div className="text-xs text-orange-600 dark:text-orange-400 font-medium">最小值</div>
                            </div>
                            <div className="text-center p-2 rounded-md bg-purple-50/80 dark:bg-purple-950/30 border border-purple-200/50 dark:border-purple-800/50">
                              <div className="text-lg font-bold font-mono text-purple-700 dark:text-purple-300">{stats.max?.toFixed(2) || 'N/A'}</div>
                              <div className="text-xs text-purple-600 dark:text-purple-400 font-medium">最大值</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* 分类统计 - 重新设计为紧凑横向布局 */}
            {summaryData.categorical_stats && Object.keys(summaryData.categorical_stats).length > 0 && (
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-green-500/5 to-emerald-500/5 rounded-xl blur-sm"></div>
                <div className="relative p-4 rounded-xl bg-card/80 backdrop-blur-sm border border-border/50 shadow-lg">
                  <h4 className="text-lg font-semibold mb-4 flex items-center gap-2 text-foreground">
                    <BarChart className="w-5 h-5 text-green-600 dark:text-green-400" />
                    分类特征统计
                  </h4>
                  <div className="space-y-4">
                    {Object.entries(summaryData.categorical_stats).map(([column, stats]: [string, any]) => (
                      <div key={column} className="relative group">
                        <div className="absolute inset-0 bg-gradient-to-r from-muted/20 to-muted/10 rounded-lg blur-sm group-hover:blur-none transition-all duration-300"></div>
                        <div className="relative p-3 rounded-lg bg-card/60 backdrop-blur-sm border border-border/30">
                          <div className="flex items-center justify-between mb-3">
                            <div className="font-semibold text-sm text-foreground">{column}</div>
                            <Badge variant="outline" className="text-xs bg-green-50/80 dark:bg-green-950/30 border-green-200/50 dark:border-green-800/50 text-green-700 dark:text-green-300">
                              {stats.unique_count} 个唯一值
                            </Badge>
                          </div>
                          {/* 横向紧凑布局，一行显示4个类别 */}
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                            {Object.entries(stats.top_values || {}).slice(0, 8).map(([value, count]: [string, any], index: number) => (
                              <div key={value} className={cn(
                                "flex flex-col items-center p-2 rounded-md text-xs border transition-all duration-200 hover:shadow-md",
                                index % 4 === 0 && "bg-blue-50/80 dark:bg-blue-950/30 border-blue-200/50 dark:border-blue-800/50",
                                index % 4 === 1 && "bg-green-50/80 dark:bg-green-950/30 border-green-200/50 dark:border-green-800/50",
                                index % 4 === 2 && "bg-orange-50/80 dark:bg-orange-950/30 border-orange-200/50 dark:border-orange-800/50",
                                index % 4 === 3 && "bg-purple-50/80 dark:bg-purple-950/30 border-purple-200/50 dark:border-purple-800/50"
                              )}>
                                <span className={cn(
                                  "truncate max-w-full font-medium",
                                  index % 4 === 0 && "text-blue-700 dark:text-blue-300",
                                  index % 4 === 1 && "text-green-700 dark:text-green-300",
                                  index % 4 === 2 && "text-orange-700 dark:text-orange-300",
                                  index % 4 === 3 && "text-purple-700 dark:text-purple-300"
                                )} title={value}>
                                  {value}
                                </span>
                                <span className={cn(
                                  "font-mono font-bold text-sm mt-1",
                                  index % 4 === 0 && "text-blue-600 dark:text-blue-400",
                                  index % 4 === 1 && "text-green-600 dark:text-green-400",
                                  index % 4 === 2 && "text-orange-600 dark:text-orange-400",
                                  index % 4 === 3 && "text-purple-600 dark:text-purple-400"
                                )}>
                                  {count}
                                </span>
                              </div>
                            ))}
                          </div>
                          {/* 如果有更多类别，显示省略提示 */}
                          {Object.keys(stats.top_values || {}).length > 8 && (
                            <div className="text-center mt-2">
                              <span className="text-xs text-muted-foreground">
                                还有 {Object.keys(stats.top_values || {}).length - 8} 个类别...
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </TabsContent>

          <TabsContent value="analysis" className="space-y-6 mt-4">
            {summaryData.detailed_analysis ? (
              <div className="relative">
                {/* 背景层次效果 */}
                <div className="absolute inset-0 bg-gradient-to-br from-slate-500/5 via-blue-500/5 to-indigo-500/5 rounded-2xl blur-sm"></div>
                <div className="absolute inset-1 bg-gradient-to-br from-card/60 to-card/40 rounded-xl backdrop-blur-sm"></div>

                {/* 内容层 */}
                <div className="relative p-6 rounded-xl bg-card/80 backdrop-blur-sm shadow-xl">
                  <div className="analysis-report max-w-none">
                    <ReactMarkdown
                      remarkPlugins={[remarkGfm]}
                      components={{
                        h1: ({children}) => (
                          <div className="relative mb-6">
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-lg blur-sm"></div>
                            <h1 className="relative flex items-center gap-3 p-4 rounded-lg bg-card/60 backdrop-blur-sm border border-blue-200/30 dark:border-blue-800/30 shadow-md">
                              <div className="w-8 h-8 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center shadow-lg">
                                <FileText className="w-4 h-4 text-white" />
                              </div>
                              <span className="text-xl font-bold text-blue-700 dark:text-blue-300">{children}</span>
                            </h1>
                          </div>
                        ),
                        h2: ({children}) => (
                          <div className="relative mb-4 mt-6">
                            <div className="absolute inset-0 bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-lg blur-sm"></div>
                            <h2 className="relative flex items-center gap-2 p-3 rounded-lg bg-card/40 backdrop-blur-sm shadow-sm">
                              <div className="w-6 h-6 rounded-full bg-gradient-to-br from-green-500 to-green-600 flex items-center justify-center">
                                <TrendingUp className="w-3 h-3 text-white" />
                              </div>
                              <span className="font-semibold text-green-700 dark:text-green-300">{children}</span>
                            </h2>
                          </div>
                        ),
                        h3: ({children}) => (
                          <div className="relative mb-3 mt-4">
                            <h3 className="flex items-center gap-2 p-2 rounded-md bg-muted/30 backdrop-blur-sm">
                              <div className="w-5 h-5 rounded-full bg-gradient-to-br from-orange-500 to-orange-600 flex items-center justify-center">
                                <BarChart className="w-2.5 h-2.5 text-white" />
                              </div>
                              <span className="font-medium text-orange-700 dark:text-orange-300">{children}</span>
                            </h3>
                          </div>
                        ),
                        blockquote: ({children}) => (
                          <div className="relative my-4">
                            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-cyan-500/5 rounded-lg blur-sm"></div>
                            <blockquote className="relative p-4 rounded-lg bg-card/40 backdrop-blur-sm shadow-sm">
                              <div className="flex items-start gap-3">
                                <div className="w-6 h-6 rounded-full bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center mt-0.5 shrink-0">
                                  <Info className="w-3 h-3 text-white" />
                                </div>
                                <div className="text-blue-700 dark:text-blue-300">{children}</div>
                              </div>
                            </blockquote>
                          </div>
                        ),
                        p: ({children}) => (
                          <p className="mb-3 text-foreground/90 leading-relaxed">{children}</p>
                        ),
                        ul: ({children}) => (
                          <ul className="mb-4 space-y-1 text-foreground/90">{children}</ul>
                        ),
                        li: ({children}) => (
                          <li className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 shrink-0"></div>
                            <span>{children}</span>
                          </li>
                        )
                      }}
                    >
                      {summaryData.detailed_analysis}
                    </ReactMarkdown>
                  </div>
                </div>
              </div>
            ) : (
              <div className="relative">
                <div className="absolute inset-0 bg-gradient-to-br from-slate-500/5 to-slate-600/5 rounded-2xl blur-sm"></div>
                <div className="relative text-center py-16 px-6 rounded-xl bg-card/60 backdrop-blur-sm shadow-lg">
                  <div className="relative">
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-lg"></div>
                    <FileText className="relative w-20 h-20 mx-auto mb-6 text-muted-foreground/60" />
                  </div>
                  <h3 className="text-xl font-bold mb-3 text-foreground">详细分析报告正在生成中</h3>
                  <p className="text-muted-foreground max-w-md mx-auto leading-relaxed">AI正在深度分析您的数据，请稍候片刻...</p>
                  <div className="flex justify-center gap-1 mt-6">
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                    <div className="w-2 h-2 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                  </div>
                </div>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
