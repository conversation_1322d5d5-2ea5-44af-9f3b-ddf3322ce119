"""反思审查智能体 - 使用Reflexion框架审查和改进报告"""

import os
import json
import asyncio
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime
from langchain_core.messages import HumanMessage, SystemMessage
from src.agents.base_agent import BaseAgent
from src.models.state_models import AnalysisState
from src.utils.logger import get_logger
from src.database.chroma_client import chroma_client

logger = get_logger(__name__)


def safe_json_dumps(obj: Any, **kwargs) -> str:
    """安全的JSON序列化，处理datetime和其他不可序列化的对象"""
    def json_serializer(obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif hasattr(obj, 'isoformat'):  # 处理其他日期时间类型
            return obj.isoformat()
        elif hasattr(obj, '__dict__'):  # 处理自定义对象
            return obj.__dict__
        else:
            return str(obj)

    try:
        return json.dumps(obj, default=json_serializer, ensure_ascii=False, **kwargs)
    except Exception as e:
        logger.warning(f"JSON序列化失败: {str(e)}, 使用字符串表示")
        return str(obj)


class ReflectionAgent(BaseAgent):
    """反思审查智能体 - 使用Reflexion框架审查和改进生成的报告"""
    
    def __init__(self):
        super().__init__(
            name="ReflectionAgent",
            description="使用Reflexion框架审查报告质量并生成改进版本"
        )
        self.evaluation_criteria = [
            "信息准确性",
            "实验结果覆盖度",
            "内容专业性",
            "逻辑流畅性",
            "数据可视化引用",
            "结论与数据一致性",
            "学术写作质量",
            "结构完整性"
        ]

    def _create_streaming_llm(self):
        """创建专用于报告生成的流式LLM实例"""
        from langchain_openai import ChatOpenAI
        from src.config.settings import settings

        return ChatOpenAI(
            base_url=settings.openai_api_base,
            api_key=settings.openai_api_key,
            model=settings.openai_model_name,
            temperature=0.7,  # 报告生成使用稍高的温度以增加创造性
            max_tokens=6000,
            streaming=True  # 启用流式传输
        )

    def _clean_json_response(self, content: str) -> str:
        """清理LLM响应中的JSON内容，去除代码块标记"""
        # 去除前后空白
        content = content.strip()

        # 去除代码块标记
        if content.startswith('```json'):
            content = content[7:]  # 去除 ```json
        elif content.startswith('```'):
            content = content[3:]   # 去除 ```

        if content.endswith('```'):
            content = content[:-3]  # 去除结尾的 ```

        # 再次去除前后空白
        content = content.strip()

        return content

    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行反思审查流程"""
        self._log_execution("开始反思审查流程")
        
        try:
            task_id = state["task_id"]
            
            # 发送反思开始信号
            await self._send_reflection_start(task_id)
            
            # 1. 收集分析上下文
            context = await self._collect_analysis_context(state)
            
            # 2. 评估报告质量（Evaluator）
            evaluation_result = await self._evaluate_report(state, context)
            await self._send_evaluation_result(task_id, evaluation_result)
            
            # 3. 生成反思和改进建议（Self-Reflection）
            reflection_result = await self._generate_reflection(state, context, evaluation_result)
            await self._send_reflection_result(task_id, reflection_result)
            
            # 4. 重新生成改进的报告（Actor）
            improved_report = await self._regenerate_report(state, context, reflection_result)
            
            # 保存改进后的报告
            improved_report_path = await self._save_improved_report(task_id, improved_report)
            
            # 发送反思完成信号
            await self._send_reflection_complete(task_id, improved_report_path)
            
            # 更新状态
            updated_state = {
                "reflection_evaluation": evaluation_result,
                "reflection_insights": reflection_result,
                "improved_report": improved_report,
                "improved_report_path": improved_report_path,
                "insights": state.get("insights", []) + ["报告反思审查完成，生成改进版本"]
            }
            
            self._log_execution("反思审查流程完成", f"改进报告路径: {improved_report_path}")
            return updated_state
            
        except Exception as e:
            error_msg = f"反思审查执行失败: {str(e)}"
            self.logger.error(error_msg)
            await self._send_reflection_error(state["task_id"], error_msg)
            return {"errors": state.get("errors", []) + [error_msg]}
    
    async def _collect_analysis_context(self, state: AnalysisState) -> Dict[str, Any]:
        """收集分析上下文信息"""
        self.logger.info("收集分析上下文信息")
        
        context = {
            "original_query": state.get("original_query", ""),
            "data_summary": state.get("data_summary", {}),
            "plan": state.get("plan", {}),
            "executed_steps": state.get("executed_steps", []),
            "execution_results": state.get("execution_results", {}),
            "final_report": state.get("final_report", {}),
            "generated_images": [],
            "chroma_insights": []
        }
        
        # 收集生成的图片信息
        task_id = state["task_id"]
        results_dir = Path(f"data/results/{task_id}")
        if results_dir.exists():
            image_files = list(results_dir.glob("*.png")) + list(results_dir.glob("*.jpg"))
            context["generated_images"] = [str(img.name) for img in image_files]
        
        # 从ChromaDB收集相关洞察
        try:
            collection_name = state.get("chroma_collection_name", f"task_{task_id}")
            collection = chroma_client.client.get_collection(collection_name)
            
            # 查询所有相关文档
            results = collection.get()
            if results and results["documents"]:
                context["chroma_insights"] = results["documents"]
                
        except Exception as e:
            self.logger.warning(f"获取ChromaDB洞察失败: {str(e)}")
        
        return context
    
    async def _evaluate_report(self, state: AnalysisState, context: Dict[str, Any]) -> Dict[str, Any]:
        """评估报告质量（Evaluator组件）"""
        self.logger.info("开始评估报告质量")
        
        report = context["final_report"]
        
        # 构建评估提示
        evaluation_prompt = f"""
        作为专业的数据分析报告评估专家，请对基于本次具体分析任务生成的报告进行全面评估。

        ## 本次分析任务背景
        **用户原始需求**: {context["original_query"]}

        **数据集特征**:
        {safe_json_dumps(context["data_summary"], indent=2)}

        ## 实际执行的分析过程
        **分析步骤详情**:
        {safe_json_dumps(context["executed_steps"], indent=2)}

        **生成的可视化图表**:
        {context["generated_images"]}

        **代码执行结果和洞察**:
        {safe_json_dumps(context.get("execution_results", {}), indent=2)}

        ## 当前生成的报告内容
        {safe_json_dumps(report, indent=2)}

        ## 评估要求
        请基于本次具体的分析任务和实际执行结果，从以下维度评估报告质量。重点关注报告是否充分利用了本次分析的具体发现和图表：

        1. **信息准确性** - 报告中的数据和结论是否准确
        2. **实验结果覆盖度** - 是否充分利用了所有分析步骤的结果
        3. **内容专业性** - 语言表达是否专业，术语使用是否准确
        4. **逻辑流畅性** - 报告结构是否清晰，逻辑是否连贯
        5. **数据可视化引用** - 是否正确引用了生成的图表
        6. **结论与数据一致性** - 结论是否与分析结果一致
        7. **学术写作质量** - 是否符合学术写作规范
        8. **结构完整性** - 报告各部分是否完整

        请以JSON格式返回评估结果：
        {{
            "overall_score": 总体评分(1-10),
            "dimension_scores": {{
                "信息准确性": {{
                    "score": 评分,
                    "issues": ["具体问题1", "具体问题2"]
                }},
                ...
            }},
            "critical_issues": ["严重问题列表"],
            "improvement_priorities": ["改进优先级列表"]
        }}
        """
        
        messages = [
            SystemMessage(content="你是一个专业的数据分析报告评估专家，具有丰富的学术写作和数据分析经验。"),
            HumanMessage(content=evaluation_prompt)
        ]
        
        response = await self.llm.ainvoke(messages)

        try:
            # 清理响应内容，去除代码块标记
            cleaned_content = self._clean_json_response(response.content)
            evaluation_result = json.loads(cleaned_content)
            self.logger.info(f"报告评估完成，总体评分: {evaluation_result.get('overall_score', 'N/A')}")
            return evaluation_result
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，记录原始内容并返回基本结构
            self.logger.error(f"评估结果JSON解析失败: {str(e)}")
            self.logger.error(f"原始响应内容: {response.content[:500]}...")
            return {
                "overall_score": 5,
                "dimension_scores": {},
                "critical_issues": ["评估结果解析失败"],
                "improvement_priorities": ["需要重新评估"]
            }

    async def _generate_reflection(self, state: AnalysisState, context: Dict[str, Any],
                                 evaluation: Dict[str, Any]) -> Dict[str, Any]:
        """生成反思和改进建议（Self-Reflection组件）"""
        self.logger.info("开始生成反思和改进建议")

        reflection_prompt = f"""
        作为专业的自我反思专家，基于本次具体分析任务的评估结果，深入分析问题根因并提出针对性的改进策略。

        ## 本次分析任务的评估结果
        {safe_json_dumps(evaluation, indent=2)}

        ## 本次分析的具体背景和资源
        **用户原始需求**: {context["original_query"]}

        **实际执行的分析步骤**:
        {safe_json_dumps(context["executed_steps"], indent=2)}

        **生成的数据可视化图表**: {context["generated_images"]}

        **数据集特征和摘要**:
        {safe_json_dumps(context["data_summary"], indent=2)}

        **代码执行结果和发现的洞察**:
        {safe_json_dumps(context.get("execution_results", {}), indent=2)}

        ## 反思要求
        请基于本次具体的分析任务和实际执行结果进行深度反思，重点关注如何更好地利用本次分析的具体发现：

        1. **问题根因分析** - 分析为什么报告没有充分利用本次分析的具体结果和图表
        2. **改进策略制定** - 针对本次分析的具体发现，提出如何更好地整合到报告中
        3. **重写重点** - 基于本次分析的特点，确定需要重点改进的报告部分
        4. **质量提升方向** - 明确如何更好地展现本次分析的价值和洞察

        请以JSON格式返回反思结果：
        {{
            "root_cause_analysis": {{
                "问题类别": "根因分析"
            }},
            "improvement_strategies": {{
                "策略名称": "具体实施方案"
            }},
            "rewrite_priorities": ["需要重写的部分1", "需要重写的部分2"],
            "quality_enhancement_directions": ["提升方向1", "提升方向2"],
            "specific_actions": [
                {{
                    "action": "具体行动",
                    "target_section": "目标部分",
                    "expected_improvement": "预期改进"
                }}
            ]
        }}
        """

        messages = [
            SystemMessage(content="你是一个专业的自我反思专家，擅长深度分析问题并提出有效的改进策略。"),
            HumanMessage(content=reflection_prompt)
        ]

        response = await self.llm.ainvoke(messages)

        try:
            # 清理响应内容，去除代码块标记
            cleaned_content = self._clean_json_response(response.content)
            reflection_result = json.loads(cleaned_content)
            self.logger.info("反思分析完成")
            return reflection_result
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，记录原始内容并返回基本结构
            self.logger.error(f"反思结果JSON解析失败: {str(e)}")
            self.logger.error(f"原始响应内容: {response.content[:500]}...")
            return {
                "root_cause_analysis": {"解析错误": "反思结果解析失败"},
                "improvement_strategies": {"重新分析": "需要重新进行反思分析"},
                "rewrite_priorities": ["全部内容"],
                "quality_enhancement_directions": ["提升解析准确性"],
                "specific_actions": []
            }

    async def _regenerate_report(self, state: AnalysisState, context: Dict[str, Any],
                               reflection: Dict[str, Any]) -> Dict[str, str]:
        """基于反思重新生成改进的报告（Actor组件）- 使用流式生成"""
        self.logger.info("开始重新生成改进的报告")

        task_id = state["task_id"]
        original_report = context["final_report"]

        # 发送重新生成开始信号
        await self._send_regeneration_start(task_id)

        # 创建流式LLM实例（与ReportGenerationAgent相同）
        streaming_llm = self._create_streaming_llm()

        improved_report = {}

        # 获取需要重写的部分
        rewrite_priorities = reflection.get("rewrite_priorities", [])
        improvement_strategies = reflection.get("improvement_strategies", {})

        # 报告部分映射
        section_mapping = {
            "title_and_abstract": "标题和摘要",
            "introduction": "引言/背景",
            "data_description": "数据描述性分析",
            "exploratory_analysis": "探索性分析",
            "modeling_and_results": "建模方法和模型结果",
            "discussion": "结果分析和探讨",
            "conclusion": "总结"
        }

        # 生成全局改进上下文
        global_improvement_context = await self._generate_improvement_context(
            context, reflection, original_report
        )

        for section_key, section_name in section_mapping.items():
            self.logger.info(f"重新生成报告部分: {section_name}")

            # 发送部分重新生成进度
            await self._send_regeneration_progress(task_id, section_name)

            # 判断是否需要重写此部分
            needs_rewrite = any(priority in section_name for priority in rewrite_priorities) or \
                           any(priority in section_key for priority in rewrite_priorities)

            if needs_rewrite:
                # 使用流式生成重新生成此部分
                improved_content = await self._regenerate_section_streaming(
                    task_id, section_key, section_name, context, reflection,
                    original_report, global_improvement_context, streaming_llm
                )
                improved_report[section_key] = improved_content
            else:
                # 保留原有内容但进行微调
                original_content = original_report.get(section_key, "")
                improved_content = await self._refine_section_streaming(
                    task_id, section_key, section_name, original_content,
                    context, reflection, streaming_llm
                )
                improved_report[section_key] = improved_content

        self.logger.info("改进报告生成完成")
        return improved_report

    async def _generate_improvement_context(self, context: Dict[str, Any],
                                          reflection: Dict[str, Any],
                                          original_report: Dict[str, str]) -> Dict[str, Any]:
        """生成全局改进上下文"""
        return {
            "original_query": context.get("original_query", ""),
            "data_summary": context.get("data_summary", {}),
            "executed_steps": context.get("executed_steps", []),
            "generated_images": context.get("generated_images", []),
            "improvement_strategies": reflection.get("improvement_strategies", {}),
            "quality_directions": reflection.get("quality_enhancement_directions", []),
            "specific_actions": reflection.get("specific_actions", []),
            "original_report": original_report
        }

    async def _regenerate_section_streaming(self, task_id: str, section_key: str, section_name: str,
                                          context: Dict[str, Any], reflection: Dict[str, Any],
                                          original_report: Dict[str, str],
                                          global_context: Dict[str, Any],
                                          streaming_llm) -> str:
        """使用流式生成重新生成报告部分"""

        # 发送章节开始信号
        from src.api.sse import sse_manager
        await sse_manager.send_data(task_id, {
            "type": "section_start",
            "task_id": task_id,
            "section_id": f"improved_{section_key}",
            "section_name": f"改进-{section_name}",
            "message": f"开始重写: {section_name}"
        })

        # 构建改进提示
        improvement_prompt = self._build_improvement_prompt(
            section_key, section_name, original_report.get(section_key, ""),
            global_context, reflection
        )

        messages = [
            SystemMessage(content=self._get_academic_system_prompt()),
            HumanMessage(content=improvement_prompt)
        ]

        # 流式生成内容
        full_content = ""
        chunk_buffer = ""
        word_count = 0
        stream_count = 0

        self.logger.info(f"开始流式生成改进章节 {section_name}")

        async for chunk in streaming_llm.astream(messages):
            if hasattr(chunk, 'content') and chunk.content:
                chunk_buffer += chunk.content
                full_content += chunk.content
                word_count += len(chunk.content)
                stream_count += 1

                # 每累积一定字符数或遇到句号、换行符时发送一次
                if (word_count >= 20 or
                    chunk.content.endswith(('。', '！', '？', '\n', '.', '!', '?')) or
                    len(chunk_buffer) >= 80 or
                    stream_count % 3 == 0):

                    await sse_manager.send_data(task_id, {
                        "type": "section_content",
                        "task_id": task_id,
                        "section_id": f"improved_{section_key}",
                        "content": full_content,
                        "is_partial": True
                    })
                    chunk_buffer = ""
                    word_count = 0

        # 发送最终完整内容
        await sse_manager.send_data(task_id, {
            "type": "section_content",
            "task_id": task_id,
            "section_id": f"improved_{section_key}",
            "content": full_content,
            "is_partial": False
        })

        # 发送章节完成信号
        await sse_manager.send_data(task_id, {
            "type": "section_complete",
            "task_id": task_id,
            "section_id": f"improved_{section_key}",
            "section_name": f"改进-{section_name}",
            "message": f"完成重写: {section_name}"
        })

        self.logger.info(f"改进章节 {section_name} 流式生成完成，总长度: {len(full_content)}")
        return full_content

    async def _refine_section_streaming(self, task_id: str, section_key: str, section_name: str,
                                      original_content: str, context: Dict[str, Any],
                                      reflection: Dict[str, Any], streaming_llm) -> str:
        """使用流式生成微调报告部分"""

        from src.api.sse import sse_manager

        # 发送章节开始信号
        await sse_manager.send_data(task_id, {
            "type": "section_start",
            "task_id": task_id,
            "section_id": f"refined_{section_key}",
            "section_name": f"微调-{section_name}",
            "message": f"开始微调: {section_name}"
        })

        quality_directions = reflection.get("quality_enhancement_directions", [])

        refinement_prompt = f"""
        对报告的"{section_name}"部分进行微调优化。

        ## 原始内容
        {original_content}

        ## 质量提升方向
        {safe_json_dumps(quality_directions, indent=2)}

        ## 可用资源
        - 生成的图表: {context.get("generated_images", [])}
        - 分析目标: {context.get("original_query", "")}

        请对内容进行微调，重点关注：
        1. 语言表达的专业性
        2. 逻辑连贯性
        3. 数据引用的准确性
        4. 学术写作规范

        请直接返回优化后的内容。
        """

        messages = [
            SystemMessage(content=self._get_academic_system_prompt()),
            HumanMessage(content=refinement_prompt)
        ]

        # 流式生成内容
        full_content = ""
        chunk_buffer = ""
        word_count = 0
        stream_count = 0

        async for chunk in streaming_llm.astream(messages):
            if hasattr(chunk, 'content') and chunk.content:
                chunk_buffer += chunk.content
                full_content += chunk.content
                word_count += len(chunk.content)
                stream_count += 1

                # 每累积一定字符数或遇到句号、换行符时发送一次
                if (word_count >= 20 or
                    chunk.content.endswith(('。', '！', '？', '\n', '.', '!', '?')) or
                    len(chunk_buffer) >= 80 or
                    stream_count % 3 == 0):

                    await sse_manager.send_data(task_id, {
                        "type": "section_content",
                        "task_id": task_id,
                        "section_id": f"refined_{section_key}",
                        "content": full_content,
                        "is_partial": True
                    })
                    chunk_buffer = ""
                    word_count = 0

        # 发送最终完整内容
        await sse_manager.send_data(task_id, {
            "type": "section_content",
            "task_id": task_id,
            "section_id": f"refined_{section_key}",
            "content": full_content,
            "is_partial": False
        })

        # 发送章节完成信号
        await sse_manager.send_data(task_id, {
            "type": "section_complete",
            "task_id": task_id,
            "section_id": f"refined_{section_key}",
            "section_name": f"微调-{section_name}",
            "message": f"完成微调: {section_name}"
        })

        return full_content

    def _build_improvement_prompt(self, section_key: str, section_name: str,
                                original_content: str, global_context: Dict[str, Any],
                                reflection: Dict[str, Any]) -> str:
        """构建改进提示"""

        improvement_strategies = global_context.get("improvement_strategies", {})
        specific_actions = global_context.get("specific_actions", [])

        # 找到针对此部分的具体改进行动
        section_actions = [
            action for action in specific_actions
            if section_key in action.get("target_section", "") or
               section_name in action.get("target_section", "")
        ]

        return f"""
        基于对本次具体分析任务的深度反思，重新生成报告的"{section_name}"部分，确保充分利用本次分析的具体发现和结果。

        ## 当前内容（需要改进）
        {original_content}

        ## 本次分析的具体背景和资源
        **用户原始需求**: {global_context.get("original_query", "")}

        **实际执行的分析步骤**:
        {safe_json_dumps(global_context.get("executed_steps", []), indent=2)}

        **生成的数据可视化图表**: {global_context.get("generated_images", [])}

        **数据集特征和发现**:
        {safe_json_dumps(global_context.get("data_summary", {}), indent=2)}

        **代码执行结果和洞察**:
        {safe_json_dumps(global_context.get("execution_results", {}), indent=2)}

        ## 针对性改进策略
        {safe_json_dumps(improvement_strategies, indent=2)}

        ## 针对此部分的具体改进行动
        {safe_json_dumps(section_actions, indent=2)}

        ## 改进要求
        请重新生成这个部分，重点确保：
        1. **深度整合本次分析的具体发现** - 充分利用实际执行的分析步骤和结果
        2. **正确引用和解释生成的图表** - 对每个相关图表进行专业的数据解读
        3. **体现数据驱动的洞察** - 基于实际的数据特征和分析结果得出结论
        4. **回应用户的原始需求** - 确保内容直接回答用户的具体问题
        5. **保持学术写作的严谨性** - 使用客观、专业的学术语言
        6. **逻辑连贯，结构完整** - 确保论述逻辑清晰，层次分明
        7. **与实际分析过程一致** - 确保报告内容与实际执行的分析步骤相符

        请直接返回改进后的内容，不要包含任何解释或格式标记。
        """

    def _get_academic_system_prompt(self) -> str:
        """获取学术写作风格的系统提示词"""
        return """
        你是一位顶级期刊的资深数据科学研究员，具有深厚的学术写作功底。你的任务是基于反思分析结果，撰写高质量的改进数据分析报告。

        写作要求：
        1. **学术严谨性**：使用精确、客观的学术语言，避免主观臆断
        2. **逻辑连贯性**：确保论述逻辑清晰，前后呼应，层次分明
        3. **专业深度**：展现深入的数据洞察和专业分析能力
        4. **结构完整性**：每个段落都有明确的主题，段落间过渡自然
        5. **证据支撑**：所有结论都基于数据证据，避免空泛表述
        6. **改进导向**：针对反思中发现的问题进行有针对性的改进

        语言风格：
        - 使用第三人称客观表述
        - 采用规范的学术术语和表达方式
        - 写作语言仅为中文
        - 长短段落交错，长段落也不要太长（10句话以内），短段落不要太短（3句话以上）
        - 避免口语化和非正式表达
        - 注重数据的准确性和分析的深度
        - 确保图表引用的准确性和完整性

        请确保生成的内容具有顶级学术期刊如`Nature`、`Science`的专业水准，并且明显优于原始版本。
        """

    async def _regenerate_section(self, section_key: str, section_name: str,
                                context: Dict[str, Any], reflection: Dict[str, Any],
                                original_report: Dict[str, str]) -> str:
        """重新生成特定报告部分"""

        improvement_strategies = reflection.get("improvement_strategies", {})
        specific_actions = reflection.get("specific_actions", [])

        # 找到针对此部分的具体改进行动
        section_actions = [
            action for action in specific_actions
            if section_key in action.get("target_section", "") or
               section_name in action.get("target_section", "")
        ]

        regeneration_prompt = f"""
        基于反思分析结果，重新生成报告的"{section_name}"部分。

        ## 原始内容
        {original_report.get(section_key, "")}

        ## 改进策略
        {safe_json_dumps(improvement_strategies, indent=2)}

        ## 针对此部分的具体改进行动
        {safe_json_dumps(section_actions, indent=2)}

        ## 分析上下文
        - 原始目标: {context["original_query"]}
        - 可用图表: {context["generated_images"]}
        - 数据摘要: {safe_json_dumps(context["data_summary"])}

        请重新生成这个部分，确保：
        1. 解决识别出的所有问题
        2. 充分利用分析结果和图表
        3. 保持专业的学术写作风格
        4. 逻辑清晰，结构完整
        5. 与其他部分保持一致性

        请直接返回改进后的内容，不要包含任何解释或格式标记。
        """

        messages = [
            SystemMessage(content="你是一个专业的数据分析报告写作专家，擅长基于反思结果改进报告质量。"),
            HumanMessage(content=regeneration_prompt)
        ]

        response = await self.llm.ainvoke(messages)
        return response.content.strip()

    async def _refine_section(self, section_key: str, section_name: str, original_content: str,
                            context: Dict[str, Any], reflection: Dict[str, Any]) -> str:
        """微调报告部分（不需要完全重写的部分）"""

        quality_directions = reflection.get("quality_enhancement_directions", [])

        refinement_prompt = f"""
        对报告的"{section_name}"部分进行微调优化。

        ## 原始内容
        {original_content}

        ## 质量提升方向
        {safe_json_dumps(quality_directions, indent=2)}

        ## 可用资源
        - 生成的图表: {context["generated_images"]}
        - 分析目标: {context["original_query"]}

        请对内容进行微调，重点关注：
        1. 语言表达的专业性
        2. 逻辑连贯性
        3. 数据引用的准确性
        4. 学术写作规范

        请直接返回优化后的内容。
        """

        messages = [
            SystemMessage(content="你是一个专业的文本编辑专家，擅长优化学术写作质量。"),
            HumanMessage(content=refinement_prompt)
        ]

        response = await self.llm.ainvoke(messages)
        return response.content.strip()

    async def _save_improved_report(self, task_id: str, improved_report: Dict[str, str]) -> str:
        """保存改进后的报告"""
        results_dir = Path(f"data/results/{task_id}")
        results_dir.mkdir(parents=True, exist_ok=True)

        # 保存为JSON格式
        report_path = results_dir / "improved_report.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(improved_report, f, ensure_ascii=False, indent=2)

        # 保存为Markdown格式
        markdown_path = results_dir / "improved_report.md"
        markdown_content = self._convert_to_markdown(improved_report)
        with open(markdown_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)

        self.logger.info(f"改进报告已保存: {report_path}")
        return str(report_path)

    def _convert_to_markdown(self, report: Dict[str, str]) -> str:
        """将报告转换为Markdown格式"""
        section_titles = {
            "title_and_abstract": "# 标题和摘要",
            "introduction": "## 引言/背景",
            "data_description": "## 数据描述性分析",
            "exploratory_analysis": "## 探索性分析",
            "modeling_and_results": "## 建模方法和模型结果",
            "discussion": "## 结果分析和探讨",
            "conclusion": "## 总结"
        }

        markdown_parts = []
        for section_key, content in report.items():
            if section_key in section_titles:
                markdown_parts.append(section_titles[section_key])
                markdown_parts.append("")
                markdown_parts.append(content)
                markdown_parts.append("")

        return "\n".join(markdown_parts)

    # SSE通信方法
    async def _send_reflection_start(self, task_id: str):
        """发送反思开始信号"""
        try:
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_reflection_start(task_id)
        except Exception as e:
            self.logger.warning(f"发送反思开始信号失败: {str(e)}")

    async def _send_evaluation_result(self, task_id: str, evaluation: Dict[str, Any]):
        """发送评估结果"""
        try:
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_evaluation_result(task_id, evaluation)
        except Exception as e:
            self.logger.warning(f"发送评估结果失败: {str(e)}")

    async def _send_reflection_result(self, task_id: str, reflection: Dict[str, Any]):
        """发送反思结果"""
        try:
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_reflection_result(task_id, reflection)
        except Exception as e:
            self.logger.warning(f"发送反思结果失败: {str(e)}")

    async def _send_regeneration_start(self, task_id: str):
        """发送重新生成开始信号"""
        try:
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_regeneration_start(task_id)
        except Exception as e:
            self.logger.warning(f"发送重新生成开始信号失败: {str(e)}")

    async def _send_regeneration_progress(self, task_id: str, section_name: str):
        """发送重新生成进度"""
        try:
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_regeneration_progress(task_id, section_name)
        except Exception as e:
            self.logger.warning(f"发送重新生成进度失败: {str(e)}")

    async def _send_reflection_complete(self, task_id: str, report_path: str):
        """发送反思完成信号"""
        try:
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_reflection_complete(task_id, report_path)
        except Exception as e:
            self.logger.warning(f"发送反思完成信号失败: {str(e)}")

    async def _send_reflection_error(self, task_id: str, error_msg: str):
        """发送反思错误信号"""
        try:
            from src.api.sse import report_stream_manager
            await report_stream_manager.send_error(task_id, error_msg)
        except Exception as e:
            self.logger.warning(f"发送反思错误信号失败: {str(e)}")

    def _log_execution(self, message: str, details: str = ""):
        """记录执行日志"""
        log_msg = f"[反思审查] {message}"
        if details:
            log_msg += f" - {details}"
        self.logger.info(log_msg)
