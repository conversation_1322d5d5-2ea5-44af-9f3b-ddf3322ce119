import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
  FileText,
  Download,
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  Edit,
  Save,
  X,
  ChevronDown
} from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import rehypeKatex from 'rehype-katex';
import ReactQuill from 'react-quill';
import 'react-quill/dist/quill.snow.css';
import 'highlight.js/styles/github-dark.css';
import 'katex/dist/katex.min.css';
import './ReportGeneration.css';

interface ReportSection {
  id: string;
  name: string;
  status: 'pending' | 'generating' | 'complete' | 'error';
  content?: string;
  error?: string;
}

interface ReportGenerationCardProps {
  taskId: string;
  isHighlighted?: boolean;
}

const SECTION_NAMES = {
  title_and_abstract: '标题和摘要',
  introduction: '引言/背景',
  data_description: '数据描述性分析',
  exploratory_analysis: '探索性分析',
  modeling_and_results: '建模方法和模型结果',
  discussion: '结果分析和探讨',
  conclusion: '总结'
};

export function ReportGenerationCard({ taskId, isHighlighted }: ReportGenerationCardProps) {
  const [sections, setSections] = useState<ReportSection[]>([]);
  const [currentSection, setCurrentSection] = useState<string | null>(null);
  const [overallStatus, setOverallStatus] = useState<'idle' | 'generating' | 'complete' | 'error'>('idle');
  const [progress, setProgress] = useState(0);
  const [reportPath, setReportPath] = useState<string | null>(null);
  const [duration, setDuration] = useState<number | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [editedContent, setEditedContent] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [forceUpdate, setForceUpdate] = useState(0); // 用于强制重新渲染
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const contentEndRef = useRef<HTMLDivElement>(null);

  // 自动滚动到底部
  const scrollToBottom = () => {
    if (contentEndRef.current) {
      contentEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end'
      });
    }
  };

  // 初始化章节和恢复状态
  useEffect(() => {
    if (!taskId || isInitialized) return;

    const initializeAndRecover = async () => {
      // 初始化章节
      const initialSections: ReportSection[] = Object.entries(SECTION_NAMES).map(([id, name]) => ({
        id,
        name,
        status: 'pending'
      }));

      // 尝试从后端恢复已生成的内容
      try {
        const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
        const response = await fetch(`${apiBaseUrl}/api/v1/tasks/${taskId}/report-status`);
        
        if (response.ok) {
          const status = await response.json();
          console.log('[ReportGeneration] 恢复状态:', status);

          if (status.overall_status === 'complete') {
            setOverallStatus('complete');
            setProgress(100);
            setReportPath(status.report_path);
            
            // 恢复章节内容
            if (status.sections) {
              const recoveredSections = initialSections.map(section => ({
                ...section,
                status: status.sections[section.id]?.status || 'pending',
                content: status.sections[section.id]?.content || undefined,
                error: status.sections[section.id]?.error || undefined
              }));
              setSections(recoveredSections);
            } else {
              setSections(initialSections);
            }
          } else {
            // 恢复章节内容，无论报告是否完成
            if (status.sections) {
              const recoveredSections = initialSections.map(section => ({
                ...section,
                status: status.sections[section.id]?.status || 'pending',
                content: status.sections[section.id]?.content || undefined,
                error: status.sections[section.id]?.error || undefined
              }));
              console.log('[ReportGeneration] 恢复章节内容:', recoveredSections);
              setSections(recoveredSections);
              // 强制重新渲染，确保UI更新
              setForceUpdate(prev => prev + 1);
            } else {
              console.log('[ReportGeneration] 没有章节内容，使用初始章节');
              setSections(initialSections);
            }

            setOverallStatus(status.overall_status || 'idle');
            setProgress(status.progress || 0);
            setCurrentSection(status.current_section);

            // 如果有报告路径，也设置它
            if (status.report_path) {
              setReportPath(status.report_path);
            }
          }
        } else {
          setSections(initialSections);
        }
      } catch (error) {
        console.error('恢复报告状态失败:', error);
        setSections(initialSections);
      }

      setIsInitialized(true);
    };

    initializeAndRecover();
  }, [taskId, isInitialized]);

  const handleSSEMessage = React.useCallback((data: {
    type: string;
    task_id: string;
    section?: string;
    content?: string;
    is_partial?: boolean;
    report_path?: string;
    error?: string;
    timestamp?: string;
    duration?: number;
  }) => {
    console.log('收到SSE消息:', data); // 添加调试日志

    switch (data.type) {
      case 'report_start':
        console.log('[ReportGeneration] 开始生成报告');
        setOverallStatus('generating');
        setProgress(0);
        break;

      case 'section_start':
        console.log('[ReportGeneration] 收到章节开始信号:', data);
        // 后端发送的是 section 字段，不是 section_id
        setCurrentSection(data.section);
        setSections(prev => {
          const updated = prev.map(section =>
            section.id === data.section
              ? { ...section, status: 'generating' as const }
              : section
          );
          console.log('[ReportGeneration] 更新章节状态:', updated);
          // 强制重新渲染
          setForceUpdate(prev => prev + 1);
          return updated;
        });
        break;

      case 'section_content':
        // 后端发送的是 section 字段，不是 section_id
        setSections(prev => prev.map(section =>
          section.id === data.section
            ? {
                ...section,
                content: data.is_partial ? data.content : data.content // 使用完整内容，不是追加
              }
            : section
        ));
        // 自动滚动到底部
        setTimeout(scrollToBottom, 100);
        break;

      case 'section_complete': {
        console.log('[ReportGeneration] 收到章节完成信号:', data);
        setSections(prev => {
          const updated = prev.map(section =>
            section.id === data.section
              ? { ...section, status: 'complete' as const }
              : section
          );
          console.log('[ReportGeneration] 章节完成状态更新:', updated);

          // 使用更新后的状态计算进度
          const completedSections = updated.filter(s => s.status === 'complete').length;
          const totalSections = updated.length;
          const newProgress = Math.round((completedSections / totalSections) * 100);
          console.log('[ReportGeneration] 进度更新:', `${completedSections}/${totalSections} = ${newProgress}%`);
          setProgress(newProgress);

          // 强制重新渲染
          setForceUpdate(prev => prev + 1);

          return updated;
        });
        break;
      }

      case 'section_error':
        setSections(prev => prev.map(section =>
          section.id === data.section
            ? { ...section, status: 'error', error: data.error }
            : section
        ));
        break;

      case 'report_complete':
        setOverallStatus('complete');
        setProgress(100);
        setCurrentSection(null);
        setReportPath(data.report_path);
        setDuration(data.duration);
        break;

      case 'report_error':
        setOverallStatus('error');
        setCurrentSection(null);
        break;

      case 'heartbeat':
        // 心跳消息，不需要处理
        break;

      default:
        console.log('未知的SSE消息类型:', data.type);
    }
    // 不需要依赖sections，因为我们使用函数式更新
  }, []);

  // SSE连接
  useEffect(() => {
    if (!taskId) return;

    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
    const sseUrl = `${apiBaseUrl}/api/v1/tasks/${taskId}/report-stream`;
    console.log('连接SSE流:', sseUrl);

    const eventSource = new EventSource(sseUrl);

    eventSource.onopen = () => {
      console.log('SSE连接已建立');
    };

    eventSource.onmessage = (event) => {
      console.log('收到SSE原始消息:', event.data);
      try {
        const data = JSON.parse(event.data);
        handleSSEMessage(data);
      } catch (error) {
        console.error('解析SSE消息失败:', error, '原始数据:', event.data);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE连接错误:', error);
      console.log('SSE readyState:', eventSource.readyState);
      setOverallStatus('error');
    };

    return () => {
      console.log('关闭SSE连接');
      eventSource.close();
    };
  }, [taskId, handleSSEMessage]);

  const getStatusIcon = (status: ReportSection['status']) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-gray-400" />;
      case 'generating':
        return <Loader2 className="h-4 w-4 text-blue-500 animate-spin" />;
      case 'complete':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getStatusBadge = () => {
    switch (overallStatus) {
      case 'idle':
        return <Badge variant="secondary">等待开始</Badge>;
      case 'generating':
        return <Badge variant="default" className="bg-blue-500">生成中</Badge>;
      case 'complete':
        return <Badge variant="default" className="bg-green-500">已完成</Badge>;
      case 'error':
        return <Badge variant="destructive">生成失败</Badge>;
      default:
        return null;
    }
  };

  // 下载Markdown格式
  const handleDownloadMarkdown = () => {
    const content = getFullReportContent();
    const blob = new Blob([content], { type: 'text/markdown;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `analysis_report_${taskId}_${new Date().toISOString().split('T')[0]}.md`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 下载HTML格式
  const handleDownloadHtml = () => {
    const markdownContent = getFullReportContent();
    const htmlContent = convertMarkdownToHtml(markdownContent);
    const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `analysis_report_${taskId}_${new Date().toISOString().split('T')[0]}.html`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // 获取完整报告内容用于编辑 - 转换为更易读的格式
  const getFullReportContent = useCallback(() => {
    const reportTitle = "# 数据分析报告\n\n";
    const reportContent = sections
      .filter(section => section.content)
      .map(section => {
        // 清理和格式化内容
        const cleanContent = section.content
          ?.replace(/^#+\s*/gm, '') // 移除多余的标题标记
          ?.replace(/\n{3,}/g, '\n\n') // 规范化换行
          ?.trim();

        return `## ${section.name}\n\n${cleanContent}`;
      })
      .join('\n\n');

    return reportTitle + reportContent;
  }, [sections]);

  // 处理Markdown内容，转换为HTML - 重新设计避免嵌套问题
  const processMarkdownContent = useCallback((markdown: string) => {
    // 按章节分割内容
    const sections = markdown.split(/^## /gm).filter(Boolean);

    let htmlSections = '';

    sections.forEach((section, index) => {
      if (!section.trim()) return;

      // 提取章节标题和内容
      const lines = section.split('\n');
      const title = lines[0].replace(/^#+\s*/, '').trim();
      const content = lines.slice(1).join('\n').trim();

      if (!title) return;

      // 处理内容
      let processedContent = content
        // 处理子标题
        .replace(/^### (.*$)/gim, '<h3 class="subsection-title">$1</h3>')
        .replace(/^#### (.*$)/gim, '<h4 class="sub-subsection-title">$1</h4>')
        // 处理粗体和斜体
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        // 处理代码块
        .replace(/```([^`]+)```/g, '<pre class="code-block"><code>$1</code></pre>')
        .replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>')
        // 处理引用
        .replace(/^> (.*$)/gim, '<blockquote class="quote">$1</blockquote>')
        // 处理数学公式 - 修复正则表达式
        .replace(/\$\$([\s\S]*?)\$\$/g, '<div class="math-display">$$$1$$</div>')
        .replace(/\$([^$\n]+)\$/g, '<span class="math-inline">$1</span>')
        // 处理列表
        .replace(/^\* (.*$)/gim, '<li class="list-item">$1</li>')
        .replace(/^- (.*$)/gim, '<li class="list-item">$1</li>')
        .replace(/^\d+\. (.*$)/gim, '<li class="ordered-item">$1</li>')
        // 处理段落
        .split('\n\n')
        .map(para => {
          para = para.trim();
          if (!para) return '';
          if (para.startsWith('<h') || para.startsWith('<pre') || para.startsWith('<blockquote') || para.startsWith('<div') || para.startsWith('<ul') || para.startsWith('<ol')) {
            return para;
          }
          if (para.includes('<li')) {
            const isOrdered = para.includes('ordered-item');
            const listTag = isOrdered ? 'ol' : 'ul';
            return `<${listTag} class="content-list">${para}</${listTag}>`;
          }
          return `<p class="content-paragraph">${para.replace(/\n/g, '<br>')}</p>`;
        })
        .join('\n');

      // 生成章节HTML
      htmlSections += `
        <section class="report-section" id="section-${index}">
          <div class="section-header">
            <h2 class="section-title">
              <span class="section-number">${String(index + 1).padStart(2, '0')}</span>
              ${title}
            </h2>
          </div>
          <div class="section-content">
            ${processedContent}
          </div>
        </section>
      `;
    });

    return htmlSections;
  }, []);

  // 转换Markdown为HTML用于下载 - 全新设计的专业报告模板
  const convertMarkdownToHtml = useCallback((markdown: string) => {
    // 获取当前日期
    const currentDate = new Date().toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    // 提取报告标题
    const titleMatch = markdown.match(/^# (.*?)$/m);
    const reportTitle = titleMatch ? titleMatch[1] : '数据分析报告';

    // 全新的专业HTML模板
    const htmlTemplate = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="专业数据分析报告">
    <title>${reportTitle} - ${currentDate}</title>

    <!-- 外部资源 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@300;400;500&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.css">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/katex.min.js"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.9/dist/contrib/auto-render.min.js"></script>

    <style>
        :root {
            /* 现代色彩系统 */
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;
            --primary-800: #075985;

            --accent-50: #fef3c7;
            --accent-500: #f59e0b;
            --accent-600: #d97706;

            --gray-50: #f8fafc;
            --gray-100: #f1f5f9;
            --gray-200: #e2e8f0;
            --gray-300: #cbd5e1;
            --gray-400: #94a3b8;
            --gray-500: #64748b;
            --gray-600: #475569;
            --gray-700: #334155;
            --gray-800: #1e293b;
            --gray-900: #0f172a;

            --success-500: #10b981;
            --warning-500: #f59e0b;
            --error-500: #ef4444;

            /* 间距系统 */
            --space-xs: 0.5rem;
            --space-sm: 0.75rem;
            --space-md: 1rem;
            --space-lg: 1.5rem;
            --space-xl: 2rem;
            --space-2xl: 3rem;
            --space-3xl: 4rem;

            /* 圆角系统 */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;

            /* 阴影系统 */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.75;
            color: var(--gray-800);
            background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 50%, var(--primary-50) 100%);
            min-height: 100vh;
            font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--space-xl);
        }

        /* 现代玻璃态效果 */
        .glass {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(24px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: var(--shadow-lg);
        }

        /* 现代导航栏 */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            padding: var(--space-lg) var(--space-xl);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(24px) saturate(180%);
            border-bottom: 1px solid var(--gray-200);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navbar-content {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--primary-600);
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .nav-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .nav-links a {
            color: var(--gray-600);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-links a:hover {
            color: var(--primary-600);
        }

        .nav-links a::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--primary-600);
            transition: width 0.3s ease;
        }

        .nav-links a:hover::after {
            width: 100%;
        }

        /* 主要内容区域 */
        .main-content {
            margin-top: 5rem;
            padding: 2rem 0;
        }

        /* 报告标题区域 */
        .report-header {
            text-align: center;
            margin-bottom: 4rem;
            padding: 4rem 2rem;
            background: linear-gradient(135deg, var(--primary-50) 0%, rgba(59, 130, 246, 0.05) 100%);
            border-radius: 2rem;
            position: relative;
            overflow: hidden;
        }

        .report-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23000" opacity="0.02"/><circle cx="75" cy="75" r="1" fill="%23000" opacity="0.02"/><circle cx="50" cy="10" r="1" fill="%23000" opacity="0.02"/><circle cx="10" cy="50" r="1" fill="%23000" opacity="0.02"/><circle cx="90" cy="30" r="1" fill="%23000" opacity="0.02"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .report-title {
            font-size: 3rem;
            font-weight: 700;
            color: var(--gray-900);
            margin-bottom: 1rem;
            position: relative;
            z-index: 1;
        }

        .report-subtitle {
            font-size: 1.25rem;
            color: var(--gray-600);
            margin-bottom: 2rem;
            position: relative;
            z-index: 1;
        }

        .report-meta {
            display: flex;
            justify-content: center;
            gap: 2rem;
            flex-wrap: wrap;
            position: relative;
            z-index: 1;
        }

        .meta-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 2rem;
            font-weight: 500;
            color: var(--gray-700);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        /* 现代报告章节样式 */
        .report-section {
            margin-bottom: var(--space-3xl);
            background: rgba(255, 255, 255, 0.9);
            border-radius: var(--radius-2xl);
            backdrop-filter: blur(24px) saturate(180%);
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: var(--shadow-lg);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .report-section:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
            border-color: var(--primary-200);
        }

        .report-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-500), var(--accent-500), var(--primary-600));
        }

        .section-header {
            padding: var(--space-2xl) var(--space-2xl) var(--space-lg);
            border-bottom: 1px solid var(--gray-100);
            background: linear-gradient(135deg, var(--gray-50) 0%, rgba(255, 255, 255, 0.8) 100%);
        }

        .section-title {
            font-size: 2.25rem;
            font-weight: 700;
            color: var(--gray-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-lg);
            letter-spacing: -0.025em;
        }

        .section-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 3rem;
            height: 3rem;
            background: linear-gradient(135deg, var(--primary-500), var(--primary-600));
            color: white;
            border-radius: var(--radius-lg);
            font-size: 1.125rem;
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        .section-content {
            padding: var(--space-2xl);
        }

        .subsection-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--gray-800);
            margin: var(--space-2xl) 0 var(--space-lg) 0;
            padding-left: var(--space-lg);
            border-left: 3px solid var(--accent-500);
            position: relative;
        }

        .subsection-title::before {
            content: '';
            position: absolute;
            left: -3px;
            top: 0;
            bottom: 0;
            width: 3px;
            background: linear-gradient(180deg, var(--accent-500), var(--primary-500));
        }

        .content-paragraph {
            margin-bottom: var(--space-lg);
            text-align: justify;
            color: var(--gray-700);
            font-size: 1.125rem;
            line-height: 1.8;
            hyphens: auto;
        }

        /* 现代代码样式 */
        .inline-code {
            font-family: 'JetBrains Mono', 'Fira Code', monospace;
            background: var(--gray-100);
            color: var(--primary-700);
            padding: 0.2rem 0.4rem;
            border-radius: var(--radius-sm);
            font-size: 0.9em;
            font-weight: 400;
            border: 1px solid var(--gray-200);
            white-space: nowrap;
        }

        .code-block {
            background: var(--gray-50);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-lg);
            padding: var(--space-xl);
            margin: var(--space-xl) 0;
            overflow-x: auto;
            font-family: 'JetBrains Mono', monospace;
            font-size: 0.9rem;
            line-height: 1.6;
            box-shadow: var(--shadow-md);
            position: relative;
        }

        .code-block::before {
            content: 'Code';
            position: absolute;
            top: 0;
            right: var(--space-lg);
            background: var(--primary-500);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 0 0 var(--radius-sm) var(--radius-sm);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .code-block code {
            background: none;
            border: none;
            padding: 0;
            color: var(--gray-800);
            display: block;
        }

        /* 数学公式样式 */
        .math-inline {
            font-family: 'KaTeX_Main', serif;
            padding: 0 0.2em;
        }

        .math-display {
            margin: var(--space-xl) 0;
            padding: var(--space-lg);
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            border-left: 3px solid var(--primary-500);
            overflow-x: auto;
            text-align: center;
        }

        /* 现代引用样式 */
        .quote {
            border-left: 4px solid var(--primary-500);
            background: linear-gradient(135deg, var(--primary-50), rgba(14, 165, 233, 0.03));
            padding: var(--space-xl);
            margin: var(--space-xl) 0;
            border-radius: 0 var(--radius-lg) var(--radius-lg) 0;
            font-style: italic;
            position: relative;
            backdrop-filter: blur(10px);
            color: var(--gray-600);
        }

        .quote::before {
            content: '"';
            font-size: 4rem;
            color: rgba(14, 165, 233, 0.2);
            position: absolute;
            top: -1rem;
            left: var(--space-md);
            font-family: 'Georgia', serif;
            opacity: 0.7;
        }

        /* 现代列表样式 */
        .content-list {
            margin: var(--space-xl) 0;
            padding-left: var(--space-2xl);
            list-style-position: outside;
        }

        .list-item, .ordered-item {
            margin-bottom: var(--space-md);
            color: var(--gray-700);
            position: relative;
            padding-left: var(--space-xs);
        }

        .content-list .list-item::marker {
            color: var(--primary-500);
            content: '•';
            font-size: 1.2em;
        }

        .content-list .ordered-item::marker {
            color: var(--primary-600);
            font-weight: 600;
            font-family: 'JetBrains Mono', monospace;
        }

        /* 数据标签样式 */
        .data-pill {
            display: inline-flex;
            align-items: center;
            background: var(--gray-100);
            color: var(--gray-700);
            padding: 0.25rem 0.75rem;
            border-radius: 2rem;
            font-size: 0.875rem;
            font-weight: 500;
            margin-right: 0.5rem;
            border: 1px solid var(--gray-200);
        }

        .data-pill-primary {
            background: var(--primary-50);
            color: var(--primary-700);
            border-color: var(--primary-100);
        }

        .data-pill-accent {
            background: var(--accent-50);
            color: var(--accent-600);
            border-color: var(--accent-50);
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 2rem 0;
            border-radius: 0.75rem;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            background: white;
        }

        th {
            background: linear-gradient(135deg, var(--gray-100), var(--gray-50));
            color: var(--gray-800);
            font-weight: 600;
            padding: 1rem;
            text-align: left;
            border-bottom: 2px solid var(--gray-200);
        }

        td {
            padding: 1rem;
            border-bottom: 1px solid var(--gray-100);
            color: var(--gray-700);
        }

        tr:hover {
            background: var(--gray-50);
        }

        /* 数学公式样式 */
        .katex {
            font-size: 1.1em;
        }

        .katex-display {
            margin: 2rem 0;
            text-align: center;
            padding: 1rem;
            background: var(--gray-50);
            border-radius: 0.75rem;
            border: 1px solid var(--gray-200);
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            width: 3rem;
            height: 3rem;
            background: var(--primary-600);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            opacity: 0;
            transform: translateY(20px);
        }

        .back-to-top.visible {
            opacity: 1;
            transform: translateY(0);
        }

        .back-to-top:hover {
            background: var(--primary-700);
            transform: translateY(-2px);
            box-shadow: 0 6px 24px rgba(59, 130, 246, 0.4);
        }

        /* 进度指示器 */
        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
            z-index: 1001;
            transition: width 0.3s ease;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .container {
                padding: 1rem;
            }

            .navbar {
                padding: 1rem;
            }

            .nav-links {
                display: none;
            }

            .report-title {
                font-size: 2rem;
            }

            .report-meta {
                flex-direction: column;
                align-items: center;
            }

            .section {
                padding: 2rem;
                margin-bottom: 2rem;
            }

            .section h2 {
                font-size: 1.5rem;
            }
        }

        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .section {
            animation: fadeInUp 0.6s ease-out;
        }

        .section:nth-child(even) {
            animation-delay: 0.1s;
        }

        .section:nth-child(odd) {
            animation-delay: 0.2s;
        }
    </style>
</head>
<body>
    <!-- 进度指示器 -->
    <div class="progress-bar" id="progressBar"></div>

    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="navbar-content">
            <div class="logo">
                <i class="fas fa-chart-line"></i>
                数据分析报告
            </div>
            <ul class="nav-links">
                <li><a href="#abstract">摘要</a></li>
                <li><a href="#introduction">引言</a></li>
                <li><a href="#analysis">分析</a></li>
                <li><a href="#results">结果</a></li>
                <li><a href="#conclusion">结论</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container">
        <div class="main-content">
            <!-- 报告标题区域 -->
            <div class="report-header">
                <h1 class="report-title">${reportTitle}</h1>
                <p class="report-subtitle">专业数据洞察与分析结果</p>
                <div class="report-meta">
                    <div class="meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        生成日期：${currentDate}
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-robot"></i>
                        智能分析系统
                    </div>
                    <div class="meta-item">
                        <i class="fas fa-chart-line"></i>
                        数据分析报告
                    </div>
                </div>
            </div>

            <!-- 报告内容 -->
            <div class="report-content">
                ${processMarkdownContent(markdown)}
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <i class="fas fa-arrow-up"></i>
    </button>

    <script>
        // 进度指示器
        function updateProgressBar() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            document.getElementById('progressBar').style.width = scrollPercent + '%';
        }

        // 返回顶部按钮
        function toggleBackToTop() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.classList.add('visible');
            } else {
                backToTop.classList.remove('visible');
            }
        }

        // 平滑滚动
        function smoothScrollTo(target) {
            const element = document.querySelector(target);
            if (element) {
                element.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }

        // 事件监听
        window.addEventListener('scroll', () => {
            updateProgressBar();
            toggleBackToTop();
        });

        document.getElementById('backToTop').addEventListener('click', () => {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // 导航链接点击事件
        document.querySelectorAll('.nav-links a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                smoothScrollTo(link.getAttribute('href'));
            });
        });

        // KaTeX渲染
        document.addEventListener('DOMContentLoaded', function() {
            if (window.renderMathInElement) {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: '$$', right: '$$', display: true},
                        {left: '$', right: '$', display: false},
                        {left: '\\\\(', right: '\\\\)', display: false},
                        {left: '\\\\[', right: '\\\\]', display: true}
                    ]
                });
            }
        });

        // 初始化
        updateProgressBar();
        toggleBackToTop();
    </script>
</body>
</html>`;
    return htmlTemplate;
  }, [processMarkdownContent]);

  // 开始编辑
  const handleStartEdit = () => {
    const fullContent = getFullReportContent();
    setEditedContent(fullContent);
    setIsEditing(true);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedContent('');
  };

  // 保存编辑后的报告
  const handleSaveEdit = async () => {
    if (!editedContent.trim()) return;
    
    setIsSaving(true);
    try {
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8001';
      const response = await fetch(`${apiBaseUrl}/api/v1/tasks/${taskId}/report/save-edited`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: editedContent,
          original_report_path: reportPath
        }),
      });

      if (response.ok) {
        const result = await response.json();
        setReportPath(result.new_report_path);
        setIsEditing(false);
        // 可以显示成功消息
        console.log('报告已保存为新版本:', result.new_report_path);
      } else {
        console.error('保存报告失败');
      }
    } catch (error) {
      console.error('保存报告时出错:', error);
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Card className={`transition-all duration-200 ${isHighlighted ? 'ring-2 ring-primary' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            <CardTitle className="text-lg">数据分析报告生成</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {getStatusBadge()}
            {overallStatus === 'complete' && reportPath && (
              <>
                <Dialog open={isEditing} onOpenChange={setIsEditing}>
                  <DialogTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleStartEdit}
                      className="flex items-center gap-1"
                    >
                      <Edit className="h-4 w-4" />
                      编辑报告
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-6xl max-h-[90vh] overflow-hidden">
                    <DialogHeader>
                      <DialogTitle>编辑数据分析报告</DialogTitle>
                    </DialogHeader>
                    <div className="flex flex-col h-[75vh]">
                      <div className="flex-1 overflow-hidden">
                        <ReactQuill
                          value={editedContent}
                          onChange={setEditedContent}
                          theme="snow"
                          className="h-full report-editor"
                          modules={{
                            toolbar: [
                              [{ 'header': [1, 2, 3, 4, false] }],
                              ['bold', 'italic', 'underline', 'strike'],
                              [{ 'color': [] }, { 'background': [] }],
                              [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                              [{ 'indent': '-1'}, { 'indent': '+1' }],
                              ['blockquote', 'code-block'],
                              ['link', 'image', 'formula'],
                              [{ 'align': [] }],
                              ['clean']
                            ],
                          }}
                          formats={[
                            'header', 'bold', 'italic', 'underline', 'strike',
                            'color', 'background', 'list', 'bullet', 'indent',
                            'blockquote', 'code-block', 'link', 'image', 'formula', 'align'
                          ]}
                        />
                      </div>
                      <div className="flex justify-end gap-2 pt-4 border-t">
                        <Button
                          variant="outline"
                          onClick={handleCancelEdit}
                          disabled={isSaving}
                        >
                          <X className="h-4 w-4 mr-1" />
                          取消
                        </Button>
                        <Button
                          onClick={handleSaveEdit}
                          disabled={isSaving || !editedContent.trim()}
                        >
                          {isSaving ? (
                            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                          ) : (
                            <Save className="h-4 w-4 mr-1" />
                          )}
                          保存新版本
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      size="sm"
                      variant="outline"
                      className="flex items-center gap-1"
                    >
                      <Download className="h-4 w-4" />
                      下载报告
                      <ChevronDown className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleDownloadMarkdown}>
                      <FileText className="h-4 w-4 mr-2" />
                      下载 Markdown 格式
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleDownloadHtml}>
                      <Download className="h-4 w-4 mr-2" />
                      下载 HTML 格式
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}
          </div>
        </div>
        
        {overallStatus === 'generating' && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>生成进度</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>
        )}

        {overallStatus === 'complete' && duration && (
          <div className="text-sm text-muted-foreground">
            报告生成完成，耗时 {Math.round(duration)} 秒
          </div>
        )}
      </CardHeader>

      <CardContent>
        <div className="space-y-4">
          {/* 章节状态列表 - 横向排列 */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">报告章节</h4>
            <div className="flex flex-wrap gap-2">
              {sections.map((section) => (
                <div
                  key={section.id}
                  className={`flex items-center gap-1 px-3 py-1.5 rounded-full text-xs border transition-all ${
                    section.id === currentSection
                      ? 'bg-blue-100 dark:bg-blue-900 border-blue-300 dark:border-blue-700'
                      : 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                  }`}
                  title={section.status === 'error' && section.error ? section.error : undefined}
                >
                  {getStatusIcon(section.status)}
                  <span className="font-medium truncate max-w-24">{section.name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* 实时内容展示 */}
          {sections.some(s => s.content) && (
            <>
              <Separator />
              <div className="space-y-3 report-container">
                <h4 className="text-sm font-medium text-muted-foreground">报告内容预览</h4>
                <div className="report-preview-area">
                  <ScrollArea className="h-[600px] w-full rounded-md border p-4 report-scroll" ref={scrollAreaRef}>
                    <div className="space-y-6 max-w-full">
                      {sections
                        .filter(section => section.content)
                        .map((section) => (
                          <div
                            key={section.id}
                            className={`space-y-2 ${section.status === 'generating' ? 'generating-section' : ''}`}
                          >
                            <div className="flex items-center gap-2">
                              <h5 className="text-sm font-medium">{section.name}</h5>
                              {getStatusIcon(section.status)}
                              {section.status === 'generating' && (
                                <span className="typing-cursor text-primary"></span>
                              )}
                            </div>
                            <div className="report-content w-full max-w-full overflow-hidden">
                              <ReactMarkdown
                                // 预处理内容，处理各种数学公式格式
                                children={(() => {
                                  const originalContent = section.content || '';
                                  const processedContent = originalContent
                                    .replace(/\\\[([\s\S]*?)\\\]/g, '$$$$1$$') // \[...\] -> $$...$$
                                    .replace(/\\\(([\s\S]*?)\\\)/g, '$$$1$$'); // \(...\) -> $...$

                                  // 调试信息
                                  if (originalContent !== processedContent) {
                                    console.log('[Math] 原始内容:', originalContent.substring(0, 200));
                                    console.log('[Math] 处理后内容:', processedContent.substring(0, 200));
                                  }

                                  return processedContent;
                                })()}
                                remarkPlugins={[remarkGfm, remarkMath]}
                                rehypePlugins={[rehypeHighlight, rehypeRaw, rehypeKatex]}
                                components={{
                                  // 自定义LaTeX渲染组件
                                  code: ({ className, children, ...props }: React.HTMLProps<HTMLElement>) => {
                                    const match = /language-(\w+)/.exec(className || '');
                                    const isLatex = match && (match[1] === 'latex' || match[1] === 'math');

                                    if (isLatex) {
                                      // LaTeX渲染逻辑
                                      return (
                                        <div className="latex-block">
                                          <div className="text-xs text-muted-foreground mb-2">LaTeX公式:</div>
                                          <code className="text-sm font-mono">{children}</code>
                                        </div>
                                      );
                                    }

                                    return (
                                      <code className={className} {...props}>
                                        {children}
                                      </code>
                                    );
                                  },
                                  // 处理数学公式 - KaTeX会自动处理，这里只是添加样式
                                  span: ({ className, children, ...props }: React.HTMLProps<HTMLSpanElement>) => {
                                    if (className?.includes('math')) {
                                      console.log('[LaTeX] 检测到数学公式:', children);
                                      return (
                                        <span className={`${className} math-formula`} {...props}>
                                          {children}
                                        </span>
                                      );
                                    }
                                    return <span className={className} {...props}>{children}</span>;
                                  }
                                }}
                              />
                            </div>
                            {section !== sections.filter(s => s.content).slice(-1)[0] && (
                              <Separator className="my-4" />
                            )}
                          </div>
                        ))}
                      {/* 滚动锚点 */}
                      <div ref={contentEndRef} />
                    </div>
                  </ScrollArea>
                </div>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
