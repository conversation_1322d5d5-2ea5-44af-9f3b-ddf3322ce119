#!/usr/bin/env python3
"""测试反思审查的流式生成功能"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.agents.reflection_agent import ReflectionAgent
from src.models.state_models import AnalysisState
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def test_reflection_streaming():
    """测试反思审查的流式生成功能"""
    print("🧠 测试反思审查流式生成功能...")
    
    # 创建测试状态
    test_state = AnalysisState(
        task_id=f"test_streaming_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        original_query="分析电商用户购买行为数据，识别影响转化率的关键因素",
        dataframe_path="test_ecommerce.csv",
        plan={
            "steps": [
                {"step": 1, "objective": "数据预处理", "description": "清洗和准备数据"},
                {"step": 2, "objective": "探索性分析", "description": "分析用户行为模式"},
                {"step": 3, "objective": "转化率建模", "description": "建立预测模型"}
            ]
        },
        current_step=3,
        executed_steps=[
            {
                "step": 1,
                "code": "df = pd.read_csv('data.csv')\ndf.info()",
                "result": "数据加载成功，10000条记录",
                "insights": ["数据质量良好"]
            },
            {
                "step": 2,
                "code": "df.describe()\nsns.heatmap(df.corr())",
                "result": "生成相关性热力图",
                "insights": ["发现关键相关性"]
            },
            {
                "step": 3,
                "code": "from sklearn.ensemble import RandomForestClassifier\nmodel.fit(X, y)",
                "result": "模型训练完成，AUC=0.85",
                "insights": ["模型性能良好"]
            }
        ],
        chroma_collection_name="test_streaming",
        errors=[],
        insights=["分析完成"],
        data_summary={
            "shape": [10000, 8],
            "columns": ["user_id", "age", "gender", "session_time", "pages_viewed", "cart_items", "purchased", "amount"],
            "dtypes": {
                "user_id": "int64",
                "age": "float64",
                "gender": "object",
                "session_time": "float64",
                "pages_viewed": "int64",
                "cart_items": "int64",
                "purchased": "int64",
                "amount": "float64"
            }
        },
        final_report={
            "title_and_abstract": "电商用户转化率分析\n\n本研究分析了10000名用户的购买行为数据。",
            "introduction": "电商转化率是衡量平台成功的关键指标。",
            "data_description": "数据集包含用户基本信息和行为数据。",
            "exploratory_analysis": "通过探索性分析发现了用户行为模式。",
            "modeling_and_results": "使用随机森林模型预测用户转化。",
            "discussion": "模型结果显示会话时长是关键因素。",
            "conclusion": "建议优化用户体验以提升转化率。"
        }
    )
    
    # 创建反思智能体
    reflection_agent = ReflectionAgent()
    
    try:
        print(f"📊 任务ID: {test_state['task_id']}")
        print("🔄 开始执行反思审查...")
        
        # 执行反思审查
        result = await reflection_agent.execute(test_state)
        
        print("✅ 反思审查完成!")
        
        # 显示结果
        evaluation = result.get("reflection_evaluation", {})
        reflection_insights = result.get("reflection_insights", {})
        improved_report_path = result.get("improved_report_path", "")
        
        print(f"📈 评估结果:")
        print(f"  总体评分: {evaluation.get('overall_score', 'N/A')}/10")
        print(f"  关键问题: {len(evaluation.get('critical_issues', []))} 个")
        
        print(f"💡 反思洞察:")
        strategies = reflection_insights.get("improvement_strategies", {})
        print(f"  改进策略: {len(strategies)} 个")
        
        print(f"📄 改进报告: {improved_report_path}")
        
        # 检查改进报告是否存在
        if improved_report_path and Path(improved_report_path).exists():
            print("✅ 改进报告文件已生成")
            
            # 显示改进报告的部分内容
            with open(improved_report_path, 'r', encoding='utf-8') as f:
                improved_data = json.load(f)
            
            print("\n📋 改进报告预览:")
            for section, content in improved_data.items():
                if content:
                    preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"  {section}: {preview}")
        
        return True
        
    except Exception as e:
        print(f"❌ 反思审查失败: {str(e)}")
        logger.error(f"反思审查测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始反思审查流式生成测试\n")
    
    success = await test_reflection_streaming()
    
    print(f"\n📊 测试结果:")
    print(f"  反思流式生成: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 反思审查流式生成功能正常工作!")
        return 0
    else:
        print("\n⚠️  测试失败，请检查日志。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
