"""WebSocket处理器"""

import json
import asyncio
from typing import Dict, Set, Any
from fastapi import WebSocket, WebSocketDisconnect
from src.models.api_models import WebSocketMessage, NodeUpdate
from src.utils.logger import get_logger

logger = get_logger(__name__)


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        """初始化管理器"""
        self.active_connections: Dict[str, Set[WebSocket]] = {}
        self.connection_tasks: Dict[str, Set[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, task_id: str) -> None:
        """建立WebSocket连接"""
        await websocket.accept()
        
        if task_id not in self.active_connections:
            self.active_connections[task_id] = set()
            self.connection_tasks[task_id] = set()
        
        self.active_connections[task_id].add(websocket)
        self.connection_tasks[task_id].add(websocket)
        
        logger.info(f"WebSocket连接建立: 任务ID {task_id}, 连接数: {len(self.active_connections[task_id])}")
    
    def disconnect(self, websocket: WebSocket, task_id: str) -> None:
        """断开WebSocket连接"""
        if task_id in self.active_connections:
            self.active_connections[task_id].discard(websocket)
            self.connection_tasks[task_id].discard(websocket)
            
            # 如果没有连接了，清理任务
            if not self.active_connections[task_id]:
                del self.active_connections[task_id]
                del self.connection_tasks[task_id]
        
        logger.info(f"WebSocket连接断开: 任务ID {task_id}")
    
    async def send_message(self, task_id: str, message: WebSocketMessage) -> None:
        """向特定任务的所有连接发送消息"""
        if task_id not in self.active_connections:
            return

        try:
            # 使用Pydantic的model_dump_json()方法进行序列化，自动处理datetime等特殊类型
            message_json = message.model_dump_json()
        except Exception as e:
            logger.error(f"消息序列化失败: {str(e)}, 消息内容: {message}")
            return

        disconnected_connections = set()

        for connection in self.active_connections[task_id].copy():
            try:
                await connection.send_text(message_json)
            except Exception as e:
                logger.warning(f"发送WebSocket消息失败: {str(e)}")
                disconnected_connections.add(connection)

        # 清理断开的连接
        for connection in disconnected_connections:
            self.disconnect(connection, task_id)
    
    async def send_node_update(self, task_id: str, node_update: NodeUpdate) -> None:
        """发送节点更新消息"""
        try:
            message = WebSocketMessage(
                event="node_update",
                payload=node_update.model_dump()
            )
            await self.send_message(task_id, message)
            logger.debug(f"节点更新消息已发送: {node_update.node_id}")
        except Exception as e:
            logger.error(f"发送节点更新失败: {str(e)}")
            # 不抛出异常，避免中断工作流
    
    async def send_task_complete(self, task_id: str, results: Dict) -> None:
        """发送任务完成消息"""
        message = WebSocketMessage(
            event="task_complete",
            payload={
                "task_id": task_id,
                "results": results
            }
        )
        await self.send_message(task_id, message)
    
    async def send_error(self, task_id: str, error: str) -> None:
        """发送错误消息"""
        message = WebSocketMessage(
            event="error",
            payload={
                "task_id": task_id,
                "error": error
            }
        )
        await self.send_message(task_id, message)
    
    async def send_progress(self, task_id: str, progress: Dict) -> None:
        """发送进度更新"""
        message = WebSocketMessage(
            event="progress",
            payload={
                "task_id": task_id,
                **progress
            }
        )
        await self.send_message(task_id, message)

    async def send_result_card(self, task_id: str, card_data: Dict) -> None:
        """发送结果卡片"""
        message = WebSocketMessage(
            event="result_card",
            payload={
                "task_id": task_id,
                "card": card_data
            }
        )
        await self.send_message(task_id, message)
        logger.debug(f"结果卡片已发送: {card_data.get('type', 'unknown')}")

    def get_connection_count(self, task_id: str) -> int:
        """获取特定任务的连接数"""
        return len(self.active_connections.get(task_id, set()))
    
    def get_total_connections(self) -> int:
        """获取总连接数"""
        return sum(len(connections) for connections in self.active_connections.values())
    
    async def broadcast_system_message(self, message: str) -> None:
        """广播系统消息给所有连接"""
        system_message = WebSocketMessage(
            event="system_message",
            payload={"message": message}
        )
        
        for task_id in list(self.active_connections.keys()):
            await self.send_message(task_id, system_message)


# 创建全局WebSocket管理器实例
websocket_manager = WebSocketManager()


async def websocket_endpoint(websocket: WebSocket, task_id: str) -> None:
    """WebSocket端点处理函数"""
    await websocket_manager.connect(websocket, task_id)
    
    try:
        # 发送连接确认消息
        welcome_message = WebSocketMessage(
            event="connected",
            payload={
                "task_id": task_id,
                "message": "WebSocket连接已建立"
            }
        )
        await websocket_manager.send_message(task_id, welcome_message)
        
        # 保持连接活跃
        while True:
            try:
                # 等待客户端消息（心跳检测）
                data = await asyncio.wait_for(websocket.receive_text(), timeout=30.0)
                
                # 处理客户端消息
                try:
                    client_message = json.loads(data)
                    if client_message.get("type") == "ping":
                        # 响应心跳
                        pong_message = WebSocketMessage(
                            event="pong",
                            payload={"timestamp": client_message.get("timestamp")}
                        )
                        await websocket_manager.send_message(task_id, pong_message)
                except json.JSONDecodeError:
                    logger.warning(f"收到无效的JSON消息: {data}")
                
            except asyncio.TimeoutError:
                # 发送心跳检测
                ping_message = WebSocketMessage(
                    event="ping",
                    payload={"timestamp": asyncio.get_event_loop().time()}
                )
                await websocket_manager.send_message(task_id, ping_message)
                
    except WebSocketDisconnect:
        logger.info(f"WebSocket客户端主动断开连接: 任务ID {task_id}")
    except Exception as e:
        logger.error(f"WebSocket连接异常: 任务ID {task_id}, 错误: {str(e)}")
    finally:
        websocket_manager.disconnect(websocket, task_id)
