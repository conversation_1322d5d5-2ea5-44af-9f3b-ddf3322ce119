import { useState, useEffect } from 'react';
import { Brain, CheckCircle, Circle, Edit3, Plus, Trash2, Clock, XCircle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ResultCard, AnalysisPlan, PlanApprovalRequest } from '@/types/analysis';
import { cn } from '@/lib/utils';
import { apiService } from '@/lib/api';
import { toast } from 'sonner';

interface PlanCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

interface PlanStep {
  step: number;
  objective: string;
  agent: string;
  dependencies: number[];
  description?: string;
  estimated_time?: number;
  status?: 'pending' | 'running' | 'completed';
}

export function PlanCard({ card, isHighlighted }: PlanCardProps) {
  // 添加调试信息
  console.log('PlanCard received card:', card);

  // 处理来自WebSocket的数据结构
  const planData = card.content.plan || card.content;
  console.log('PlanCard planData:', planData);

  // 安全地处理数据结构
  if (!planData || typeof planData !== 'object') {
    console.error('PlanCard: Invalid planData:', planData);
    return (
      <Card className="result-card border-destructive/50">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            分析计划格式错误，无法显示
          </div>
        </CardContent>
      </Card>
    );
  }

  // 获取步骤数组 - 支持两种数据结构
  const steps = planData.steps || planData;

  if (!Array.isArray(steps)) {
    console.error('PlanCard: steps is not an array:', steps);
    return (
      <Card className="result-card border-destructive/50">
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            分析计划步骤格式错误，无法显示
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "result-card animate-fade-in-up hover:shadow-xl transition-all duration-300",
      isHighlighted && "ring-2 ring-primary shadow-lg shadow-primary/20"
    )}>
      <CardHeader className="bg-gradient-to-r from-accent/5 to-primary/5 border-b border-border/50">
        <CardTitle className="flex items-center gap-3">
          <div className="relative">
            <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-accent to-primary flex items-center justify-center shadow-lg">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div className="absolute -top-1 -right-1 w-4 h-4 rounded-full bg-warning flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            </div>
          </div>
          <div className="flex-1">
            <div className="font-bold text-foreground">分析计划</div>
            <div className="text-sm text-muted-foreground font-normal">智能生成的分析执行方案</div>
          </div>
          <Badge variant="outline" className="bg-white/50 border-accent/20 text-accent font-semibold shadow-sm">
            {steps.length} 步骤
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={step.step} className="relative">
              {/* Connection Line */}
              {index < steps.length - 1 && (
                <div className="absolute left-4 top-8 w-0.5 h-6 bg-border" />
              )}
              
              <div className="flex items-start gap-4">
                {/* Step Icon */}
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border-2 bg-background",
                  step.status === 'completed' && "border-success bg-success/10",
                  step.status === 'running' && "border-primary bg-primary/10",
                  step.status === 'pending' && "border-muted-foreground bg-muted/10",
                  !step.status && "border-muted-foreground bg-muted/10"
                )}>
                  {step.status === 'completed' ? (
                    <CheckCircle className="w-4 h-4 text-success" />
                  ) : (
                    <span className={cn(
                      "text-sm font-semibold",
                      step.status === 'running' && "text-primary",
                      step.status === 'pending' && "text-muted-foreground",
                      !step.status && "text-muted-foreground"
                    )}>
                      {step.step}
                    </span>
                  )}
                </div>

                {/* Step Content */}
                <div className="flex-1 pb-4">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium text-base leading-relaxed mb-1">
                        {step.objective}
                      </h4>
                      {step.description && (
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {step.description}
                        </p>
                      )}
                    </div>
                    {step.status && (
                      <Badge
                        variant={
                          step.status === 'completed' ? 'default' :
                          step.status === 'running' ? 'secondary' : 'outline'
                        }
                        className="ml-2 text-xs flex-shrink-0"
                      >
                        {step.status}
                      </Badge>
                    )}
                  </div>
                  
                  <div className="flex items-center gap-3 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Circle className="w-3 h-3" />
                      <span>{step.agent}</span>
                    </div>
                    
                    {step.dependencies.length > 0 && (
                      <div className="flex items-center gap-1">
                        <span>Depends on:</span>
                        <div className="flex gap-1">
                          {step.dependencies.map((dep) => (
                            <Badge key={dep} variant="outline" className="text-xs px-1 py-0">
                              {dep}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

// 计划审批卡片组件
interface PlanApprovalCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
  taskId: string;
}

interface PlanApprovalContent {
  plan: AnalysisPlan;
  waiting_for_user: boolean;
  message: string;
}

export function PlanApprovalCard({ card, isHighlighted, taskId }: PlanApprovalCardProps) {
  const content = card.content as PlanApprovalContent;
  const [showEdit, setShowEdit] = useState(false);
  const [editedSteps, setEditedSteps] = useState(content.plan.steps || []);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [executionTime, setExecutionTime] = useState<number | null>(null);

  // 当卡片内容更新时，同步更新本地状态
  useEffect(() => {
    setEditedSteps(content.plan.steps || []);
    // 如果计划已确认，退出编辑模式
    if (!content.waiting_for_user) {
      setShowEdit(false);
      setIsSubmitting(false);
    }
  }, [content.plan.steps, content.waiting_for_user]);

  const handleApprove = async (approved: boolean, useModified = false) => {
    setIsSubmitting(true);
    const startTime = Date.now();

    try {
      const request: PlanApprovalRequest = {
        approved,
        modified_plan: useModified ? {
          steps: editedSteps,
          estimated_time: content.plan.estimated_time,
          complexity: content.plan.complexity
        } : undefined
      };

      const response = await apiService.approvePlan(taskId, request);
      const executionTime = ((Date.now() - startTime) / 1000).toFixed(2);

      // 更新执行时间
      setExecutionTime(parseFloat(executionTime));

      if (response.success) {
        toast.success(approved ? '计划已确认，开始执行分析' : '计划已拒绝');
      } else {
        toast.error('操作失败，请重试');
      }
    } catch (error) {
      console.error('审批计划失败:', error);
      // 即使出错也设置执行时间
      const executionTime = ((Date.now() - startTime) / 1000).toFixed(2);
      setExecutionTime(parseFloat(executionTime));

      // 显示更友好的错误消息
      if (error instanceof Error && error.message.includes('超时')) {
        toast.warning('请求已发送，但响应超时。分析将在后台继续执行。');
      } else {
        toast.error('操作失败，请重试');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const updateStep = (index: number, field: string, value: string) => {
    const newSteps = [...editedSteps];
    newSteps[index] = { ...newSteps[index], [field]: value };
    setEditedSteps(newSteps);
  };

  const addStep = () => {
    const newStep = {
      step: editedSteps.length + 1,
      objective: '',
      description: '',
      expected_output: ''
    };
    setEditedSteps([...editedSteps, newStep]);
  };

  const removeStep = (index: number) => {
    const newSteps = editedSteps.filter((_, i) => i !== index);
    const renumberedSteps = newSteps.map((step, i) => ({ ...step, step: i + 1 }));
    setEditedSteps(renumberedSteps);
  };

  return (
    <Card className={cn(
      "result-card animate-fade-in-up border-0 shadow-lg bg-card",
      isHighlighted && "ring-2 ring-primary ring-opacity-50"
    )}>
      <CardHeader className="pb-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-t-lg">
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 bg-white/20 rounded-lg">
            <Brain className="w-5 h-5" />
          </div>
          <div className="flex-1">
            <h2 className="text-lg font-semibold">分析计划确认</h2>
            <p className="text-blue-100 text-sm mt-1">
              {content.waiting_for_user
                ? "请确认或修改您的数据分析计划"
                : "分析计划已确认，正在执行分析"
              }
            </p>
          </div>
          <div className="flex items-center gap-2">
            {executionTime !== null && !content.waiting_for_user && (
              <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">
                <Clock className="w-3 h-3 mr-1" />
                {executionTime}秒
              </Badge>
            )}

            <Badge className={content.waiting_for_user
              ? "bg-amber-500 text-amber-900 border-0"
              : "bg-green-500 text-green-900 border-0"
            }>
              {content.waiting_for_user ? (
                <>
                  <Clock className="w-3 h-3 mr-1" />
                  等待确认
                </>
              ) : (
                <>
                  <CheckCircle className="w-3 h-3 mr-1" />
                  已确认
                </>
              )}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="p-6 space-y-6">
        {/* 计划预览 */}
        <div className="bg-muted/50 rounded-lg p-4 border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="font-semibold text-foreground">分析计划 ({content.plan.steps?.length || 0} 步)</h3>
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="w-4 h-4" />
              约 {content.plan.estimated_time} 分钟
            </div>
          </div>

          <div className="space-y-3">
            {(showEdit ? editedSteps : content.plan.steps)?.map((step, index) => (
              <div key={index} className="bg-background rounded-lg p-3 border">
                <div className="flex items-start gap-3">
                  <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-semibold">
                    {step.step}
                  </div>
                  <div className="flex-1 space-y-2">
                    {showEdit ? (
                      <>
                        <Input
                          value={step.objective}
                          onChange={(e) => updateStep(index, 'objective', e.target.value)}
                          placeholder="步骤目标"
                          className="font-medium"
                        />
                        <Textarea
                          value={step.description}
                          onChange={(e) => updateStep(index, 'description', e.target.value)}
                          placeholder="详细描述"
                          rows={2}
                        />
                      </>
                    ) : (
                      <>
                        <h4 className="font-medium text-foreground">{step.objective}</h4>
                        <p className="text-sm text-muted-foreground">{step.description}</p>
                      </>
                    )}
                  </div>
                  {showEdit && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeStep(index)}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {showEdit && (
            <Button
              variant="outline"
              onClick={addStep}
              className="w-full mt-4 border-dashed border-2"
            >
              <Plus className="w-4 h-4 mr-2" />
              添加步骤
            </Button>
          )}
        </div>

        {/* 操作按钮 - 只在等待用户确认时显示 */}
        {content.waiting_for_user && (
          <div className="space-y-4">
            {!showEdit ? (
              <div className="grid grid-cols-2 gap-4">
                <Button
                  onClick={() => handleApprove(true)}
                  disabled={isSubmitting}
                  className="h-12 bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  {isSubmitting ? '处理中...' : '直接执行'}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setShowEdit(true)}
                  disabled={isSubmitting}
                  className="h-12"
                >
                  <Edit3 className="w-4 h-4 mr-2" />
                  修改计划
                </Button>
              </div>
            ) : (
              <div className="grid grid-cols-3 gap-3">
                <Button
                  onClick={() => handleApprove(true, true)}
                  disabled={isSubmitting}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  {isSubmitting ? '处理中...' : '确认执行'}
                </Button>

                <Button
                  variant="outline"
                  onClick={() => setShowEdit(false)}
                  disabled={isSubmitting}
                >
                  取消修改
                </Button>

                <Button
                  variant="outline"
                  onClick={() => handleApprove(false)}
                  disabled={isSubmitting}
                  className="border-destructive text-destructive hover:bg-destructive/10"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  拒绝
                </Button>
              </div>
            )}
          </div>
        )}

        {/* 已确认状态显示 */}
        {!content.waiting_for_user && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center gap-2 text-green-700">
              <CheckCircle className="w-5 h-5" />
              <span className="font-medium">{content.message}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}