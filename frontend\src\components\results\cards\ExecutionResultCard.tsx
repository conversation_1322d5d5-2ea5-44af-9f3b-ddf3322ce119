import { useState } from 'react';
import { Play, BarChart3, Download, <PERSON>, EyeOff } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar } from 'recharts';
import { ResultCard, ExecutionResult } from '@/types/analysis';
import { cn } from '@/lib/utils';
import apiService from '@/lib/api';

interface ExecutionResultCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

interface LegacyExecutionResult {
  output?: string;
  plot_data?: any;
  table_data?: any[];
  chart_type?: 'line' | 'bar' | 'scatter';
  success: boolean;
}

export function ExecutionResultCard({ card, isHighlighted }: ExecutionResultCardProps) {
  const [showOutput, setShowOutput] = useState(true);

  // 处理新旧数据结构
  const data = card.content as ExecutionResult | LegacyExecutionResult;

  // 判断是否为新的ExecutionResult结构
  const isNewFormat = 'stdout' in data || 'stderr' in data || 'plots' in data;

  const getOutput = () => {
    if (isNewFormat) {
      const newData = data as ExecutionResult;
      return newData.stdout || newData.stderr || '';
    }
    return (data as LegacyExecutionResult).output || '';
  };

  const getSuccess = () => {
    if (isNewFormat) {
      return (data as ExecutionResult).success;
    }
    return (data as LegacyExecutionResult).success;
  };

  const getPlots = () => {
    if (isNewFormat) {
      return (data as ExecutionResult).plots || [];
    }
    return (data as LegacyExecutionResult).plot_data ? [data.plot_data] : [];
  };

  const getExecutionTime = () => {
    if (isNewFormat) {
      return (data as ExecutionResult).execution_time;
    }
    return undefined;
  };

  const renderChart = () => {
    const plots = getPlots();
    if (plots.length === 0) return null;

    // 如果是新格式，plots包含文件路径
    if (isNewFormat && typeof plots[0] === 'string') {
      return (
        <div className="space-y-4">
          {plots.map((plotPath: string, index: number) => (
            <div key={index} className="border rounded-lg overflow-hidden">
              <img
                src={apiService.getStaticFileUrl(plotPath)}
                alt={`图表 ${index + 1}`}
                className="w-full h-auto"
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            </div>
          ))}
        </div>
      );
    }

    // 旧格式的图表数据
    const chartData = Array.isArray(plots[0]) ? plots[0] : [plots[0]];

    const legacyData = data as LegacyExecutionResult;
    return (
      <div className="h-64 w-full">
        <ResponsiveContainer width="100%" height="100%">
          {legacyData.chart_type === 'bar' ? (
            <BarChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
              <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" />
              <YAxis stroke="hsl(var(--muted-foreground))" />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px'
                }}
              />
              <Bar dataKey="value" fill="hsl(var(--primary))" />
            </BarChart>
          ) : (
            <LineChart data={chartData}>
              <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
              <XAxis dataKey="name" stroke="hsl(var(--muted-foreground))" />
              <YAxis stroke="hsl(var(--muted-foreground))" />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'hsl(var(--card))',
                  border: '1px solid hsl(var(--border))',
                  borderRadius: '8px'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke="hsl(var(--primary))" 
                strokeWidth={2}
                dot={{ fill: 'hsl(var(--primary))' }}
              />
            </LineChart>
          )}
        </ResponsiveContainer>
      </div>
    );
  };

  const renderTable = () => {
    if (!data.table_data || !Array.isArray(data.table_data) || data.table_data.length === 0) return null;

    const columns = Object.keys(data.table_data[0]);
    
    return (
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead key={column} className="font-mono text-xs">
                  {column}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {data.table_data.slice(0, 10).map((row, index) => (
              <TableRow key={index}>
                {columns.map((column) => (
                  <TableCell key={column} className="font-mono text-xs">
                    {row[column] !== null && row[column] !== undefined ? String(row[column]) : (
                      <span className="text-muted-foreground italic">null</span>
                    )}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
        {data.table_data.length > 10 && (
          <div className="text-center py-2 text-sm text-muted-foreground border-t">
            Showing 10 of {data.table_data.length} rows
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className={cn(
      "result-card animate-fade-in-up",
      isHighlighted && "ring-2 ring-primary"
    )}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="w-5 h-5 text-success" />
          {card.title}
          <div className="ml-auto flex items-center gap-2">
            {getExecutionTime() && (
              <Badge variant="outline" className="text-xs">
                {getExecutionTime()?.toFixed(2)}s
              </Badge>
            )}
            <Badge variant={getSuccess() ? "default" : "destructive"}>
              {getSuccess() ? "成功" : "失败"}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Charts */}
        {getPlots().length > 0 && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                可视化结果
              </h4>
              <Button variant="outline" size="sm">
                <Download className="w-4 h-4 mr-2" />
                导出
              </Button>
            </div>
            {renderChart()}
          </div>
        )}

        {/* Table */}
        {data.table_data && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Data Output</h4>
            {renderTable()}
          </div>
        )}

        {/* Text Output */}
        {getOutput() && (
          <Collapsible open={showOutput} onOpenChange={setShowOutput}>
            <div className="space-y-3">
              <CollapsibleTrigger asChild>
                <Button variant="ghost" size="sm" className="w-full justify-between">
                  <span className="text-sm font-medium">控制台输出</span>
                  {showOutput ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <div className="code-block max-h-48 overflow-y-auto">
                  <pre className="text-sm whitespace-pre-wrap">{getOutput()}</pre>
                </div>
              </CollapsibleContent>
            </div>
          </Collapsible>
        )}

        {/* Warning Output */}
        {isNewFormat && (data as ExecutionResult).warnings && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-yellow-600">警告信息</h4>
            <div className="code-block max-h-48 overflow-y-auto">
              <pre className="text-sm whitespace-pre-wrap text-yellow-800 bg-yellow-50 p-3 rounded-md border border-yellow-200" style={{ wordBreak: 'break-word', maxWidth: '100%' }}>
                {(data as ExecutionResult).warnings}
              </pre>
            </div>
          </div>
        )}

        {/* Error Output */}
        {isNewFormat && (data as ExecutionResult).stderr && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-destructive">错误输出</h4>
            <div className="code-block max-h-48 overflow-y-auto">
              <pre className="text-sm whitespace-pre-wrap text-destructive bg-destructive/10 p-3 rounded-md border border-red-200" style={{ wordBreak: 'break-word', maxWidth: '100%' }}>
                {(data as ExecutionResult).stderr}
              </pre>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}