"""规划智能体"""

import json
from typing import Dict, Any
from langchain_core.messages import HumanMessage, SystemMessage
from src.agents.base_agent import BaseAgent
from src.models.state_models import AnalysisState


class PlannerAgent(BaseAgent):
    """规划智能体 - 实现Plan-and-Execute框架的规划部分"""
    
    def __init__(self):
        super().__init__(
            name="规划智能体",
            description="根据用户需求和数据特征，制定详细的数据分析计划"
        )
    
    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行分析规划"""
        self._log_execution("开始制定分析计划")
        
        try:
            # 生成分析计划
            plan = await self._generate_analysis_plan(
                state["original_query"],
                state.get("data_summary", {})
            )
            
            # 更新状态
            updated_state = {
                "plan": plan,
                "current_step": 0
            }
            
            self._log_execution("分析计划制定完成", f"共{len(plan['steps'])}个步骤")
            return updated_state
            
        except Exception as e:
            error_msg = self._format_error_message(str(e))
            self._log_execution("分析计划制定失败", str(e))
            return {
                "errors": state.get("errors", []) + [error_msg]
            }
    
    async def _generate_analysis_plan(self, query: str, data_summary: Dict[str, Any]) -> Dict[str, Any]:
        """生成分析计划"""
        system_prompt = self._create_system_prompt(
            "数据分析规划师",
            """
你需要根据用户的分析需求和数据特征，制定一个详细的分析计划。

计划可以包含以下步骤类型，可以全部都有，也可以增加或减少（灵活处理）：
1. data_preprocessing - 数据预处理（处理缺失值、异常值、数据类型转换等）
2. exploratory_analysis - 探索性数据分析（描述性统计、分布分析、相关性分析等）
3. feature_engineering - 特征工程（特征选择、特征创建、特征变换等）
4. modeling - 建模分析（回归、分类、聚类等机器学习模型）
5. visualization - 数据可视化（图表生成、结果展示等）
6. interpretation - 结果解释（模型解释、业务洞察等）

请返回JSON格式的计划，包含：
- steps: 步骤列表，每个步骤包含step、objective、agent、dependencies、estimated_time
- total_estimated_time: 总预估时间（分钟）
- complexity: 复杂度等级（simple/medium/complex）

示例格式：
{
  "steps": [
    {
      "step": 1,
      "objective": "数据预处理和清洗",
      "agent": "PreprocessAgent",
      "dependencies": [],
      "estimated_time": 5,
      "description": "处理缺失值和异常值"
    }
  ],
  "total_estimated_time": 25,
  "complexity": "medium"
}
            """
        )
        
        # 构建数据摘要文本
        data_info = ""
        if data_summary:
            data_info = f"""
数据基本信息：
- 数据形状: {data_summary.get('shape', '未知')}
- 列名: {', '.join(data_summary.get('columns', []))}
- 数据类型: {data_summary.get('dtypes', {})}
- 缺失值情况: {data_summary.get('missing_values', {})}
            """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"""
用户分析需求: {query}

{data_info}

请制定详细的分析计划。
            """)
        ]
        
        try:
            response = await self.llm.ainvoke(messages)
            self.logger.info(f"LLM响应内容: {response.content[:500]}...")

            # 清理响应内容，移除markdown代码块标记
            content = response.content.strip()
            if content.startswith('```json'):
                content = content[7:]  # 移除 ```json
            if content.startswith('```'):
                content = content[3:]   # 移除 ```
            if content.endswith('```'):
                content = content[:-3]  # 移除结尾的 ```
            content = content.strip()

            # 解析JSON响应
            plan = json.loads(content)
            self.logger.info(f"成功解析分析计划: {len(plan.get('steps', []))}个步骤")
            return plan

        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {str(e)}, 响应内容: {response.content}")
            # 如果JSON解析失败，创建一个默认计划
            return self._create_default_plan(query)
        except Exception as e:
            self.logger.error(f"LLM调用失败: {str(e)}")
            # 如果LLM调用失败，创建一个默认计划
            return self._create_default_plan(query)

    def _create_default_plan(self, query: str) -> Dict[str, Any]:
        """创建默认分析计划"""
        self.logger.warning(f"使用默认分析计划，用户查询: {query}")

        # 根据查询内容调整默认计划
        steps = [
            {
                "step": 1,
                "objective": "数据预处理和清洗",
                "agent": "PreprocessAgent",
                "dependencies": [],
                "estimated_time": 5,
                "description": "处理缺失值、异常值和数据类型转换，确保数据质量"
            },
            {
                "step": 2,
                "objective": "探索性数据分析",
                "agent": "AnalysisAgent",
                "dependencies": [1],
                "estimated_time": 10,
                "description": "生成描述性统计、分布分析和相关性分析"
            }
        ]

        # 根据查询内容添加特定步骤
        if any(keyword in query.lower() for keyword in ['模型', 'model', '预测', 'predict', '分类', 'classify', '回归', 'regression']):
            steps.append({
                "step": 3,
                "objective": "机器学习建模",
                "agent": "ModelingAgent",
                "dependencies": [2],
                "estimated_time": 15,
                "description": "构建和训练机器学习模型，进行预测分析"
            })
            steps.append({
                "step": 4,
                "objective": "模型评估和解释",
                "agent": "EvaluationAgent",
                "dependencies": [3],
                "estimated_time": 8,
                "description": "评估模型性能，解释模型结果和特征重要性"
            })
        else:
            steps.append({
                "step": 3,
                "objective": "深度数据分析",
                "agent": "AnalysisAgent",
                "dependencies": [2],
                "estimated_time": 12,
                "description": "进行深入的数据挖掘和模式识别"
            })

        steps.append({
            "step": len(steps) + 1,
            "objective": "结果解释和报告生成",
            "agent": "ReportAgent",
            "dependencies": list(range(1, len(steps) + 1)),
            "estimated_time": 5,
            "description": "汇总所有分析结果，生成综合性报告"
        })

        total_time = sum(step["estimated_time"] for step in steps)

        return {
            "steps": steps,
            "total_estimated_time": total_time,
            "complexity": "medium" if len(steps) <= 4 else "complex"
        }
