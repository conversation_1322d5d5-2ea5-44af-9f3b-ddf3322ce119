"""
时间序列分析智能体
使用Prophet和Darts进行专业的时间序列分析和预测
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

# 时间序列分析库
try:
    from prophet import Prophet
    PROPHET_AVAILABLE = True
except ImportError:
    PROPHET_AVAILABLE = False
    logging.warning("Prophet库未安装，时间序列预测功能受限")

try:
    from darts import TimeSeries
    from darts.models import (
        ExponentialSmoothing, 
        ARIMA as DartsARIMA,
        LinearRegressionModel,
        RandomForest,
        XGBModel
    )
    from darts.metrics import mape, mae, rmse
    from darts.utils.statistics import check_seasonality
    DARTS_AVAILABLE = True
except ImportError:
    DARTS_AVAILABLE = False
    logging.warning("Darts库未安装，高级时间序列分析功能受限")

# 统计分析
import statsmodels.api as sm
from statsmodels.tsa.seasonal import seasonal_decompose
from statsmodels.tsa.stattools import adfuller
from statsmodels.tsa.arima.model import ARIMA

from .base_agent import BaseAgent

logger = logging.getLogger(__name__)


class TimeSeriesAgent(BaseAgent):
    """时间序列分析智能体"""
    
    def __init__(self):
        super().__init__(
            name="TimeSeriesAgent",
            description="专业的时间序列分析和预测"
        )
        
    async def execute(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """执行时间序列分析"""
        try:
            logger.info(f"{self.name} 开始执行时间序列分析")
            
            # 获取数据
            df = state.get('cleaned_data')
            if df is None:
                raise ValueError("未找到清洗后的数据")
            
            # 检测时间序列数据
            time_info = await self._detect_time_series(df)
            if not time_info['has_time_series']:
                logger.warning("未检测到时间序列数据")
                return state
            
            ts_results = {}
            
            # 1. 基础时间序列分析
            basic_analysis = await self._basic_time_series_analysis(df, time_info)
            ts_results['basic_analysis'] = basic_analysis
            
            # 2. 季节性分解
            decomposition = await self._seasonal_decomposition(df, time_info)
            ts_results['decomposition'] = decomposition
            
            # 3. 平稳性检验
            stationarity = await self._stationarity_tests(df, time_info)
            ts_results['stationarity'] = stationarity
            
            # 4. Prophet预测（如果可用）
            if PROPHET_AVAILABLE:
                prophet_results = await self._prophet_forecast(df, time_info)
                ts_results['prophet_forecast'] = prophet_results
            
            # 5. Darts多模型比较（如果可用）
            if DARTS_AVAILABLE:
                darts_results = await self._darts_model_comparison(df, time_info)
                ts_results['darts_comparison'] = darts_results
            
            # 6. ARIMA模型
            arima_results = await self._arima_analysis(df, time_info)
            ts_results['arima_analysis'] = arima_results
            
            # 更新状态
            state['time_series_analysis'] = ts_results
            
            logger.info(f"{self.name} 时间序列分析完成")
            return state
            
        except Exception as e:
            logger.error(f"{self.name} 执行失败: {str(e)}")
            raise
    
    async def _detect_time_series(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检测时间序列数据"""
        time_info = {
            'has_time_series': False,
            'time_column': None,
            'value_columns': [],
            'frequency': None,
            'date_range': None
        }
        
        # 检测时间列
        datetime_cols = df.select_dtypes(include=['datetime64']).columns.tolist()
        
        # 如果没有datetime列，尝试解析可能的时间列
        if not datetime_cols:
            for col in df.columns:
                if any(keyword in col.lower() for keyword in ['date', 'time', '时间', '日期']):
                    try:
                        df[col] = pd.to_datetime(df[col])
                        datetime_cols.append(col)
                        break
                    except:
                        continue
        
        if datetime_cols:
            time_col = datetime_cols[0]
            time_info['has_time_series'] = True
            time_info['time_column'] = time_col
            
            # 获取数值列作为值列
            numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
            time_info['value_columns'] = numeric_cols
            
            # 分析频率
            if len(df) > 1:
                df_sorted = df.sort_values(time_col)
                time_diff = df_sorted[time_col].diff().dropna()
                if len(time_diff) > 0:
                    mode_diff = time_diff.mode()
                    if len(mode_diff) > 0:
                        time_info['frequency'] = str(mode_diff.iloc[0])
            
            # 日期范围
            time_info['date_range'] = {
                'start': df[time_col].min().isoformat(),
                'end': df[time_col].max().isoformat(),
                'periods': len(df)
            }
        
        return time_info
    
    async def _basic_time_series_analysis(self, df: pd.DataFrame, time_info: Dict) -> Dict[str, Any]:
        """基础时间序列分析"""
        try:
            time_col = time_info['time_column']
            value_cols = time_info['value_columns']
            
            if not value_cols:
                return {'error': '没有找到数值列进行分析'}
            
            # 按时间排序
            df_sorted = df.sort_values(time_col)
            
            results = {}
            
            for col in value_cols[:3]:  # 限制分析前3个数值列
                series_data = df_sorted[[time_col, col]].dropna()
                
                if len(series_data) < 2:
                    continue
                
                # 基础统计
                col_stats = {
                    'count': len(series_data),
                    'mean': float(series_data[col].mean()),
                    'std': float(series_data[col].std()),
                    'min': float(series_data[col].min()),
                    'max': float(series_data[col].max()),
                    'trend': self._calculate_trend(series_data[col])
                }
                
                # 缺失值分析
                total_expected = len(df_sorted)
                missing_count = total_expected - len(series_data)
                col_stats['missing_ratio'] = missing_count / total_expected
                
                results[col] = col_stats
            
            return results
            
        except Exception as e:
            logger.error(f"基础时间序列分析失败: {str(e)}")
            return {'error': str(e)}
    
    async def _seasonal_decomposition(self, df: pd.DataFrame, time_info: Dict) -> Dict[str, Any]:
        """季节性分解"""
        try:
            time_col = time_info['time_column']
            value_cols = time_info['value_columns']
            
            if not value_cols:
                return {'error': '没有找到数值列进行分解'}
            
            results = {}
            
            # 选择第一个数值列进行分解
            target_col = value_cols[0]
            df_sorted = df.sort_values(time_col).dropna(subset=[target_col])
            
            if len(df_sorted) < 24:  # 需要足够的数据点
                return {'error': '数据点不足，无法进行季节性分解'}
            
            # 设置时间索引
            ts_data = df_sorted.set_index(time_col)[target_col]
            
            # 尝试不同的周期
            for period in [12, 7, 4]:  # 月、周、季度
                if len(ts_data) >= 2 * period:
                    try:
                        decomposition = seasonal_decompose(
                            ts_data, 
                            model='additive', 
                            period=period
                        )
                        
                        results[f'period_{period}'] = {
                            'trend_strength': float(np.var(decomposition.trend.dropna()) / np.var(ts_data)),
                            'seasonal_strength': float(np.var(decomposition.seasonal) / np.var(ts_data)),
                            'residual_variance': float(np.var(decomposition.resid.dropna())),
                            'period': period
                        }
                        break
                    except:
                        continue
            
            return results
            
        except Exception as e:
            logger.error(f"季节性分解失败: {str(e)}")
            return {'error': str(e)}
    
    async def _stationarity_tests(self, df: pd.DataFrame, time_info: Dict) -> Dict[str, Any]:
        """平稳性检验"""
        try:
            time_col = time_info['time_column']
            value_cols = time_info['value_columns']
            
            results = {}
            
            for col in value_cols[:2]:  # 检验前2个数值列
                series_data = df.sort_values(time_col)[col].dropna()
                
                if len(series_data) < 10:
                    continue
                
                # ADF检验
                adf_result = adfuller(series_data)
                
                results[col] = {
                    'adf_statistic': float(adf_result[0]),
                    'adf_pvalue': float(adf_result[1]),
                    'is_stationary': adf_result[1] < 0.05,
                    'critical_values': {k: float(v) for k, v in adf_result[4].items()}
                }
            
            return results
            
        except Exception as e:
            logger.error(f"平稳性检验失败: {str(e)}")
            return {'error': str(e)}
    
    async def _prophet_forecast(self, df: pd.DataFrame, time_info: Dict) -> Dict[str, Any]:
        """Prophet预测"""
        try:
            time_col = time_info['time_column']
            value_cols = time_info['value_columns']
            
            if not value_cols:
                return {'error': '没有找到数值列进行预测'}
            
            # 选择第一个数值列
            target_col = value_cols[0]
            
            # 准备Prophet数据格式
            prophet_df = df[[time_col, target_col]].dropna()
            prophet_df = prophet_df.rename(columns={time_col: 'ds', target_col: 'y'})
            prophet_df = prophet_df.sort_values('ds')
            
            if len(prophet_df) < 10:
                return {'error': '数据点不足，无法进行Prophet预测'}
            
            # 创建和训练模型
            model = Prophet(
                daily_seasonality=False,
                weekly_seasonality=True,
                yearly_seasonality=True
            )
            model.fit(prophet_df)
            
            # 预测未来30天
            future = model.make_future_dataframe(periods=30)
            forecast = model.predict(future)
            
            # 提取预测结果
            forecast_data = forecast[['ds', 'yhat', 'yhat_lower', 'yhat_upper']].tail(30)
            
            results = {
                'forecast_periods': 30,
                'forecast_data': forecast_data.to_dict('records'),
                'model_components': {
                    'trend': forecast['trend'].iloc[-1],
                    'weekly': forecast.get('weekly', pd.Series([0])).iloc[-1],
                    'yearly': forecast.get('yearly', pd.Series([0])).iloc[-1]
                }
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Prophet预测失败: {str(e)}")
            return {'error': str(e)}
    
    async def _darts_model_comparison(self, df: pd.DataFrame, time_info: Dict) -> Dict[str, Any]:
        """Darts多模型比较"""
        try:
            time_col = time_info['time_column']
            value_cols = time_info['value_columns']
            
            if not value_cols:
                return {'error': '没有找到数值列进行预测'}
            
            # 准备数据
            target_col = value_cols[0]
            ts_df = df[[time_col, target_col]].dropna().sort_values(time_col)
            
            if len(ts_df) < 20:
                return {'error': '数据点不足，无法进行Darts分析'}
            
            # 创建Darts时间序列
            ts = TimeSeries.from_dataframe(
                ts_df, 
                time_col=time_col, 
                value_cols=[target_col]
            )
            
            # 分割训练和测试集
            train_size = int(0.8 * len(ts))
            train, test = ts[:train_size], ts[train_size:]
            
            models = {}
            
            # 指数平滑
            try:
                exp_smooth = ExponentialSmoothing()
                exp_smooth.fit(train)
                pred = exp_smooth.predict(len(test))
                models['ExponentialSmoothing'] = {
                    'mae': float(mae(test, pred)),
                    'mape': float(mape(test, pred)),
                    'rmse': float(rmse(test, pred))
                }
            except Exception as e:
                models['ExponentialSmoothing'] = {'error': str(e)}
            
            # 线性回归
            try:
                lr_model = LinearRegressionModel(lags=5)
                lr_model.fit(train)
                pred = lr_model.predict(len(test))
                models['LinearRegression'] = {
                    'mae': float(mae(test, pred)),
                    'mape': float(mape(test, pred)),
                    'rmse': float(rmse(test, pred))
                }
            except Exception as e:
                models['LinearRegression'] = {'error': str(e)}
            
            results = {
                'model_comparison': models,
                'best_model': self._find_best_ts_model(models),
                'train_size': train_size,
                'test_size': len(test)
            }
            
            return results
            
        except Exception as e:
            logger.error(f"Darts模型比较失败: {str(e)}")
            return {'error': str(e)}
    
    async def _arima_analysis(self, df: pd.DataFrame, time_info: Dict) -> Dict[str, Any]:
        """ARIMA模型分析"""
        try:
            time_col = time_info['time_column']
            value_cols = time_info['value_columns']
            
            if not value_cols:
                return {'error': '没有找到数值列进行ARIMA分析'}
            
            target_col = value_cols[0]
            series_data = df.sort_values(time_col)[target_col].dropna()
            
            if len(series_data) < 20:
                return {'error': '数据点不足，无法进行ARIMA分析'}
            
            # 自动选择ARIMA参数
            try:
                model = ARIMA(series_data, order=(1, 1, 1))
                fitted_model = model.fit()
                
                # 预测
                forecast = fitted_model.forecast(steps=10)
                
                results = {
                    'aic': float(fitted_model.aic),
                    'bic': float(fitted_model.bic),
                    'order': (1, 1, 1),
                    'forecast': forecast.tolist(),
                    'residuals_mean': float(fitted_model.resid.mean()),
                    'residuals_std': float(fitted_model.resid.std())
                }
                
                return results
                
            except Exception as e:
                return {'error': f'ARIMA拟合失败: {str(e)}'}
            
        except Exception as e:
            logger.error(f"ARIMA分析失败: {str(e)}")
            return {'error': str(e)}
    
    def _calculate_trend(self, series: pd.Series) -> str:
        """计算趋势方向"""
        if len(series) < 2:
            return 'unknown'
        
        # 简单线性回归计算趋势
        x = np.arange(len(series))
        slope = np.polyfit(x, series, 1)[0]
        
        if slope > 0.01:
            return 'increasing'
        elif slope < -0.01:
            return 'decreasing'
        else:
            return 'stable'
    
    def _find_best_ts_model(self, models: Dict) -> Optional[str]:
        """找出最佳时间序列模型"""
        best_model = None
        best_mae = float('inf')
        
        for model_name, metrics in models.items():
            if 'error' not in metrics and 'mae' in metrics:
                if metrics['mae'] < best_mae:
                    best_mae = metrics['mae']
                    best_model = model_name
        
        return best_model
