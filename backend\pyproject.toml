[project]
name = "ai-agent-analysis-backend"
version = "0.1.0"
description = "AI智能体数据分析平台后端服务"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "websockets>=12.0",
    "pydantic>=2.5.0",
    "python-multipart>=0.0.6",
    "python-dotenv>=1.0.0",
    "pandas>=2.1.0",
    "polars>=0.20.0",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "statsmodels>=0.14.0",
    "matplotlib>=3.7.0",
    "seaborn>=0.12.0",
    "plotly>=5.17.0",
    "scikit-learn>=1.3.0",
    "xgboost>=2.0.0",
    "lightgbm>=4.1.0",
    "catboost>=1.2.0",
    "prophet>=1.1.4",
    "darts>=0.27.0",
    "jupyter-client>=8.6.0",
    "ipykernel>=6.26.0",
    "langgraph>=0.2.74",
    "langchain>=0.3.0",
    "langchain-openai>=0.2.0",
    "chromadb>=0.5.0",
    "aiofiles>=23.2.0",
    "httpx>=0.25.0",
    "openpyxl>=3.1.0",
    "xlrd>=2.0.0",
    "pydantic-settings>=2.10.1",
    "pyarrow>=14.0.0",
    "fastparquet>=2023.10.0",
    "joblib>=1.3.0",
    "tqdm>=4.66.0",
    "psutil>=5.9.0",
    "nbformat>=4.2.0",
    "yellowbrick>=1.5",
]

[dependency-groups]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
