/* 报告生成卡片的自定义样式 */

.report-content {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  line-height: 1.7;
  color: hsl(var(--foreground) / 0.9);
  max-width: 100%;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.report-content h1 {
  font-size: 1.875rem;
  font-weight: 700;
  color: hsl(var(--primary) / 0.9);
  margin-bottom: 1.5rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid hsl(var(--border) / 0.3);
  position: relative;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.05), hsl(var(--primary) / 0.02));
  padding: 1rem;
  border-radius: 0.5rem;
  backdrop-filter: blur(10px);
}

.report-content h1::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 1rem;
  right: 1rem;
  height: 2px;
  background: linear-gradient(90deg, hsl(var(--primary) / 0.6), hsl(var(--primary) / 0.2), transparent);
  border-radius: 1px;
}

.report-content h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: hsl(var(--primary) / 0.85);
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  padding: 0.75rem 1rem;
  background: linear-gradient(135deg, hsl(var(--primary) / 0.08), hsl(var(--primary) / 0.03));
  border-left: 4px solid hsl(var(--primary) / 0.4);
  border-radius: 0.375rem;
  backdrop-filter: blur(5px);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.report-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: hsl(var(--primary) / 0.8);
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  padding: 0.5rem 0.75rem;
  background: hsl(var(--muted) / 0.3);
  border-radius: 0.25rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.report-content h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: hsl(var(--foreground) / 0.9);
  margin-bottom: 0.5rem;
  margin-top: 1.5rem;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.report-content p {
  margin-bottom: 1.25rem;
  text-align: justify;
  color: hsl(var(--foreground) / 0.85);
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.report-content code {
  background: hsl(var(--muted));
  color: hsl(var(--primary));
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid hsl(var(--border));
}

.report-content pre {
  background: hsl(var(--muted));
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1rem 0;
  overflow-x: auto;
  font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Consolas', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.report-content pre code {
  background: none;
  border: none;
  padding: 0;
  color: hsl(var(--foreground));
}

.report-content blockquote {
  border-left: 4px solid hsl(var(--primary));
  background: hsl(var(--muted) / 0.5);
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0 0.5rem 0.5rem 0;
  font-style: italic;
  position: relative;
}

.report-content blockquote::before {
  content: '"';
  font-size: 3rem;
  color: hsl(var(--primary) / 0.3);
  position: absolute;
  top: -0.5rem;
  left: 0.5rem;
  font-family: serif;
}

.report-content ul, .report-content ol {
  margin: 1rem 0;
  padding-left: 1.5rem;
}

.report-content li {
  margin-bottom: 0.5rem;
  position: relative;
}

.report-content ul li::marker {
  color: hsl(var(--primary));
}

.report-content ol li::marker {
  color: hsl(var(--primary));
  font-weight: 600;
}

.report-content table {
  width: 100%;
  max-width: 100%;
  border-collapse: collapse;
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid hsl(var(--border) / 0.3);
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.1);
  backdrop-filter: blur(5px);
  background: hsl(var(--background) / 0.8);
  table-layout: fixed;
}

.report-content th {
  background: linear-gradient(135deg, hsl(var(--muted) / 0.8), hsl(var(--muted) / 0.6));
  color: hsl(var(--foreground) / 0.9);
  font-weight: 600;
  padding: 0.75rem;
  text-align: left;
  border-bottom: 2px solid hsl(var(--primary) / 0.2);
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.report-content td {
  padding: 0.75rem;
  border-bottom: 1px solid hsl(var(--border) / 0.2);
  color: hsl(var(--foreground) / 0.8);
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
}

.report-content tr:hover {
  background: hsl(var(--primary) / 0.05);
  transition: background-color 0.2s ease;
}

.report-content strong {
  font-weight: 600;
  color: hsl(var(--primary));
}

.report-content em {
  font-style: italic;
  color: hsl(var(--muted-foreground));
}

/* 打字机效果的光标 */
.typing-cursor::after {
  content: '|';
  color: hsl(var(--primary));
  animation: blink 1s infinite;
  font-weight: 100;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 生成中的章节高亮 */
.generating-section {
  background: linear-gradient(90deg, 
    hsl(var(--primary) / 0.1) 0%, 
    hsl(var(--primary) / 0.05) 50%, 
    hsl(var(--primary) / 0.1) 100%);
  border-radius: 0.5rem;
  padding: 0.5rem;
  animation: pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 5px hsl(var(--primary) / 0.3);
  }
  50% { 
    box-shadow: 0 0 15px hsl(var(--primary) / 0.5);
  }
}

/* 滚动条样式 */
.report-scroll::-webkit-scrollbar {
  width: 8px;
}

.report-scroll::-webkit-scrollbar-track {
  background: hsl(var(--muted));
  border-radius: 4px;
}

.report-scroll::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.5);
  border-radius: 4px;
}

.report-scroll::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.7);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .report-content h1 {
    font-size: 1.5rem;
  }
  
  .report-content h2 {
    font-size: 1.25rem;
  }
  
  .report-content h3 {
    font-size: 1.125rem;
  }
  
  .report-content table {
    font-size: 0.875rem;
  }
  
  .report-content th,
  .report-content td {
    padding: 0.5rem;
  }
}

/* 报告容器固定宽度 */
.report-container {
  width: 100%;
  max-width: 100%;
  overflow: hidden;
}

.report-preview-area {
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  overflow-y: auto;
}

/* 编辑模式样式 */
.report-edit-mode {
  border: 2px dashed hsl(var(--primary) / 0.3);
  border-radius: 0.5rem;
  background: hsl(var(--primary) / 0.02);
}

.report-edit-toolbar {
  display: flex;
  gap: 0.5rem;
  padding: 0.75rem;
  background: hsl(var(--muted) / 0.5);
  border-bottom: 1px solid hsl(var(--border) / 0.3);
  border-radius: 0.5rem 0.5rem 0 0;
  backdrop-filter: blur(10px);
}

.report-edit-button {
  padding: 0.5rem 1rem;
  border-radius: 0.25rem;
  border: 1px solid hsl(var(--border) / 0.3);
  background: hsl(var(--background) / 0.8);
  color: hsl(var(--foreground));
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.report-edit-button:hover {
  background: hsl(var(--primary) / 0.1);
  border-color: hsl(var(--primary) / 0.3);
}

.report-edit-button.active {
  background: hsl(var(--primary) / 0.2);
  border-color: hsl(var(--primary) / 0.5);
  color: hsl(var(--primary));
}

/* 富文本编辑器样式 */
.report-editor {
  min-height: 400px;
  border: 1px solid hsl(var(--border) / 0.3);
  border-radius: 0.375rem;
  background: hsl(var(--background) / 0.95);
  backdrop-filter: blur(10px);
}

.report-editor .ql-toolbar {
  border-top: none;
  border-left: none;
  border-right: none;
  border-bottom: 1px solid hsl(var(--border) / 0.3);
  background: hsl(var(--muted) / 0.3);
}

.report-editor .ql-container {
  border: none;
  font-family: inherit;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .report-content pre {
    background: hsl(var(--muted));
  }

  .report-content table {
    box-shadow: 0 2px 8px hsl(var(--primary) / 0.2);
  }

  .report-edit-toolbar {
    background: hsl(var(--muted) / 0.7);
  }

  .report-editor {
    background: hsl(var(--background) / 0.98);
  }
}

/* LaTeX公式样式 */
.latex-block {
  margin: 1rem 0;
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--border));
  background: hsl(var(--muted) / 0.3);
  backdrop-filter: blur(5px);
}

.latex-block code {
  background: transparent;
  color: hsl(var(--foreground));
  font-family: 'Computer Modern', 'Latin Modern Math', 'Times New Roman', serif;
  font-size: 1rem;
  border: none;
}

/* KaTeX数学公式样式 - 增强版 */
.math-formula {
  font-family: 'KaTeX_Main', 'Computer Modern', 'Times New Roman', serif !important;
}

.katex {
  font-size: 1.1em !important;
  color: hsl(var(--foreground)) !important;
}

.katex-display {
  margin: 1.5rem 0 !important;
  text-align: center;
  padding: 1rem;
  background: hsl(var(--muted) / 0.3);
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.katex-html {
  color: hsl(var(--foreground)) !important;
}

/* 行内数学公式 */
.math-inline .katex {
  font-size: 1em !important;
  padding: 0.1rem 0.2rem;
}

/* 块级数学公式 */
.math-display .katex {
  font-size: 1.2em !important;
}

/* 数学公式容器样式 */
.math-display-block {
  margin: 1.5rem 0;
  padding: 1rem;
  background: hsl(var(--muted) / 0.2);
  border-radius: 0.5rem;
  border-left: 3px solid hsl(var(--primary));
  text-align: center;
}

.math-display-block .katex {
  font-size: 1.15em !important;
}

/* 修复KaTeX在深色模式下的显示 */
.dark .katex {
  color: hsl(var(--foreground)) !important;
}

.dark .katex-display {
  background: hsl(var(--muted) / 0.5);
  border-left-color: hsl(var(--primary));
}

.dark .math-display-block {
  background: hsl(var(--muted) / 0.3);
  border-left-color: hsl(var(--primary));
}

/* 编辑器增强样式 */
.report-editor .ql-container {
  height: calc(100% - 42px) !important;
  font-size: 16px;
  line-height: 1.8;
  border-radius: 0 0 0.5rem 0.5rem;
}

.report-editor .ql-editor {
  padding: 24px;
  min-height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
}

.report-editor .ql-toolbar {
  border-radius: 0.5rem 0.5rem 0 0;
  border-bottom: 1px solid hsl(var(--border));
  background: hsl(var(--muted) / 0.3);
}
