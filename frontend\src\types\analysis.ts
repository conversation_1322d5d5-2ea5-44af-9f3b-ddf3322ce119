export type NodeStatus = 'pending' | 'running' | 'success' | 'warning' | 'error';
export type TaskStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

export interface WorkflowNode {
  id: string;
  name: string;
  type: 'summary' | 'plan' | 'preprocess' | 'analysis' | 'modeling' | 'evaluation' | 'report' | 'data' | 'planning' | 'code' | 'visualization' | 'completion';
  status: NodeStatus;
  icon: string;
  timestamp?: string;
  description?: string;
  progress?: number;
}

// 匹配后端TaskResponse模型
export interface AnalysisTask {
  task_id: string;
  status: TaskStatus;
  created_at: string;
  updated_at: string;
  prompt: string;
  file_name?: string;
  error_message?: string;
}

// 前端扩展的任务信息
export interface ExtendedAnalysisTask extends AnalysisTask {
  name: string; // 从prompt生成的简短名称
  progress: number;
  nodes: WorkflowNode[];
}

// 匹配后端NodeUpdate模型
export interface NodeUpdate {
  node_id: string;
  node_name: string;
  status: NodeStatus;
  content: Record<string, any>;
  timestamp: string;
  execution_time?: number;
}

// WebSocket消息类型
export interface WebSocketMessage {
  event: 'node_update' | 'task_complete' | 'error' | 'progress' | 'connected' | 'ping' | 'pong' | 'step_update' | 'result_card';
  payload: Record<string, any>;
}

// 步骤更新消息
export interface StepUpdate {
  step_node_id: string;
  step_name: string;
  status: 'started' | 'generating_code' | 'code_generated' | 'code_executed' | 'generating_insights' | 'completed' | 'failed' | 'retrying';
  content: {
    attempt?: number;
    code?: string;
    execution_result?: any;
    success?: boolean;
    error?: string;
    insights?: string[];
    message: string;
  };
  timestamp: string;
}

// 匹配后端DataSummary模型
export interface DataSummary {
  shape: [number, number]; // [rows, columns]
  columns: string[];
  dtypes: Record<string, string>;
  missing_values: Record<string, number>;
  basic_stats: Record<string, any>;
  // 前端扩展字段
  sample_data?: Record<string, any>[];
}

// 匹配后端ExecutionResult模型
export interface ExecutionResult {
  success: boolean;
  stdout?: string;
  stderr?: string;
  warnings?: string; // 警告信息
  result?: any;
  execution_time: number;
  plots: string[]; // 图表文件路径列表
}

export interface CodeExecution {
  code: string;
  language: string;
  output?: string;
  error?: string;
  execution_time?: string;
  has_plot?: boolean;
}

export interface Insight {
  id: string;
  title: string;
  description: string;
  type: 'correlation' | 'trend' | 'anomaly' | 'recommendation';
  confidence: number;
  source_node?: string;
}

export interface ResultCard {
  id: string;
  node_id: string;
  type: 'summary' | 'plan' | 'code' | 'execution' | 'error' | 'insight' | 'report' | 'report_generation' | 'reflection' | 'step_progress' | 'step_execution' | 'plan_approval';
  title: string;
  content: any;
  timestamp: string;
}

// 分析计划类型
export interface AnalysisPlan {
  steps: AnalysisStep[];
  estimated_time: number;
  complexity: string;
}

export interface AnalysisStep {
  step: number;
  objective: string;
  description: string;
  expected_output: string;
}

// 计划审批相关类型
export interface PlanApprovalRequest {
  approved: boolean;
  modified_plan?: AnalysisPlan;
  library_preferences?: LibraryPreferences;
}

export interface LibraryPreferences {
  data_processing?: string[];  // 如 ['pandas', 'polars']
  visualization?: string[];    // 如 ['matplotlib', 'plotly', 'seaborn']
  machine_learning?: string[]; // 如 ['scikit-learn', 'xgboost', 'lightgbm']
  statistics?: string[];       // 如 ['scipy', 'statsmodels']
}

export interface UserInteractionResponse {
  success: boolean;
  message: string;
}

// API响应类型
export interface TaskCreateRequest {
  prompt: string;
  file?: File;
}

export interface TaskListResponse {
  tasks: AnalysisTask[];
  total: number;
  limit: number;
  offset: number;
}

export interface TaskResultsResponse {
  task_id: string;
  status: string;
  plan?: Record<string, any>;
  executed_steps: Record<string, any>[];
  insights: string[];
  data_summary?: Record<string, any>;
  errors: string[];
  created_at: string;
  updated_at: string;
}

export interface StatsResponse {
  total_tasks: number;
  status_counts: Record<string, number>;
  running_tasks: number;
  active_connections: number;
}