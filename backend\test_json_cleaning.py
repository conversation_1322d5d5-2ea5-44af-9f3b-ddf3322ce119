#!/usr/bin/env python3
"""测试JSON清理功能"""

import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.agents.reflection_agent import ReflectionAgent


def test_json_cleaning():
    """测试JSON清理功能"""
    print("🧹 测试JSON清理功能...")
    
    # 创建反思智能体实例
    agent = ReflectionAgent()
    
    # 测试用例1: 带有```json标记的响应
    test_case_1 = '''```json
{
    "overall_score": 7,
    "dimension_scores": {
        "信息准确性": {
            "score": 8,
            "issues": [
                "报告中提到'服务质量与满意度相关性最强'，但未明确说明相关系数(r=0.78)",   
                "未明确说明数据清洗后保留的记录数(4500条)与原始数据的关系"
            ]
        }
    },
    "critical_issues": [
        "完全缺失数据可视化结果的讨论和引用",
        "改进建议缺乏详细的数据支持"
    ],
    "improvement_priorities": [
        "增加可视化结果的展示和解读",
        "补充分析方法的具体说明"
    ]
}
```'''
    
    # 测试用例2: 只有```标记的响应
    test_case_2 = '''```
{
    "root_cause_analysis": {
        "问题类别1": "根因分析1",
        "问题类别2": "根因分析2"
    },
    "improvement_strategies": {
        "策略名称1": "具体实施方案1",
        "策略名称2": "具体实施方案2"
    }
}
```'''
    
    # 测试用例3: 没有代码块标记的响应
    test_case_3 = '''{
    "overall_score": 8,
    "dimension_scores": {
        "信息准确性": {
            "score": 9,
            "issues": []
        }
    }
}'''
    
    # 测试用例4: 前后有空白的响应
    test_case_4 = '''
    
```json
{
    "test": "value"
}
```

    '''
    
    test_cases = [
        ("带有```json标记", test_case_1),
        ("只有```标记", test_case_2),
        ("没有代码块标记", test_case_3),
        ("前后有空白", test_case_4)
    ]
    
    print("🔍 开始测试各种情况:")
    print("=" * 60)
    
    all_passed = True
    
    for i, (description, test_content) in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}: {description}")
        print(f"原始内容长度: {len(test_content)} 字符")
        
        try:
            # 使用清理函数
            cleaned_content = agent._clean_json_response(test_content)
            print(f"清理后长度: {len(cleaned_content)} 字符")
            
            # 尝试解析JSON
            parsed_json = json.loads(cleaned_content)
            print("✅ JSON解析成功")
            
            # 显示解析结果的关键信息
            if "overall_score" in parsed_json:
                print(f"  - 总体评分: {parsed_json.get('overall_score')}")
            if "dimension_scores" in parsed_json:
                print(f"  - 维度数量: {len(parsed_json.get('dimension_scores', {}))}")
            if "root_cause_analysis" in parsed_json:
                print(f"  - 根因分析数量: {len(parsed_json.get('root_cause_analysis', {}))}")
            if "improvement_strategies" in parsed_json:
                print(f"  - 改进策略数量: {len(parsed_json.get('improvement_strategies', {}))}")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON解析失败: {str(e)}")
            print(f"清理后内容: {cleaned_content[:100]}...")
            all_passed = False
        except Exception as e:
            print(f"❌ 其他错误: {str(e)}")
            all_passed = False
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    if all_passed:
        print("✅ 所有测试用例通过!")
        print("💡 JSON清理功能工作正常，可以处理各种格式的LLM响应")
        return True
    else:
        print("❌ 部分测试用例失败!")
        print("⚠️  需要进一步调试JSON清理功能")
        return False


def test_edge_cases():
    """测试边缘情况"""
    print("\n🔬 测试边缘情况...")
    
    agent = ReflectionAgent()
    
    edge_cases = [
        ("空字符串", ""),
        ("只有空白", "   \n\t  "),
        ("只有代码块标记", "```json\n```"),
        ("不完整的JSON", "```json\n{\"key\": \"value\"\n```"),
        ("多层嵌套的代码块", "```json\n```json\n{\"test\": \"value\"}\n```\n```"),
    ]
    
    for description, test_content in edge_cases:
        print(f"\n边缘情况: {description}")
        try:
            cleaned_content = agent._clean_json_response(test_content)
            print(f"✅ 清理成功: '{cleaned_content}'")
            
            if cleaned_content.strip():
                try:
                    json.loads(cleaned_content)
                    print("✅ JSON有效")
                except json.JSONDecodeError:
                    print("⚠️  清理后的内容不是有效JSON（这可能是预期的）")
            else:
                print("ℹ️  清理后为空内容")
                
        except Exception as e:
            print(f"❌ 清理失败: {str(e)}")


def main():
    """主测试函数"""
    print("🚀 开始JSON清理功能测试\n")
    
    # 基本功能测试
    basic_success = test_json_cleaning()
    
    # 边缘情况测试
    test_edge_cases()
    
    print(f"\n📊 最终结果:")
    print(f"  基本功能测试: {'✅ 通过' if basic_success else '❌ 失败'}")
    
    if basic_success:
        print("\n🎉 JSON清理功能修复成功!")
        print("💡 现在ReflectionAgent应该能正确解析LLM的JSON响应")
        print("🔧 建议运行完整的ReflectionAgent测试来验证修复效果")
        return 0
    else:
        print("\n⚠️  JSON清理功能仍有问题，需要进一步调试")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
