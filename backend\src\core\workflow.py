"""LangGraph工作流定义"""

import time
from typing import Dict, Any, Literal
from datetime import datetime
from langgraph.graph import StateGraph, END
from src.models.state_models import AnalysisState
from src.models.api_models import NodeUpdate, NodeStatus
from src.agents.summarizer_agent import SummarizerAgent
from src.agents.planner_agent import PlannerAgent
from src.agents.code_agent import CodeAgent
from src.agents.report_generation_agent import ReportGenerationAgent
from src.agents.reflection_agent import ReflectionAgent

from src.api.websocket import websocket_manager
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AnalysisWorkflow:
    """数据分析工作流"""

    def __init__(self):
        """初始化工作流"""
        self.summarizer = SummarizerAgent()
        self.planner = PlannerAgent()
        self.code_agent = CodeAgent()
        self.report_agent = ReportGenerationAgent()
        self.reflection_agent = ReflectionAgent()

        self.graph = self._build_graph()

    async def _send_node_update(self, task_id: str, node_id: str, node_name: str,
                               status: NodeStatus, content: Dict[str, Any] = None,
                               execution_time: float = None) -> None:
        """发送节点更新消息"""
        try:
            node_update = NodeUpdate(
                node_id=node_id,
                node_name=node_name,
                status=status,
                content=content or {},
                timestamp=datetime.now(),
                execution_time=execution_time
            )

            # 检查是否有活跃的WebSocket连接
            connection_count = websocket_manager.get_connection_count(task_id)
            if connection_count > 0:
                await websocket_manager.send_node_update(task_id, node_update)
                logger.debug(f"节点更新已发送: {node_id}, 连接数: {connection_count}")
            else:
                logger.debug(f"没有活跃的WebSocket连接，跳过节点更新: {node_id}")

        except Exception as e:
            logger.warning(f"发送WebSocket消息失败: {str(e)}")
            # 不要因为WebSocket失败而中断工作流执行
    
    def _build_graph(self) -> StateGraph:
        """构建LangGraph工作流图"""
        # 创建状态图
        workflow = StateGraph(AnalysisState)
        
        # 添加节点
        workflow.add_node("summarize", self._summarize_node)
        workflow.add_node("plan", self._plan_node)
        workflow.add_node("wait_for_approval", self._wait_for_approval_node)
        workflow.add_node("execute", self._execute_node)
        workflow.add_node("generate_report", self._generate_report_node)
        workflow.add_node("reflection", self._reflection_node)
        workflow.add_node("check_completion", self._check_completion_node)
        
        # 设置入口点
        workflow.set_entry_point("summarize")
        
        # 添加边
        workflow.add_edge("summarize", "plan")
        workflow.add_edge("plan", "wait_for_approval")
        workflow.add_conditional_edges(
            "wait_for_approval",
            self._check_user_approval,
            {
                "approved": "execute",
                "waiting": END  # 结束工作流，等待外部触发继续
            }
        )
        workflow.add_conditional_edges(
            "execute",
            self._should_continue,
            {
                "continue": "execute",
                "complete": "generate_report"
            }
        )
        workflow.add_edge("generate_report", "reflection")
        workflow.add_edge("reflection", "check_completion")
        workflow.add_edge("check_completion", END)
        
        # 编译图
        return workflow.compile()
    
    async def _summarize_node(self, state: AnalysisState) -> Dict[str, Any]:
        """数据摘要节点"""
        task_id = state['task_id']
        logger.info(f"[工作流] 执行数据摘要节点 - 任务ID: {task_id}")

        # 发送开始消息
        await self._send_node_update(task_id, "summarize", "数据摘要", NodeStatus.RUNNING)

        start_time = time.time()
        try:
            result = await self.summarizer.execute(state)
            execution_time = time.time() - start_time

            # 发送成功消息
            await self._send_node_update(
                task_id, "summarize", "数据摘要", NodeStatus.SUCCESS,
                content={"summary": result.get("data_summary", {})},
                execution_time=execution_time
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            # 发送失败消息
            await self._send_node_update(
                task_id, "summarize", "数据摘要", NodeStatus.ERROR,
                content={"error": str(e)},
                execution_time=execution_time
            )
            raise
    
    async def _plan_node(self, state: AnalysisState) -> Dict[str, Any]:
        """规划节点"""
        task_id = state['task_id']
        logger.info(f"[工作流] 执行规划节点 - 任务ID: {task_id}")

        # 发送开始消息
        await self._send_node_update(task_id, "plan", "分析规划", NodeStatus.RUNNING)

        start_time = time.time()
        try:
            result = await self.planner.execute(state)
            execution_time = time.time() - start_time

            # 发送成功消息
            plan_info = result.get("plan", {})
            steps_count = len(plan_info.get("steps", []))
            await self._send_node_update(
                task_id, "plan", "分析规划", NodeStatus.SUCCESS,
                content={"plan": plan_info, "steps_count": steps_count},
                execution_time=execution_time
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            # 发送失败消息
            await self._send_node_update(
                task_id, "plan", "分析规划", NodeStatus.ERROR,
                content={"error": str(e)},
                execution_time=execution_time
            )
            raise

    async def _wait_for_approval_node(self, state: AnalysisState) -> Dict[str, Any]:
        """等待用户审批节点"""
        task_id = state['task_id']
        logger.info(f"[工作流] 等待用户审批 - 任务ID: {task_id}")

        # 发送等待用户审批消息
        await self._send_node_update(
            task_id, "wait_for_approval", "等待用户确认", NodeStatus.RUNNING,
            content={
                "plan": state.get("plan", {}),
                "waiting_for_user": True,
                "message": "请确认分析计划或进行修改"
            }
        )

        # 发送计划审批卡片
        await self._send_plan_approval_card(task_id, state.get("plan", {}))

        # 设置等待用户状态
        return {
            "waiting_for_user": True,
            "user_approved_plan": False,
            "user_modified_plan": None,
            "library_preferences": None
        }
    
    async def _execute_node(self, state: AnalysisState) -> Dict[str, Any]:
        """执行节点"""
        task_id = state['task_id']
        current_step = state.get("current_step", 0)
        logger.info(f"[工作流] 执行代码节点 - 任务ID: {task_id}, 步骤: {current_step + 1}")

        # 发送开始消息
        node_name = f"代码执行 - 步骤 {current_step + 1}"
        await self._send_node_update(task_id, f"execute_{current_step}", node_name, NodeStatus.RUNNING)

        start_time = time.time()
        try:
            result = await self.code_agent.execute(state)
            execution_time = time.time() - start_time

            # 发送成功消息
            executed_steps = result.get("executed_steps", [])
            latest_step = executed_steps[-1] if executed_steps else {}
            await self._send_node_update(
                task_id, f"execute_{current_step}", node_name, NodeStatus.SUCCESS,
                content={"step_result": latest_step, "current_step": current_step + 1},
                execution_time=execution_time
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            # 发送失败消息
            await self._send_node_update(
                task_id, f"execute_{current_step}", node_name, NodeStatus.ERROR,
                content={"error": str(e), "current_step": current_step + 1},
                execution_time=execution_time
            )
            raise

    async def _generate_report_node(self, state: AnalysisState) -> Dict[str, Any]:
        """报告生成节点"""
        task_id = state['task_id']
        logger.info(f"[工作流] 执行报告生成节点 - 任务ID: {task_id}")

        # 发送开始消息
        await self._send_node_update(task_id, "generate_report", "生成分析报告", NodeStatus.RUNNING)

        # 立即发送报告生成卡片，让前端开始显示报告生成界面
        await self._send_report_generation_card(task_id)

        start_time = time.time()
        try:
            result = await self.report_agent.execute(state)
            execution_time = time.time() - start_time

            # 发送成功消息
            await self._send_node_update(
                task_id, "generate_report", "生成分析报告", NodeStatus.SUCCESS,
                content={"report_path": result.get("report_path"), "sections_count": 7},
                execution_time=execution_time
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            # 发送失败消息
            await self._send_node_update(
                task_id, "generate_report", "生成分析报告", NodeStatus.ERROR,
                content={"error": str(e)},
                execution_time=execution_time
            )
            raise

    async def _reflection_node(self, state: AnalysisState) -> Dict[str, Any]:
        """反思审查节点"""
        task_id = state['task_id']
        logger.info(f"[工作流] 执行反思审查节点 - 任务ID: {task_id}")

        # 发送开始消息
        await self._send_node_update(task_id, "reflection", "反思审查", NodeStatus.RUNNING)

        start_time = time.time()
        try:
            # 发送反思卡片
            await self._send_reflection_card(task_id)

            # 执行反思审查
            result = await self.reflection_agent.execute(state)
            execution_time = time.time() - start_time

            # 发送成功消息
            await self._send_node_update(
                task_id, "reflection", "反思审查", NodeStatus.SUCCESS,
                content={"message": "反思审查完成，报告已改进"},
                execution_time=execution_time
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            # 发送失败消息
            await self._send_node_update(
                task_id, "reflection", "反思审查", NodeStatus.ERROR,
                content={"error": str(e)},
                execution_time=execution_time
            )
            raise

    async def _check_completion_node(self, state: AnalysisState) -> Dict[str, Any]:
        """检查完成状态节点"""
        task_id = state['task_id']
        logger.info(f"[工作流] 检查完成状态 - 任务ID: {task_id}")

        # 发送开始消息
        await self._send_node_update(task_id, "check_completion", "完成检查", NodeStatus.RUNNING)

        start_time = time.time()
        try:
            # 这里可以添加额外的完成检查逻辑
            result = {}
            execution_time = time.time() - start_time

            # 发送成功消息
            await self._send_node_update(
                task_id, "check_completion", "完成检查", NodeStatus.SUCCESS,
                content={"status": "completed"},
                execution_time=execution_time
            )

            return result
        except Exception as e:
            execution_time = time.time() - start_time
            # 发送失败消息
            await self._send_node_update(
                task_id, "check_completion", "完成检查", NodeStatus.ERROR,
                content={"error": str(e)},
                execution_time=execution_time
            )
            raise
    
    def _check_user_approval(self, state: AnalysisState) -> Literal["approved", "waiting"]:
        """检查用户是否已审批计划"""
        waiting_for_user = state.get("waiting_for_user", True)
        user_approved = state.get("user_approved_plan", False)

        if waiting_for_user and not user_approved:
            logger.info(f"[工作流] 等待用户审批计划 - 任务ID: {state['task_id']}")
            return "waiting"
        else:
            logger.info(f"[工作流] 用户已审批计划，继续执行 - 任务ID: {state['task_id']}")
            return "approved"

    def _should_continue(self, state: AnalysisState) -> Literal["continue", "complete"]:
        """判断是否应该继续执行"""
        current_step = state.get("current_step", 0)
        plan = state.get("plan", {})

        # 如果用户修改了计划，使用修改后的计划
        if state.get("user_modified_plan"):
            plan = state["user_modified_plan"]

        total_steps = len(plan.get("steps", []))

        # 检查是否还有步骤需要执行
        if current_step < total_steps:
            logger.info(f"[工作流] 继续执行，当前步骤: {current_step}/{total_steps}")
            return "continue"
        else:
            logger.info(f"[工作流] 所有步骤执行完成，准备生成报告: {current_step}/{total_steps}")
            return "complete"
    

    
    async def run(self, initial_state: AnalysisState) -> AnalysisState:
        """运行工作流"""
        logger.info(f"[工作流] 开始执行分析工作流 - 任务ID: {initial_state['task_id']}")
        
        try:
            # 执行工作流
            final_state = await self.graph.ainvoke(initial_state)
            
            logger.info(f"[工作流] 工作流执行完成 - 任务ID: {initial_state['task_id']}")
            return final_state
            
        except Exception as e:
            logger.error(f"[工作流] 工作流执行失败 - 任务ID: {initial_state['task_id']}, 错误: {str(e)}")
            
            # 返回包含错误信息的状态
            error_state = initial_state.copy()
            error_state["errors"] = initial_state.get("errors", []) + [f"工作流执行失败: {str(e)}"]
            return error_state

    async def run_from_node(self, state: AnalysisState, start_node: str) -> AnalysisState:
        """从指定节点开始执行工作流"""
        try:
            logger.info(f"[工作流] 从节点 {start_node} 开始执行 - 任务ID: {state['task_id']}")

            # 创建一个新的工作流，只包含从指定节点开始的部分
            continue_workflow = StateGraph(AnalysisState)

            # 添加节点
            continue_workflow.add_node("execute", self._execute_node)
            continue_workflow.add_node("generate_report", self._generate_report_node)
            continue_workflow.add_node("reflection", self._reflection_node)
            continue_workflow.add_node("check_completion", self._check_completion_node)

            # 设置入口点
            continue_workflow.set_entry_point(start_node)

            # 添加边
            continue_workflow.add_conditional_edges(
                "execute",
                self._should_continue,
                {
                    "continue": "execute",
                    "complete": "generate_report"
                }
            )
            continue_workflow.add_edge("generate_report", "reflection")
            continue_workflow.add_edge("reflection", "check_completion")
            continue_workflow.add_edge("check_completion", END)

            # 编译并执行
            app = continue_workflow.compile()
            final_state = await app.ainvoke(state)

            logger.info(f"[工作流] 从节点 {start_node} 执行完成 - 任务ID: {state['task_id']}")
            return final_state

        except Exception as e:
            logger.error(f"[工作流] 从节点 {start_node} 执行失败 - 任务ID: {state['task_id']}, 错误: {str(e)}")
            error_state = state.copy()
            error_state["errors"] = state.get("errors", []) + [f"工作流从节点 {start_node} 执行失败: {str(e)}"]
            return error_state

    async def _send_plan_approval_card(self, task_id: str, plan: Dict[str, Any]):
        """发送计划审批卡片"""
        try:
            card_data = {
                "id": f"plan_approval_{task_id}",  # 使用固定ID，确保后续更新能正确匹配
                "node_id": "wait_for_approval",
                "type": "plan_approval",
                "title": "分析计划确认",
                "content": {
                    "plan": plan,
                    "waiting_for_user": True,
                    "message": "请确认分析计划，您可以直接执行或进行修改后再执行。"
                },
                "timestamp": datetime.now().isoformat()
            }

            await websocket_manager.send_result_card(task_id, card_data)
            logger.info(f"[工作流] 发送计划审批卡片 - 任务ID: {task_id}")

        except Exception as e:
            logger.error(f"发送计划审批卡片失败: {str(e)}")
            # 不抛出异常，避免中断工作流

    async def _send_report_generation_card(self, task_id: str):
        """发送报告生成卡片"""
        try:
            card_data = {
                "id": f"report_generation_{task_id}",
                "node_id": "generate_report",
                "type": "report_generation",
                "title": "数据分析报告生成",
                "content": {
                    "task_id": task_id,
                    "message": "正在生成专业的数据分析报告，请稍候..."
                },
                "timestamp": datetime.now().isoformat()
            }

            await websocket_manager.send_result_card(task_id, card_data)
            logger.info(f"[工作流] 发送报告生成卡片 - 任务ID: {task_id}")

        except Exception as e:
            logger.error(f"发送报告生成卡片失败: {str(e)}")
            # 不抛出异常，避免中断工作流

    async def _send_reflection_card(self, task_id: str):
        """发送反思审查卡片"""
        try:
            card_data = {
                "id": f"reflection_{task_id}",
                "node_id": "reflection",
                "type": "reflection",
                "title": "报告反思审查",
                "content": {
                    "task_id": task_id,
                    "message": "正在使用Reflexion框架审查报告质量并生成改进版本..."
                },
                "timestamp": datetime.now().isoformat()
            }

            await websocket_manager.send_result_card(task_id, card_data)
            logger.info(f"[工作流] 发送反思审查卡片 - 任务ID: {task_id}")

        except Exception as e:
            logger.error(f"发送反思审查卡片失败: {str(e)}")
            # 不抛出异常，避免中断工作流


# 创建全局工作流实例
analysis_workflow = AnalysisWorkflow()
