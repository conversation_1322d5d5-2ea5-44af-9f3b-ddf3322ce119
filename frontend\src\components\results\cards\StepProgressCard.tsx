import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import { Clock, Code, Play, CheckCircle, XCircle, RotateCcw, Lightbulb } from 'lucide-react';
import { ResultCard } from '@/types/analysis';

interface StepProgressCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

export function StepProgressCard({ card, isHighlighted }: StepProgressCardProps) {
  const content = card.content as {
    status?: string;
    attempt?: number;
    code?: string;
    execution_result?: any;
    success?: boolean;
    error?: string;
    insights?: string[];
    message: string;
    step_name?: string;
  };

  // 从content中获取状态，如果没有则从卡片标题中解析
  const getStatus = () => {
    if (content.status) {
      return content.status;
    }
    // 从卡片标题中提取状态
    const titleParts = card.title.split(' - ');
    if (titleParts.length > 1) {
      const statusText = titleParts[1].toLowerCase();
      if (statusText.includes('开始')) return 'started';
      if (statusText.includes('生成代码')) return 'generating_code';
      if (statusText.includes('生成完成')) return 'code_generated';
      if (statusText.includes('执行结果')) return 'code_executed';
      if (statusText.includes('生成洞察')) return 'generating_insights';
      if (statusText.includes('完成')) return 'completed';
      if (statusText.includes('失败')) return 'failed';
      if (statusText.includes('重试')) return 'retrying';
    }
    return 'unknown';
  };

  const status = getStatus();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'started':
        return <Play className="w-4 h-4 text-blue-500" />;
      case 'generating_code':
        return <Code className="w-4 h-4 text-yellow-500" />;
      case 'code_generated':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'code_executed':
        return content.success ?
          <CheckCircle className="w-4 h-4 text-green-500" /> :
          <XCircle className="w-4 h-4 text-red-500" />;
      case 'generating_insights':
        return <Lightbulb className="w-4 h-4 text-purple-500" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'retrying':
        return <RotateCcw className="w-4 h-4 text-orange-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'retrying':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'generating_code':
      case 'generating_insights':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'code_generated':
      case 'code_executed':
        return content.success ? 
          'bg-green-100 text-green-800 border-green-200' : 
          'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-blue-100 text-blue-800 border-blue-200';
    }
  };

  const getProgressValue = (status: string) => {
    switch (status) {
      case 'started': return 10;
      case 'generating_code': return 25;
      case 'code_generated': return 40;
      case 'code_executed': return content.success ? 70 : 30;
      case 'generating_insights': return 85;
      case 'completed': return 100;
      case 'failed': return 0;
      case 'retrying': return 20;
      default: return 0;
    }
  };

  return (
    <Card className={cn(
      "result-card animate-fade-in-up transition-all duration-200",
      isHighlighted && "ring-2 ring-primary",
      "border-l-4",
      status === 'completed' && "border-l-green-500",
      status === 'failed' && "border-l-red-500",
      status.includes('generating') && "border-l-yellow-500",
      !['completed', 'failed'].includes(status) && !status.includes('generating') && "border-l-blue-500"
    )}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            {getStatusIcon(status)}
            <span className="font-medium">{card.title}</span>
          </div>
          <div className="flex items-center gap-2">
            {content.attempt && (
              <Badge variant="outline" className="text-xs">
                尝试 {content.attempt}
              </Badge>
            )}
            <Badge className={cn("text-xs", getStatusColor(status))}>
              {card.timestamp}
            </Badge>
          </div>
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* 进度条 */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>执行进度</span>
            <span>{getProgressValue(status)}%</span>
          </div>
          <Progress 
            value={getProgressValue(status)} 
            className="h-2"
          />
        </div>

        {/* 消息内容 */}
        <div className="text-sm text-muted-foreground">
          {content.message}
        </div>

        {/* 错误信息 */}
        {content.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <div className="text-sm text-red-800">
              <strong>错误:</strong> {content.error}
            </div>
          </div>
        )}

        {/* 成功信息 */}
        {content.success === true && (
          <div className="p-3 bg-green-50 border border-green-200 rounded-md">
            <div className="text-sm text-green-800">
              代码执行成功
            </div>
          </div>
        )}

        {/* 洞察预览 */}
        {content.insights && content.insights.length > 0 && (
          <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
            <div className="text-sm text-purple-800">
              <strong>生成洞察:</strong> {content.insights.length} 个分析要点
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
