"""数据分析智能体"""

from typing import Dict, Any
from langchain_core.messages import HumanMessage, SystemMessage
from src.agents.base_agent import BaseAgent
from src.models.state_models import AnalysisState
from src.execution.jupyter_executor import jupyter_executor


class AnalysisAgent(BaseAgent):
    """数据分析智能体"""
    
    def __init__(self):
        super().__init__(
            name="数据分析智能体",
            description="执行探索性数据分析、统计分析、相关性分析等任务"
        )
    
    async def execute(self, state: AnalysisState) -> Dict[str, Any]:
        """执行数据分析"""
        self._log_execution("开始数据分析")
        
        try:
            # 生成分析代码
            analysis_code = await self._generate_analysis_code(state)
            
            # 执行分析代码
            execution_result = await jupyter_executor.execute_code(
                state["task_id"], 
                analysis_code
            )
            
            if execution_result.success:
                # 生成分析洞察
                insights = await self._generate_analysis_insights(
                    state, 
                    analysis_code, 
                    execution_result
                )
                
                updated_state = {
                    "insights": state.get("insights", []) + insights
                }
                
                self._log_execution("数据分析完成")
                return updated_state
            else:
                error_msg = self._format_error_message(execution_result.stderr or "分析执行失败")
                return {
                    "errors": state.get("errors", []) + [error_msg]
                }
                
        except Exception as e:
            error_msg = self._format_error_message(str(e))
            self._log_execution("数据分析失败", str(e))
            return {
                "errors": state.get("errors", []) + [error_msg]
            }
    
    async def _generate_analysis_code(self, state: AnalysisState) -> str:
        """生成数据分析代码"""
        system_prompt = self._create_system_prompt(
            "数据分析专家",
            """
你需要生成Python代码来进行数据分析。

分析任务可能包括：
1. 描述性统计分析
2. 数据分布分析
3. 相关性分析
4. 分组统计分析
5. 趋势分析
6. 异常值分析
7. 数据可视化（直方图、散点图、箱线图、热力图等）

请生成完整的Python代码，包含：
- 必要的导入语句（pandas, numpy, matplotlib, seaborn, plotly等）
- 数据加载和基本信息查看
- 多种分析方法
- 丰富的可视化图表
- 结果解释和输出
- 适当的注释说明

只返回Python代码，不要包含其他解释。
            """
        )
        
        # 构建上下文
        context_parts = [
            f"数据文件路径: {state['dataframe_path']}",
            f"分析需求: {state['original_query']}"
        ]
        
        # 添加数据摘要信息
        if state.get("data_summary"):
            summary = state["data_summary"]
            context_parts.extend([
                f"数据形状: {summary.get('shape', '未知')}",
                f"数值列: {list(summary.get('numeric_stats', {}).keys())}",
                f"分类列: {list(summary.get('categorical_stats', {}).keys())}"
            ])
        
        # 添加之前的洞察
        insights = state.get("insights", [])
        if insights:
            context_parts.append(f"之前的发现: {'; '.join(insights[-3:])}")
        
        context = "\n".join(context_parts)
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"请为以下数据生成分析代码：\n\n{context}")
        ]
        
        response = await self.llm.ainvoke(messages)
        return response.content.strip()
    
    async def _generate_analysis_insights(
        self, 
        state: AnalysisState, 
        code: str, 
        execution_result
    ) -> list:
        """生成分析洞察"""
        system_prompt = self._create_system_prompt(
            "数据分析洞察专家",
            """
基于执行的分析代码和结果，生成有价值的数据洞察。

洞察应该包括：
1. 数据的关键特征和模式
2. 重要的统计发现
3. 变量之间的关系
4. 异常或有趣的观察
5. 业务含义和建议

请提供3-5个具体、有价值的洞察点。
            """
        )
        
        result_summary = f"""
分析代码：
{code[:500]}...

执行结果：
- 成功: {execution_result.success}
- 输出: {execution_result.stdout[:1000] if execution_result.stdout else '无输出'}...
- 生成图表: {len(execution_result.plots)}个
        """
        
        messages = [
            SystemMessage(content=system_prompt),
            HumanMessage(content=f"请基于以下分析结果生成洞察：\n\n{result_summary}")
        ]
        
        response = await self.llm.ainvoke(messages)
        
        # 将洞察分割成列表
        insights_text = response.content.strip()
        insights = [insight.strip() for insight in insights_text.split('\n') if insight.strip()]
        
        return insights[:5]  # 最多返回5个洞察
