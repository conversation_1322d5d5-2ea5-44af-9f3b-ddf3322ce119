import { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Header } from '@/components/layout/Header';
import { WorkflowPanel } from '@/components/workflow/WorkflowPanel';
import { ResultsPanel } from '@/components/results/ResultsPanel';
import { WorkflowNode, ResultCard, NodeUpdate } from '@/types/analysis';
import { useTask, useTaskResults, useTaskInsights } from '@/hooks/use-api';
import { useTaskWebSocket, useStepUpdates } from '@/hooks/use-websocket';
import { toast } from 'sonner';
import { Loader2, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';

export function AnalysisPage() {
  const { taskId } = useParams<{ taskId: string }>();
  const navigate = useNavigate();
  const [selectedNodeId, setSelectedNodeId] = useState<string>('');
  const [isWorkflowCollapsed, setIsWorkflowCollapsed] = useState(false);
  const [nodes, setNodes] = useState<WorkflowNode[]>([]);
  const [cards, setCards] = useState<ResultCard[]>([]);
  const hasSetInitialSelection = useRef(false);
  const stepExecutionCards = useRef<Map<string, ResultCard>>(new Map()); // 持久化步骤执行卡片

  // API hooks
  const {
    data: task,
    isLoading: taskLoading,
    error: taskError
  } = useTask(taskId || '', !!taskId);

  const {
    data: taskResults,
    isLoading: resultsLoading
  } = useTaskResults(taskId || '', !!taskId);

  const {
    data: taskInsights
  } = useTaskInsights(taskId || '', !!taskId);

  // 辅助函数：根据节点名称推断类型
  const getNodeTypeFromName = (nodeName: string): WorkflowNode['type'] => {
    const name = nodeName.toLowerCase();
    if (name.includes('数据摘要') || name.includes('data summary')) return 'data';
    if (name.includes('规划') || name.includes('plan')) return 'planning';
    if (name.includes('代码') || name.includes('code')) return 'code';
    if (name.includes('报告') || name.includes('report')) return 'report';
    if (name.includes('分析') || name.includes('analysis')) return 'analysis';
    if (name.includes('可视化') || name.includes('visualization')) return 'visualization';
    if (name.includes('完成检查') || name.includes('completion')) return 'completion';
    return 'analysis';
  };

  // 辅助函数：根据类型获取图标
  const getIconFromType = (type: WorkflowNode['type']): string => {
    switch (type) {
      case 'summary': return 'database';
      case 'plan': return 'brain';
      case 'preprocess': return 'cog';
      case 'analysis': return 'bar-chart-3';
      case 'modeling': return 'code';
      case 'evaluation': return 'check-circle';
      case 'report': return 'file-text';
      case 'data': return 'database';
      case 'planning': return 'target';
      case 'code': return 'code';
      case 'visualization': return 'line-chart';
      case 'completion': return 'goal';
      default: return 'cog';
    }
  };

  // WebSocket连接
  const { isConnected, connectionError } = useTaskWebSocket(taskId || null, {
    onNodeUpdate: (update: NodeUpdate) => {
      console.log('节点更新:', update);
      setNodes(prevNodes => {
        const existingNodeIndex = prevNodes.findIndex(node => node.id === update.node_id);

        if (existingNodeIndex >= 0) {
          const updatedNodes = [...prevNodes];
          updatedNodes[existingNodeIndex] = {
            ...updatedNodes[existingNodeIndex],
            status: update.status,
            timestamp: new Date(update.timestamp).toLocaleTimeString(),
            description: update.execution_time 
              ? `执行时间: ${update.execution_time.toFixed(2)}s`
              : updatedNodes[existingNodeIndex].description
          };
          return updatedNodes;
        } else {
          const newNode: WorkflowNode = {
            id: update.node_id,
            name: update.node_name,
            type: getNodeTypeFromName(update.node_name),
            status: update.status,
            icon: getIconFromType(getNodeTypeFromName(update.node_name)),
            timestamp: new Date(update.timestamp).toLocaleTimeString(),
            description: update.execution_time 
              ? `执行时间: ${update.execution_time.toFixed(2)}s`
              : update.node_name
          };
          return [...prevNodes, newNode];
        }
      });

      if (update.content) {
        generateResultCard(update);
      }
    },
    onTaskComplete: (results) => {
      console.log('任务完成:', results);
      toast.success('分析任务已完成！');
    },
    onError: (error) => {
      console.error('任务错误:', error);
      toast.error('任务执行出错', { description: error });
    },
    onProgress: (progress) => {
      console.log('进度更新:', progress);
    },
    onResultCard: (card) => {
      console.log('收到结果卡片:', card);

      // 添加结果卡片到状态
      setCards(prevCards => {
        // 检查是否已存在相同的卡片
        const existingCardIndex = prevCards.findIndex(c => c.id === card.id);

        if (existingCardIndex >= 0) {
          // 更新现有卡片
          const updatedCards = [...prevCards];
          updatedCards[existingCardIndex] = card;
          return updatedCards;
        } else {
          // 添加新卡片
          return [...prevCards, card];
        }
      });

      // 如果是计划审批卡片或报告生成卡片，自动选择对应的节点
      if (card.type === 'plan_approval' || card.type === 'report_generation') {
        setSelectedNodeId(card.node_id);
      }
    }
  });

  // 步骤更新监听
  useStepUpdates(taskId || null, (stepUpdate) => {
    console.log('[DEBUG] AnalysisPage 收到步骤更新:', stepUpdate);
    console.log('[DEBUG] 当前selectedNodeId:', selectedNodeId);
    console.log('[DEBUG] stepUpdate.step_node_id:', stepUpdate.step_node_id);

    const stepCardId = `step-execution-${stepUpdate.step_node_id}`;

    // 将 step-X 格式转换为 execute_X 格式以匹配工作流节点ID
    // step-1 -> execute_0, step-2 -> execute_1, 等等
    const stepNumber = parseInt(stepUpdate.step_node_id.replace('step-', ''));
    const workflowNodeId = `execute_${stepNumber - 1}`;
    console.log('[DEBUG] 转换后的工作流节点ID:', workflowNodeId);

    // 首先从持久化存储获取现有卡片
    const existingCard = stepExecutionCards.current.get(stepCardId);

    let updatedCard: ResultCard;
    if (existingCard) {
      // 更新现有卡片 - 保留之前的内容，只更新新提供的字段
      const existingContent = existingCard.content;
      updatedCard = {
        ...existingCard,
        timestamp: new Date(stepUpdate.timestamp).toLocaleTimeString(),
        content: {
          status: stepUpdate.status,
          step_name: stepUpdate.step_name,
          attempt: stepUpdate.content.attempt || existingContent.attempt,
          message: stepUpdate.content.message || existingContent.message,
          // 保留之前的内容，只有新内容存在时才更新
          code: stepUpdate.content.code || existingContent.code,
          execution_result: stepUpdate.content.execution_result || existingContent.execution_result,
          error: stepUpdate.content.error || existingContent.error,
          insights: stepUpdate.content.insights || existingContent.insights
        }
      };
      console.log('[DEBUG] 更新了现有步骤执行卡片:', stepCardId);
    } else {
      // 创建新的步骤执行卡片，使用转换后的工作流节点ID
      updatedCard = {
        id: stepCardId,
        node_id: workflowNodeId, // 使用转换后的ID
        type: 'step_execution' as const,
        title: stepUpdate.step_name,
        timestamp: new Date(stepUpdate.timestamp).toLocaleTimeString(),
        content: {
          status: stepUpdate.status,
          step_name: stepUpdate.step_name,
          attempt: stepUpdate.content.attempt,
          message: stepUpdate.content.message,
          code: stepUpdate.content.code,
          execution_result: stepUpdate.content.execution_result,
          error: stepUpdate.content.error,
          insights: stepUpdate.content.insights
        }
      };
      console.log('[DEBUG] 创建了新步骤执行卡片:', stepCardId, '工作流节点ID:', workflowNodeId);
    }

    // 保存到持久化存储
    stepExecutionCards.current.set(stepCardId, updatedCard);

    console.log('[DEBUG] 更新后的卡片内容:', {
      status: updatedCard.content.status,
      hasCode: !!updatedCard.content.code,
      hasExecutionResult: !!updatedCard.content.execution_result,
      hasInsights: !!updatedCard.content.insights
    });

    // 更新组件状态
    setCards(prevCards => {
      const newCards = [...prevCards];
      const existingCardIndex = newCards.findIndex(card => card.id === stepCardId);

      if (existingCardIndex >= 0) {
        newCards[existingCardIndex] = updatedCard;
      } else {
        newCards.push(updatedCard);
      }

      console.log('[DEBUG] 更新后的卡片数量:', newCards.length);
      console.log('[DEBUG] 所有卡片的node_id:', newCards.map(card => ({ id: card.id, node_id: card.node_id, type: card.type })));
      return newCards;
    });

    // 自动选中当前执行的步骤节点以便实时显示结果
    if (stepUpdate.status === 'started' ||
        stepUpdate.status === 'generating_code' ||
        stepUpdate.status === 'code_generated' ||
        stepUpdate.status === 'code_executed' ||
        stepUpdate.status === 'generating_insights') {
      console.log('[DEBUG] 步骤执行中，自动选中工作流节点:', workflowNodeId);
      setSelectedNodeId(workflowNodeId); // 使用转换后的工作流节点ID
    }
    // 如果当前没有选中节点，也自动选中当前步骤节点
    else if (!selectedNodeId) {
      console.log('[DEBUG] 没有选中节点，自动选中工作流节点:', workflowNodeId);
      setSelectedNodeId(workflowNodeId); // 使用转换后的工作流节点ID
    }
  });



  const generateResultCard = (update: NodeUpdate) => {
    const cardId = `card-${update.node_id}-${Date.now()}`;

    // 处理数据摘要
    if (update.content.summary) {
      const summaryCard: ResultCard = {
        id: cardId,
        node_id: update.node_id,
        type: 'summary',
        title: `${update.node_name} - 数据摘要`,
        timestamp: new Date(update.timestamp).toLocaleTimeString(),
        content: {
          summary: update.content.summary,
          execution_time: update.execution_time
        }
      };
      setCards(prev => [...prev, summaryCard]);
    }

    // 处理分析计划
    if (update.content.plan) {
      const planCard: ResultCard = {
        id: cardId,
        node_id: update.node_id,
        type: 'plan',
        title: `${update.node_name} - 分析计划`,
        timestamp: new Date(update.timestamp).toLocaleTimeString(),
        content: {
          plan: update.content.plan,
          execution_time: update.execution_time
        }
      };
      setCards(prev => [...prev, planCard]);
    }

    // 处理代码
    if (update.content.code) {
      const codeCard: ResultCard = {
        id: cardId,
        node_id: update.node_id,
        type: 'code',
        title: `${update.node_name} - 代码`,
        timestamp: new Date(update.timestamp).toLocaleTimeString(),
        content: {
          code: update.content.code,
          language: 'python',
          execution_time: update.execution_time
        }
      };
      setCards(prev => [...prev, codeCard]);
    }

    // 处理执行结果
    if (update.content.execution_result) {
      const executionCard: ResultCard = {
        id: `${cardId}-exec`,
        node_id: update.node_id,
        type: 'execution',
        title: `${update.node_name} - 执行结果`,
        timestamp: new Date(update.timestamp).toLocaleTimeString(),
        content: update.content.execution_result
      };
      setCards(prev => [...prev, executionCard]);
    }

    // 处理分析洞察
    if (update.content.insights) {
      const insightCard: ResultCard = {
        id: `${cardId}-insight`,
        node_id: update.node_id,
        type: 'insight',
        title: `${update.node_name} - 分析洞察`,
        timestamp: new Date(update.timestamp).toLocaleTimeString(),
        content: { insights: update.content.insights }
      };
      setCards(prev => [...prev, insightCard]);
    }

    // 处理错误信息
    if (update.content.error) {
      const errorCard: ResultCard = {
        id: `${cardId}-error`,
        node_id: update.node_id,
        type: 'error',
        title: `${update.node_name} - 错误`,
        timestamp: new Date(update.timestamp).toLocaleTimeString(),
        content: { error: update.content.error }
      };
      setCards(prev => [...prev, errorCard]);
    }
  };

  const getNodeTypeFromAgent = (agent: string): WorkflowNode['type'] => {
    if (agent.includes('Summarizer')) return 'summary';
    if (agent.includes('Planner')) return 'plan';
    if (agent.includes('Preprocess')) return 'preprocess';
    if (agent.includes('Analysis')) return 'analysis';
    if (agent.includes('Modeling')) return 'modeling';
    if (agent.includes('Evaluation')) return 'evaluation';
    if (agent.includes('Report')) return 'report';
    return 'analysis';
  };

  const getNodeStatusFromStep = (step: any, executedSteps: any[]): WorkflowNode['status'] => {
    const executed = executedSteps.find(es => es.step === step.step);
    if (executed) {
      return executed.success ? 'success' : 'error';
    }
    return step.status === 'completed' ? 'success' :
           step.status === 'running' ? 'running' : 'pending';
  };

  const getTimestampFromStep = (step: any, executedSteps: any[]): string | undefined => {
    const executed = executedSteps.find(es => es.step === step.step);
    return executed ? new Date(executed.timestamp).toLocaleTimeString() : undefined;
  };

  useEffect(() => {
    if (taskResults) {
      const initialNodes: WorkflowNode[] = [];

      // 添加数据摘要节点
      if (taskResults.data_summary) {
        initialNodes.push({
          id: 'summarize',
          name: '数据摘要',
          type: 'summary',
          status: 'success',
          icon: 'database',
          description: '数据预处理和摘要分析',
          timestamp: new Date().toLocaleTimeString()
        });
      }

      // 添加分析规划节点
      if (taskResults.plan) {
        initialNodes.push({
          id: 'plan',
          name: '分析规划',
          type: 'planning',
          status: 'success',
          icon: 'target',
          description: '制定详细的分析计划',
          timestamp: new Date().toLocaleTimeString()
        });
      }

      // 添加执行步骤节点
      if (taskResults.plan?.steps) {
        const stepNodes: WorkflowNode[] = taskResults.plan.steps.map((step: any, index: number) => ({
          id: `execute_${index}`, // 使用与后端工作流一致的格式
          name: step.objective,
          type: getNodeTypeFromAgent(step.agent),
          status: getNodeStatusFromStep(step, taskResults.executed_steps || []),
          icon: getIconFromType(getNodeTypeFromAgent(step.agent)),
          description: step.description || step.objective,
          timestamp: getTimestampFromStep(step, taskResults.executed_steps || [])
        }));
        initialNodes.push(...stepNodes);
      }

      // 添加报告生成节点（如果所有步骤都已完成）
      if (taskResults.plan?.steps && taskResults.executed_steps) {
        const allStepsCompleted = taskResults.plan.steps.length === taskResults.executed_steps.length;
        if (allStepsCompleted) {
          initialNodes.push({
            id: 'generate_report',
            name: '生成分析报告',
            type: 'report',
            status: taskResults.final_report ? 'success' : 'pending',
            icon: 'file-text',
            description: '生成专业的数据分析报告',
            timestamp: new Date().toLocaleTimeString()
          });
        }
      }

      // 只有在节点为空时才设置初始节点，避免刷新后覆盖实时更新的节点
      setNodes(prevNodes => prevNodes.length === 0 ? initialNodes : prevNodes);
    }
  }, [taskResults]);

  // 单独处理初始节点选择，避免循环依赖
  useEffect(() => {
    if (nodes.length > 0 && !selectedNodeId && !hasSetInitialSelection.current) {
      // 优先选择正在运行的节点，否则选择第一个节点
      const runningNode = nodes.find(node => node.status === 'running');
      const nodeToSelect = runningNode || nodes[0];
      console.log('[DEBUG] 初始节点选择:', nodeToSelect.id, '状态:', nodeToSelect.status);
      setSelectedNodeId(nodeToSelect.id);
      hasSetInitialSelection.current = true;
    }
  }, [nodes, selectedNodeId]); // 包含必要的依赖

  // 使用 useRef 来跟踪是否已经初始化过卡片
  const hasInitializedCards = useRef(false);

  useEffect(() => {
    if (taskResults) {
      console.log('[DEBUG] taskResults useEffect 触发');
      console.log('[DEBUG] taskResults 内容:', {
        hasDataSummary: !!taskResults.data_summary,
        hasPlan: !!taskResults.plan,
        hasExecutedSteps: !!taskResults.executed_steps,
        executedStepsCount: taskResults.executed_steps?.length || 0
      });
      console.log('[DEBUG] 当前卡片数量:', cards.length);
      console.log('[DEBUG] 已初始化卡片:', hasInitializedCards.current);

      setCards(prevCards => {
        console.log('[DEBUG] setCards 回调执行，prevCards 数量:', prevCards.length);
        const newCards = [...prevCards];

        // 添加数据摘要卡片（如果不存在且没有来自WebSocket的卡片）
        if (taskResults.data_summary) {
          const summaryExists = newCards.some(card =>
            (card.id === 'data-summary-card' || card.node_id === 'summarize') && card.type === 'summary'
          );
          if (!summaryExists) {
            const summaryCard: ResultCard = {
              id: 'data-summary-card',
              node_id: 'summarize',
              type: 'summary',
              title: '数据摘要',
              timestamp: new Date().toLocaleTimeString(),
              content: taskResults.data_summary
            };
            newCards.push(summaryCard);
            console.log('[DEBUG] 添加数据摘要卡片');
          } else {
            console.log('[DEBUG] 数据摘要卡片已存在');
          }
        } else {
          console.log('[DEBUG] taskResults 中没有 data_summary');
        }

        // 添加分析规划卡片（如果不存在且没有来自WebSocket的卡片）
        if (taskResults.plan) {
          const planExists = newCards.some(card =>
            (card.id === 'analysis-plan-card' || card.node_id === 'plan') && card.type === 'plan'
          );
          if (!planExists) {
            const planCard: ResultCard = {
              id: 'analysis-plan-card',
              node_id: 'plan',
              type: 'plan',
              title: '分析规划',
              timestamp: new Date().toLocaleTimeString(),
              content: taskResults.plan
            };
            newCards.push(planCard);
            console.log('[DEBUG] 添加分析规划卡片');
          } else {
            console.log('[DEBUG] 分析规划卡片已存在');
          }
        } else {
          console.log('[DEBUG] taskResults 中没有 plan');
        }

        // 对于执行步骤，从 taskResults 创建统一的步骤执行卡片（如果还没有实时卡片的话）
        if (taskResults.executed_steps) {
          taskResults.executed_steps.forEach((step: any, index: number) => {
            const stepNodeId = `step-${index + 1}`;
            const workflowNodeId = `execute_${index}`; // 与工作流节点ID一致（从0开始）
            const stepCardId = `step-execution-${stepNodeId}`;

            // 检查是否已有该节点的实时步骤执行卡片
            const hasStepExecutionCard = newCards.some(card => card.id === stepCardId);

            if (!hasStepExecutionCard) {
              // 创建统一的步骤执行卡片，包含所有信息
              const stepExecutionCard: ResultCard = {
                id: stepCardId,
                node_id: workflowNodeId, // 使用工作流节点ID（从0开始）
                type: 'step_execution' as const,
                title: step.step_name || step.objective,
                timestamp: new Date(step.timestamp).toLocaleTimeString(),
                content: {
                  status: 'completed' as const,
                  step_name: step.step_name || step.objective,
                  message: '步骤执行完成',
                  code: step.code,
                  execution_result: step.result,
                  insights: step.insights
                }
              };

              newCards.push(stepExecutionCard);
              // 同时保存到持久化存储中
              stepExecutionCards.current.set(stepCardId, stepExecutionCard);
              console.log(`[DEBUG] 从 taskResults 创建步骤执行卡片: ${stepCardId}, 工作流节点ID: ${workflowNodeId}`);
            } else {
              console.log(`[DEBUG] 节点 ${stepNodeId} 已有实时步骤执行卡片，跳过 taskResults 创建`);
            }
          });
        }

        // 恢复持久化的步骤执行卡片
        stepExecutionCards.current.forEach((stepCard, cardId) => {
          const existsInNewCards = newCards.some(card => card.id === cardId);
          if (!existsInNewCards) {
            newCards.push(stepCard);
            console.log('[DEBUG] 恢复持久化的步骤执行卡片:', cardId);
          }
        });

        console.log('[DEBUG] taskResults useEffect 完成，最终卡片数量:', newCards.length);
        console.log('[DEBUG] 卡片类型分布:', newCards.reduce((acc, card) => {
          acc[card.type] = (acc[card.type] || 0) + 1;
          return acc;
        }, {} as Record<string, number>));

        // 标记已初始化
        hasInitializedCards.current = true;

        return newCards;
      });
    }
  }, [taskResults]);

  // 清理函数：在任务ID变化时清理持久化的步骤执行卡片
  useEffect(() => {
    const stepCardsRef = stepExecutionCards.current;
    return () => {
      if (taskId) {
        console.log('[DEBUG] 清理任务的持久化步骤执行卡片:', taskId);
        stepCardsRef.clear();
      }
    };
  }, [taskId]);

  const handleExportReport = () => {
    console.log('Exporting report...');
  };

  const canExport = task?.status === 'completed';

  if (!taskId) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            缺少任务ID。请从任务列表中选择一个任务。
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (taskError) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            加载任务失败: {taskError.message}
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => navigate('/')}
            >
              返回首页
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  if (taskLoading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>加载任务数据...</span>
        </div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <Alert className="max-w-md">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            未找到任务数据。
            <Button
              variant="outline"
              size="sm"
              className="mt-2"
              onClick={() => navigate('/')}
            >
              返回首页
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const projectName = task.prompt.length > 50
    ? task.prompt.substring(0, 50) + '...'
    : task.prompt;

  return (
    <div className="h-screen bg-background flex flex-col overflow-hidden">
      <Header
        projectName={projectName}
        onProjectNameChange={(name) => console.log('Update project name:', name)}
        onExportReport={handleExportReport}
        canExport={canExport}
        fileName={task.file_name}
        fileSize={undefined} // 暂时没有文件大小信息，可以后续从API获取
        taskId={task.task_id}
      />

      <div className="flex flex-1 overflow-hidden">
        <WorkflowPanel
          nodes={nodes}
          selectedNodeId={selectedNodeId}
          onNodeSelect={(nodeId) => {
            console.log('[DEBUG] 用户选择节点:', nodeId);
            console.log('[DEBUG] 当前卡片数量:', cards.length);
            console.log('[DEBUG] 所有卡片详情:', cards.map(card => ({
              id: card.id,
              node_id: card.node_id,
              type: card.type,
              title: card.title
            })));
            console.log('[DEBUG] 该节点的卡片:', cards.filter(card => card.node_id === nodeId));
            console.log('[DEBUG] 持久化的步骤执行卡片:', Array.from(stepExecutionCards.current.entries()));
            setSelectedNodeId(nodeId);
          }}
          isCollapsed={isWorkflowCollapsed}
          onToggleCollapse={() => setIsWorkflowCollapsed(!isWorkflowCollapsed)}
        />

        <div className="flex-1">
          <ResultsPanel
            cards={cards}
            selectedNodeId={selectedNodeId}
            currentTaskId={taskId}
          />
        </div>
      </div>

      {!isConnected && task?.status === 'running' && (
        <div className="fixed bottom-4 right-4 bg-yellow-500 text-white px-4 py-2 rounded-lg shadow-lg">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            <span className="text-sm">正在连接实时更新...</span>
          </div>
        </div>
      )}

      {connectionError && (
        <div className="fixed bottom-4 right-4 bg-red-500 text-white px-4 py-2 rounded-lg shadow-lg">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4" />
            <span className="text-sm">连接失败: {connectionError}</span>
          </div>
        </div>
      )}

      {isConnected && task?.status === 'running' && (
        <div className="fixed bottom-4 right-4 bg-green-500 text-white px-4 py-2 rounded-lg shadow-lg">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-white rounded-full" />
            <span className="text-sm">实时更新已连接</span>
          </div>
        </div>
      )}
    </div>
  );
}
