"""日志工具"""

import logging
import sys
from typing import Optional
from src.config import settings


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """获取配置好的日志器"""
    logger = logging.getLogger(name or __name__)
    
    if not logger.handlers:
        # 设置日志级别
        logger.setLevel(getattr(logging, settings.log_level.upper()))
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, settings.log_level.upper()))
        
        # 创建格式器
        formatter = logging.Formatter(settings.log_format)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(console_handler)
        
        # 防止重复日志
        logger.propagate = False
    
    return logger
