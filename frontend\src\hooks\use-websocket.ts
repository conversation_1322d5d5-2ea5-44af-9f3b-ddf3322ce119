/**
 * WebSocket React hooks
 */

import { useEffect, useRef, useCallback, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import wsManager from '@/lib/websocket';
import { WebSocketMessage, NodeUpdate, ExtendedAnalysisTask, StepUpdate } from '@/types/analysis';
import { queryKeys } from './use-api';

/**
 * WebSocket连接hook
 */
export function useWebSocket(taskId: string | null) {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const queryClient = useQueryClient();
  const taskIdRef = useRef(taskId);

  // 更新taskId引用
  useEffect(() => {
    taskIdRef.current = taskId;
  }, [taskId]);

  // 连接管理
  useEffect(() => {
    if (!taskId) {
      wsManager.disconnect();
      return;
    }

    const connectToTask = async () => {
      try {
        setConnectionError(null);
        await wsManager.connect(taskId);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '连接失败';
        setConnectionError(errorMessage);
        console.error('[WebSocket Hook] 连接失败:', error);
      }
    };

    connectToTask();

    return () => {
      wsManager.disconnect();
    };
  }, [taskId]);

  // 连接状态监听
  useEffect(() => {
    const handleConnectionChange = (connected: boolean) => {
      setIsConnected(connected);
      if (connected) {
        setConnectionError(null);
      }
    };

    wsManager.onConnectionChange(handleConnectionChange);

    return () => {
      wsManager.offConnectionChange(handleConnectionChange);
    };
  }, []);

  return {
    isConnected,
    connectionError,
    disconnect: () => wsManager.disconnect(),
    send: (message: any) => wsManager.send(message),
  };
}

/**
 * 节点更新监听hook
 */
export function useNodeUpdates(
  taskId: string | null,
  onNodeUpdate?: (update: NodeUpdate) => void
) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!taskId) return;

    const handleNodeUpdate = (message: WebSocketMessage) => {
      if (message.event === 'node_update') {
        const nodeUpdate = message.payload as NodeUpdate;

        // 调用回调函数
        onNodeUpdate?.(nodeUpdate);

        // 更新查询缓存
        queryClient.invalidateQueries({
          queryKey: queryKeys.task(taskId)
        });

        // 显示通知
        toast.info(`节点更新: ${nodeUpdate.node_name}`, {
          description: `状态: ${nodeUpdate.status}`,
        });
      }
    };

    wsManager.on('node_update', handleNodeUpdate);

    return () => {
      wsManager.off('node_update', handleNodeUpdate);
    };
  }, [taskId, onNodeUpdate, queryClient]);
}

/**
 * 步骤更新监听hook
 */
export function useStepUpdates(
  taskId: string | null,
  onStepUpdate?: (update: StepUpdate) => void
) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!taskId) return;

    const handleStepUpdate = (message: WebSocketMessage) => {
      console.log('[DEBUG] 收到WebSocket消息:', message);

      if (message.event === 'step_update') {
        const stepUpdate = message.payload as StepUpdate;
        console.log('[DEBUG] 步骤更新消息:', stepUpdate);

        // 调用回调函数
        onStepUpdate?.(stepUpdate);

        // 更新查询缓存
        queryClient.invalidateQueries({
          queryKey: queryKeys.task(taskId)
        });

        // 不再显示toast通知，因为我们在结果面板中显示
        // 只在重要状态时显示简单通知
        if (stepUpdate.status === 'completed' || stepUpdate.status === 'failed') {
          const statusMessages = {
            'completed': '步骤完成',
            'failed': '步骤失败'
          };

          toast.info(`${stepUpdate.step_name}`, {
            description: statusMessages[stepUpdate.status] || stepUpdate.status,
          });
        }
      }
    };

    wsManager.on('step_update', handleStepUpdate);

    return () => {
      wsManager.off('step_update', handleStepUpdate);
    };
  }, [taskId, onStepUpdate, queryClient]);
}

/**
 * 任务完成监听hook
 */
export function useTaskCompletion(
  taskId: string | null,
  onTaskComplete?: (results: any) => void
) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!taskId) return;

    const handleTaskComplete = (message: WebSocketMessage) => {
      if (message.event === 'task_complete') {
        const results = message.payload;
        
        // 调用回调函数
        onTaskComplete?.(results);

        // 刷新所有相关数据
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.task(taskId) 
        });
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.taskResults(taskId) 
        });
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.taskInsights(taskId) 
        });

        // 显示成功通知
        toast.success('分析任务完成！', {
          description: '点击查看详细结果',
          action: {
            label: '查看',
            onClick: () => {
              // 可以在这里添加导航逻辑
              console.log('查看任务结果:', taskId);
            },
          },
        });
      }
    };

    wsManager.on('task_complete', handleTaskComplete);

    return () => {
      wsManager.off('task_complete', handleTaskComplete);
    };
  }, [taskId, onTaskComplete, queryClient]);
}

/**
 * 错误监听hook
 */
export function useTaskErrors(
  taskId: string | null,
  onError?: (error: string) => void
) {
  useEffect(() => {
    if (!taskId) return;

    const handleError = (message: WebSocketMessage) => {
      if (message.event === 'error') {
        const error = message.payload.error || '未知错误';
        
        // 调用回调函数
        onError?.(error);

        // 显示错误通知
        toast.error('任务执行出错', {
          description: error,
        });
      }
    };

    wsManager.on('error', handleError);

    return () => {
      wsManager.off('error', handleError);
    };
  }, [taskId, onError]);
}

/**
 * 进度更新监听hook
 */
export function useProgressUpdates(
  taskId: string | null,
  onProgress?: (progress: any) => void
) {
  useEffect(() => {
    if (!taskId) return;

    const handleProgress = (message: WebSocketMessage) => {
      if (message.event === 'progress') {
        const progress = message.payload;
        
        // 调用回调函数
        onProgress?.(progress);

        // 显示进度通知（可选）
        if (progress.message) {
          toast.info('任务进度更新', {
            description: progress.message,
          });
        }
      }
    };

    wsManager.on('progress', handleProgress);

    return () => {
      wsManager.off('progress', handleProgress);
    };
  }, [taskId, onProgress]);
}

/**
 * 结果卡片监听hook
 */
export function useResultCards(
  taskId: string | null,
  onResultCard?: (card: any) => void
) {
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!taskId) return;

    const handleResultCard = (message: WebSocketMessage) => {
      if (message.event === 'result_card') {
        const card = message.payload.card;
        console.log('[DEBUG] 收到结果卡片:', card);

        // 调用回调函数
        onResultCard?.(card);

        // 更新查询缓存
        queryClient.invalidateQueries({
          queryKey: queryKeys.task(taskId)
        });

        // 对于计划审批卡片，显示特殊通知
        if (card.type === 'plan_approval') {
          toast.info('分析计划已生成', {
            description: '请确认分析计划或进行修改',
            duration: 5000,
          });
        }
      }
    };

    wsManager.on('result_card', handleResultCard);

    return () => {
      wsManager.off('result_card', handleResultCard);
    };
  }, [taskId, onResultCard, queryClient]);
}

/**
 * 综合WebSocket监听hook
 */
export function useTaskWebSocket(
  taskId: string | null,
  callbacks?: {
    onNodeUpdate?: (update: NodeUpdate) => void;
    onTaskComplete?: (results: any) => void;
    onError?: (error: string) => void;
    onProgress?: (progress: any) => void;
    onResultCard?: (card: any) => void;
  }
) {
  const connection = useWebSocket(taskId);

  useNodeUpdates(taskId, callbacks?.onNodeUpdate);
  useTaskCompletion(taskId, callbacks?.onTaskComplete);
  useTaskErrors(taskId, callbacks?.onError);
  useProgressUpdates(taskId, callbacks?.onProgress);
  useResultCards(taskId, callbacks?.onResultCard);

  return connection;
}
