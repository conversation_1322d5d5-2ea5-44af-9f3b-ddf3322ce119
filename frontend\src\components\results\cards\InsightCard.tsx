import { Lightbulb, TrendingUp, AlertCircle, Target, Star } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { ResultCard, Insight } from '@/types/analysis';
import { cn } from '@/lib/utils';

interface InsightCardProps {
  card: ResultCard;
  isHighlighted?: boolean;
}

const insightIcons = {
  correlation: TrendingUp,
  trend: TrendingUp,
  anomaly: AlertCircle,
  recommendation: Target,
};

const insightColors = {
  correlation: 'text-blue-500',
  trend: 'text-green-500',
  anomaly: 'text-orange-500',
  recommendation: 'text-purple-500',
};

export function InsightCard({ card, isHighlighted }: InsightCardProps) {
  const data = card.content;

  // 检查是否是字符串格式的洞察内容（直接显示原始内容）
  if (typeof data === 'string') {
    return (
      <Card className={cn(
        "result-card animate-fade-in-up",
        isHighlighted && "ring-2 ring-primary"
      )}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-yellow-500" />
            {card.title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <pre className="whitespace-pre-wrap text-sm">{data}</pre>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 检查是否有原始内容字段
  if (data && typeof data === 'object' && 'content' in data && typeof data.content === 'string') {
    return (
      <Card className={cn(
        "result-card animate-fade-in-up",
        isHighlighted && "ring-2 ring-primary"
      )}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="w-5 h-5 text-yellow-500" />
            {card.title}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="prose prose-sm max-w-none dark:prose-invert">
            <pre className="whitespace-pre-wrap text-sm">{data.content}</pre>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 旧格式的结构化洞察数据
  const insights = (data as Insight[]) || [];

  return (
    <Card className={cn(
      "result-card animate-fade-in-up",
      isHighlighted && "ring-2 ring-primary"
    )}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="w-5 h-5 text-accent" />
          Key Insights
          <Badge variant="outline" className="ml-auto">
            {insights.length} insights
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {insights.map((insight) => {
          const IconComponent = insightIcons[insight.type];
          const iconColor = insightColors[insight.type];
          
          return (
            <div key={insight.id} className="p-4 rounded-lg border bg-muted/30 space-y-3">
              <div className="flex items-start gap-3">
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full bg-background border",
                  iconColor
                )}>
                  <IconComponent className="w-4 h-4" />
                </div>
                
                <div className="flex-1 space-y-2">
                  <div className="flex items-start justify-between">
                    <h4 className="font-medium text-sm">{insight.title}</h4>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        {insight.type}
                      </Badge>
                      <div className="flex items-center gap-1">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={cn(
                              "w-3 h-3",
                              i < Math.round(insight.confidence * 5) 
                                ? "text-yellow-500 fill-current" 
                                : "text-muted-foreground"
                            )}
                          />
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {insight.description}
                  </p>
                  
                  <div className="space-y-1">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-muted-foreground">Confidence</span>
                      <span className="font-medium">{Math.round(insight.confidence * 100)}%</span>
                    </div>
                    <Progress value={insight.confidence * 100} className="h-1.5" />
                  </div>
                  
                  {insight.source_node && (
                    <div className="text-xs text-muted-foreground">
                      Source: {insight.source_node}
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
        })}
      </CardContent>
    </Card>
  );
}