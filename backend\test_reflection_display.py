#!/usr/bin/env python3
"""测试反思审查的前端显示功能"""

import asyncio
import json
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.agents.reflection_agent import ReflectionAgent
from src.models.state_models import AnalysisState
from src.utils.logger import get_logger

logger = get_logger(__name__)


async def test_reflection_display():
    """测试反思审查的前端显示功能"""
    print("🖥️  测试反思审查前端显示功能...")
    
    # 创建测试状态
    test_state = AnalysisState(
        task_id=f"test_display_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        original_query="分析销售数据，识别影响销售业绩的关键因素并提供优化建议",
        dataframe_path="test_sales_data.csv",
        plan={
            "steps": [
                {"step": 1, "objective": "数据清洗", "description": "处理缺失值和异常值"},
                {"step": 2, "objective": "销售趋势分析", "description": "分析销售趋势和季节性模式"},
                {"step": 3, "objective": "影响因素分析", "description": "识别影响销售的关键因素"},
                {"step": 4, "objective": "预测建模", "description": "建立销售预测模型"}
            ]
        },
        current_step=4,
        executed_steps=[
            {
                "step": 1,
                "code": "df.isnull().sum()\ndf.fillna(method='forward', inplace=True)",
                "result": "数据清洗完成，处理了5%的缺失值",
                "insights": ["数据质量良好，缺失值主要集中在价格字段"]
            },
            {
                "step": 2,
                "code": "df.groupby('month')['sales'].sum().plot()\nplt.title('月度销售趋势')",
                "result": "生成销售趋势图",
                "insights": ["销售呈现明显的季节性特征，12月和1月为销售高峰"]
            },
            {
                "step": 3,
                "code": "correlation_matrix = df.corr()\nsns.heatmap(correlation_matrix)",
                "result": "生成相关性热力图",
                "insights": ["价格与销量呈负相关，广告投入与销量呈强正相关"]
            },
            {
                "step": 4,
                "code": "from sklearn.ensemble import RandomForestRegressor\nmodel.fit(X_train, y_train)",
                "result": "模型训练完成，R²=0.78",
                "insights": ["模型预测效果良好，广告投入是最重要的预测因子"]
            }
        ],
        chroma_collection_name="test_display",
        errors=[],
        insights=["销售数据分析完成，识别出关键影响因素"],
        data_summary={
            "shape": [5000, 10],
            "columns": ["date", "product_id", "product_name", "category", "price", "quantity", "sales", "ad_spend", "promotion", "region"],
            "dtypes": {
                "date": "datetime64[ns]",
                "product_id": "int64",
                "product_name": "object",
                "category": "object",
                "price": "float64",
                "quantity": "int64",
                "sales": "float64",
                "ad_spend": "float64",
                "promotion": "int64",
                "region": "object"
            }
        },
        final_report={
            "title_and_abstract": "销售数据分析报告\n\n本报告基于5000条销售记录，分析了影响销售业绩的关键因素。",
            "introduction": "销售业绩分析对于企业决策至关重要。本研究旨在识别关键影响因素。",
            "data_description": "数据集包含产品销售信息，涵盖价格、数量、广告投入等维度。",
            "exploratory_analysis": "探索性分析发现销售存在明显的季节性特征和地区差异。",
            "modeling_and_results": "使用随机森林模型进行预测，模型R²达到0.78。",
            "discussion": "分析结果表明广告投入是影响销售的最重要因素。",
            "conclusion": "建议增加广告投入，特别是在销售淡季进行促销活动。"
        }
    )
    
    # 创建反思智能体
    reflection_agent = ReflectionAgent()
    
    try:
        print(f"📊 任务ID: {test_state['task_id']}")
        print("🔄 开始执行反思审查（注意观察前端显示）...")
        
        # 执行反思审查
        result = await reflection_agent.execute(test_state)
        
        print("✅ 反思审查完成!")
        
        # 显示结果摘要
        evaluation = result.get("reflection_evaluation", {})
        reflection_insights = result.get("reflection_insights", {})
        improved_report_path = result.get("improved_report_path", "")
        
        print(f"\n📈 评估结果摘要:")
        print(f"  总体评分: {evaluation.get('overall_score', 'N/A')}/10")
        print(f"  关键问题: {len(evaluation.get('critical_issues', []))} 个")
        print(f"  维度评分: {len(evaluation.get('dimension_scores', {}))} 个维度")
        
        print(f"\n💡 反思洞察摘要:")
        print(f"  改进策略: {len(reflection_insights.get('improvement_strategies', {}))} 个")
        print(f"  重写优先级: {len(reflection_insights.get('rewrite_priorities', []))} 个")
        print(f"  具体行动: {len(reflection_insights.get('specific_actions', []))} 项")
        
        print(f"\n📄 改进报告: {improved_report_path}")
        
        # 检查前端显示相关的信息
        print(f"\n🖥️  前端显示验证:")
        print(f"  - 评估结果已生成，包含详细的维度评分和问题列表")
        print(f"  - 反思结果已生成，包含根因分析和改进策略")
        print(f"  - 流式生成内容应该在前端实时显示")
        print(f"  - 状态持久化已启用，切换卡片后内容不会丢失")
        
        # 提示用户检查前端
        print(f"\n👀 请检查前端页面:")
        print(f"  1. 反思审查卡片是否显示了完整的评估结果")
        print(f"  2. 是否显示了详细的反思分析结果")
        print(f"  3. 是否显示了流式生成的改进报告内容")
        print(f"  4. 切换到其他卡片再回来，内容是否仍然存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 反思审查失败: {str(e)}")
        logger.error(f"反思审查测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始反思审查前端显示测试\n")
    
    success = await test_reflection_display()
    
    print(f"\n📊 测试结果:")
    print(f"  反思审查执行: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        print("\n🎉 反思审查功能正常工作!")
        print("💡 请检查前端页面确认显示效果")
        return 0
    else:
        print("\n⚠️  测试失败，请检查日志。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
