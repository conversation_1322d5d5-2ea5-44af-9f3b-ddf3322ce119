/**
 * React Query hooks for API operations
 */

import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import apiService from '@/lib/api';
import { 
  AnalysisTask, 
  TaskListResponse, 
  TaskResultsResponse,
  StatsResponse 
} from '@/types/analysis';

// Query keys
export const queryKeys = {
  tasks: ['tasks'] as const,
  task: (id: string) => ['tasks', id] as const,
  taskResults: (id: string) => ['tasks', id, 'results'] as const,
  taskInsights: (id: string) => ['tasks', id, 'insights'] as const,
  stats: ['stats'] as const,
  health: ['health'] as const,
};

/**
 * 创建分析任务
 */
export function useCreateTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ prompt, file }: { prompt: string; file?: File }) => {
      return apiService.createTask(prompt, file);
    },
    onSuccess: (data) => {
      // 更新任务列表缓存
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });
      
      toast.success('分析任务创建成功', {
        description: `任务ID: ${data.task_id}`,
      });
    },
    onError: (error: Error) => {
      toast.error('创建任务失败', {
        description: error.message,
      });
    },
  });
}

/**
 * 获取任务详情
 */
export function useTask(taskId: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.task(taskId),
    queryFn: () => apiService.getTask(taskId),
    enabled: enabled && !!taskId,
    refetchInterval: (data) => {
      // 如果任务还在运行，每5秒刷新一次
      return data?.status === 'running' ? 5000 : false;
    },
    staleTime: 1000, // 1秒后数据过期
  });
}

/**
 * 获取任务列表
 */
export function useTasks(limit = 10, offset = 0) {
  return useQuery({
    queryKey: [...queryKeys.tasks, limit, offset],
    queryFn: () => apiService.getTasks(limit, offset),
    staleTime: 30000, // 30秒后数据过期
  });
}

/**
 * 取消任务
 */
export function useCancelTask() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (taskId: string) => apiService.cancelTask(taskId),
    onSuccess: (data) => {
      // 更新相关缓存
      queryClient.invalidateQueries({ queryKey: queryKeys.task(data.task_id) });
      queryClient.invalidateQueries({ queryKey: queryKeys.tasks });
      
      toast.success('任务已取消');
    },
    onError: (error: Error) => {
      toast.error('取消任务失败', {
        description: error.message,
      });
    },
  });
}

/**
 * 获取任务执行结果
 */
export function useTaskResults(taskId: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.taskResults(taskId),
    queryFn: () => apiService.getTaskResults(taskId),
    enabled: enabled && !!taskId,
    staleTime: 10000, // 10秒后数据过期
  });
}

/**
 * 获取任务洞察
 */
export function useTaskInsights(taskId: string, enabled = true) {
  return useQuery({
    queryKey: queryKeys.taskInsights(taskId),
    queryFn: () => apiService.getTaskInsights(taskId),
    enabled: enabled && !!taskId,
    staleTime: 30000, // 30秒后数据过期
  });
}

/**
 * 获取系统统计信息
 */
export function useStats() {
  return useQuery({
    queryKey: queryKeys.stats,
    queryFn: () => apiService.getStats(),
    refetchInterval: 30000, // 每30秒刷新
    staleTime: 20000, // 20秒后数据过期
  });
}

/**
 * 健康检查
 */
export function useHealthCheck() {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: () => apiService.healthCheck(),
    refetchInterval: 60000, // 每分钟检查一次
    staleTime: 30000, // 30秒后数据过期
    retry: 3,
  });
}

/**
 * 预加载任务数据
 */
export function usePrefetchTask() {
  const queryClient = useQueryClient();

  return (taskId: string) => {
    queryClient.prefetchQuery({
      queryKey: queryKeys.task(taskId),
      queryFn: () => apiService.getTask(taskId),
      staleTime: 10000,
    });
  };
}

/**
 * 手动刷新任务数据
 */
export function useRefreshTask() {
  const queryClient = useQueryClient();

  return (taskId: string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.task(taskId) });
    queryClient.invalidateQueries({ queryKey: queryKeys.taskResults(taskId) });
    queryClient.invalidateQueries({ queryKey: queryKeys.taskInsights(taskId) });
  };
}
