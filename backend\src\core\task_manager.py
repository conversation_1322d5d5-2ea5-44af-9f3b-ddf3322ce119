"""任务管理器"""

import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from fastapi import UploadFile
from src.models.api_models import TaskResponse, TaskStatus, NodeUpdate, NodeStatus
from src.models.state_models import AnalysisState
from src.core.workflow import analysis_workflow
from src.utils.id_generator import generate_task_id, generate_collection_name
from src.utils.file_utils import save_uploaded_file
from src.api.websocket import websocket_manager
from src.execution.jupyter_executor import jupyter_executor
from src.utils.logger import get_logger

logger = get_logger(__name__)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        """初始化任务管理器"""
        self.tasks: Dict[str, TaskResponse] = {}
        self.task_states: Dict[str, AnalysisState] = {}
        self.running_tasks: Dict[str, asyncio.Task] = {}
    
    async def create_task(self, prompt: str, file: Optional[UploadFile] = None) -> TaskResponse:
        """创建新任务"""
        task_id = generate_task_id()
        now = datetime.now()
        
        # 处理上传的文件
        file_path = None
        file_name = None
        if file:
            file_name = file.filename
            file_path = await save_uploaded_file(file, task_id)
        
        # 创建任务响应对象
        task = TaskResponse(
            task_id=task_id,
            status=TaskStatus.PENDING,
            created_at=now,
            updated_at=now,
            prompt=prompt,
            file_name=file_name
        )
        
        # 创建初始状态
        initial_state: AnalysisState = {
            "task_id": task_id,
            "original_query": prompt,
            "dataframe_path": file_path or "",
            "plan": None,
            "current_step": 0,
            "executed_steps": [],
            "chroma_collection_name": generate_collection_name(task_id),
            "errors": [],
            "insights": [],
            "messages": [],
            "data_summary": None,
            "jupyter_kernel_id": None,
            "execution_results": [],
            "waiting_for_user": False,
            "user_approved_plan": False,
            "user_modified_plan": None,
            "library_preferences": None,
            "created_at": now,
            "updated_at": now
        }
        
        # 存储任务和状态
        self.tasks[task_id] = task
        self.task_states[task_id] = initial_state
        
        logger.info(f"任务创建成功: {task_id}")
        return task
    
    async def execute_task(self, task_id: str) -> None:
        """执行任务"""
        if task_id not in self.tasks:
            logger.error(f"任务不存在: {task_id}")
            return
        
        task = self.tasks[task_id]
        state = self.task_states[task_id]
        
        try:
            # 更新任务状态为运行中
            task.status = TaskStatus.RUNNING
            task.updated_at = datetime.now()
            
            # 发送开始执行的WebSocket消息
            await websocket_manager.send_progress(task_id, {
                "status": "started",
                "message": "开始执行数据分析任务"
            })
            
            # 创建Jupyter内核
            await jupyter_executor.create_kernel(task_id)
            state["jupyter_kernel_id"] = task_id
            
            # 执行工作流
            final_state = await analysis_workflow.run(state)
            
            # 更新状态
            self.task_states[task_id] = final_state
            
            # 检查是否有错误
            if final_state.get("errors"):
                task.status = TaskStatus.FAILED
                task.error_message = final_state["errors"][-1]
                
                await websocket_manager.send_error(task_id, task.error_message)
            else:
                task.status = TaskStatus.COMPLETED
                
                # 发送完成消息
                results = await self._format_task_results(task_id, final_state)
                await websocket_manager.send_task_complete(task_id, results)
            
            task.updated_at = datetime.now()
            
        except Exception as e:
            logger.error(f"任务执行失败: {task_id}, 错误: {str(e)}")

            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.updated_at = datetime.now()

            await websocket_manager.send_error(task_id, str(e))

    async def approve_plan(self, task_id: str, approved: bool, modified_plan: Optional[Dict[str, Any]] = None,
                          library_preferences: Optional[Dict[str, Any]] = None) -> bool:
        """用户审批分析计划"""
        if task_id not in self.tasks or task_id not in self.task_states:
            logger.error(f"任务不存在: {task_id}")
            return False

        task = self.tasks[task_id]
        state = self.task_states[task_id]

        # 检查任务是否在等待用户审批
        if not state.get("waiting_for_user", False):
            logger.warning(f"任务不在等待用户审批状态: {task_id}")
            return False

        try:
            # 更新状态
            state["waiting_for_user"] = False
            state["user_approved_plan"] = approved

            if approved:
                if modified_plan:
                    state["user_modified_plan"] = modified_plan
                    state["plan"] = modified_plan  # 更新当前计划
                    logger.info(f"用户修改了计划: {task_id}, 新计划步骤数: {len(modified_plan.get('steps', []))}")

                    # 发送更新后的计划审批卡片
                    await self._send_updated_plan_approval_card(task_id, modified_plan)

                if library_preferences:
                    state["library_preferences"] = library_preferences
                    logger.info(f"用户设置了库偏好: {task_id}, 偏好: {library_preferences}")

                # 发送审批成功消息
                from src.models.api_models import WebSocketMessage
                approval_message = WebSocketMessage(
                    event="plan_approved",
                    payload={
                        "task_id": task_id,
                        "approved": True,
                        "modified": bool(modified_plan),
                        "message": "计划已确认，继续执行分析"
                    }
                )
                await websocket_manager.send_message(task_id, approval_message)

                # 更新等待确认节点状态为完成
                from src.models.api_models import NodeStatus, NodeUpdate
                from datetime import datetime

                node_update = NodeUpdate(
                    node_id="wait_for_approval",
                    node_name="用户确认",
                    status=NodeStatus.SUCCESS,
                    timestamp=datetime.now(),
                    execution_time=0.0,
                    content={
                        "approved": True,
                        "modified": bool(modified_plan),
                        "message": "用户已确认计划，开始执行分析"
                    }
                )
                await websocket_manager.send_node_update(task_id, node_update)

                # 更新状态，标记不再等待用户
                state["waiting_for_user"] = False

                # 启动后台任务继续执行工作流，不阻塞API响应
                asyncio.create_task(self._continue_workflow_execution(task_id, state))

            else:
                # 用户拒绝计划，取消任务
                task.status = TaskStatus.CANCELLED
                from src.models.api_models import WebSocketMessage
                rejection_message = WebSocketMessage(
                    event="plan_rejected",
                    payload={
                        "task_id": task_id,
                        "message": "用户拒绝了分析计划，任务已取消"
                    }
                )
                await websocket_manager.send_message(task_id, rejection_message)

            task.updated_at = datetime.now()
            logger.info(f"用户审批完成: {task_id}, 审批结果: {approved}")
            return True

        except Exception as e:
            logger.error(f"处理用户审批失败: {task_id}, 错误: {str(e)}")
            return False
        
        finally:
            # 清理资源
            await jupyter_executor.shutdown_kernel(task_id)
            
            # 从运行任务列表中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]

    async def _continue_workflow_execution(self, task_id: str, state: AnalysisState):
        """后台继续执行工作流"""
        try:
            task = self.tasks[task_id]

            # 使用全局工作流实例从execute节点开始继续执行
            from src.core.workflow import analysis_workflow
            final_state = await analysis_workflow.run_from_node(state, "execute")
            self.task_states[task_id] = final_state

            # 检查执行结果
            if final_state.get("errors"):
                task.status = TaskStatus.FAILED
                task.error_message = final_state["errors"][-1]
                await websocket_manager.send_error(task_id, task.error_message)
            else:
                task.status = TaskStatus.COMPLETED
                results = await self._format_task_results(task_id, final_state)
                await websocket_manager.send_task_complete(task_id, results)

            task.updated_at = datetime.now()
            logger.info(f"后台工作流执行完成: {task_id}")

        except Exception as e:
            logger.error(f"后台工作流执行失败: {task_id}, 错误: {str(e)}")
            task = self.tasks[task_id]
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.updated_at = datetime.now()
            await websocket_manager.send_error(task_id, str(e))

        finally:
            # 清理资源
            await jupyter_executor.shutdown_kernel(task_id)

            # 从运行任务列表中移除
            if task_id in self.running_tasks:
                del self.running_tasks[task_id]
    
    async def get_task(self, task_id: str) -> Optional[TaskResponse]:
        """获取任务信息"""
        return self.tasks.get(task_id)

    async def _send_updated_plan_approval_card(self, task_id: str, updated_plan: Dict[str, Any]):
        """发送更新后的计划审批卡片"""
        try:
            from datetime import datetime

            # 使用固定的ID格式，确保前端更新现有卡片而不是创建新卡片
            # 这个ID格式应该与workflow.py中_send_plan_approval_card使用的格式一致
            card_data = {
                "id": f"plan_approval_{task_id}",  # 使用固定ID，不包含时间戳
                "node_id": "wait_for_approval",
                "type": "plan_approval",
                "title": "分析计划确认",
                "content": {
                    "plan": updated_plan,
                    "waiting_for_user": False,  # 已经确认，不再等待
                    "message": "用户已修改并确认分析计划，开始执行分析。"
                },
                "timestamp": datetime.now().isoformat()
            }

            await websocket_manager.send_result_card(task_id, card_data)
            logger.info(f"[任务管理器] 发送更新后的计划审批卡片 - 任务ID: {task_id}")

        except Exception as e:
            logger.error(f"发送更新后的计划审批卡片失败: {str(e)}")
            # 不抛出异常，避免中断审批流程
    
    async def list_tasks(self, limit: int = 10, offset: int = 0) -> List[TaskResponse]:
        """获取任务列表"""
        tasks = list(self.tasks.values())
        # 按创建时间倒序排列
        tasks.sort(key=lambda x: x.created_at, reverse=True)
        return tasks[offset:offset + limit]
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        if task_id not in self.tasks:
            return False
        
        task = self.tasks[task_id]
        
        # 只能取消待执行或运行中的任务
        if task.status not in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            return False
        
        # 取消运行中的任务
        if task_id in self.running_tasks:
            self.running_tasks[task_id].cancel()
            del self.running_tasks[task_id]
        
        # 更新任务状态
        task.status = TaskStatus.CANCELLED
        task.updated_at = datetime.now()
        
        # 清理资源
        await jupyter_executor.shutdown_kernel(task_id)
        
        # 发送取消消息
        await websocket_manager.send_progress(task_id, {
            "status": "cancelled",
            "message": "任务已被取消"
        })
        
        logger.info(f"任务已取消: {task_id}")
        return True
    
    async def get_task_results(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务执行结果"""
        if task_id not in self.task_states:
            return None
        
        state = self.task_states[task_id]
        return await self._format_task_results(task_id, state)
    
    async def get_task_insights(self, task_id: str) -> List[str]:
        """获取任务分析洞察"""
        if task_id not in self.task_states:
            return []
        
        state = self.task_states[task_id]
        return state.get("insights", [])
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        total_tasks = len(self.tasks)
        status_counts = {}
        
        for task in self.tasks.values():
            status = task.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        return {
            "total_tasks": total_tasks,
            "status_counts": status_counts,
            "running_tasks": len(self.running_tasks),
            "active_connections": websocket_manager.get_total_connections()
        }
    
    async def _format_task_results(self, task_id: str, state: AnalysisState) -> Dict[str, Any]:
        """格式化任务结果"""
        return {
            "task_id": task_id,
            "status": self.tasks[task_id].status.value,
            "plan": state.get("plan", {}),
            "executed_steps": state.get("executed_steps", []),
            "insights": state.get("insights", []),
            "data_summary": state.get("data_summary", {}),
            "errors": state.get("errors", []),
            "created_at": state["created_at"].isoformat(),
            "updated_at": state["updated_at"].isoformat()
        }


# 创建全局任务管理器实例
task_manager = TaskManager()
