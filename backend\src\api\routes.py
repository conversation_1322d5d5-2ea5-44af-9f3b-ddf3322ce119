"""API路由定义"""

import asyncio
import os
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, UploadFile, File, HTTPException, BackgroundTasks, Form, Request
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel
from src.models.api_models import TaskCreateRequest, TaskResponse, TaskStatus, PlanApprovalRequest, UserInteractionResponse
from src.core.task_manager import task_manager
from src.utils.logger import get_logger
from src.api.sse import create_sse_response
from src.config.settings import settings

logger = get_logger(__name__)

# 创建路由器
router = APIRouter()


# 报告编辑相关的请求模型
class ReportEditRequest(BaseModel):
    content: str
    original_report_path: Optional[str] = None


@router.post("/tasks", response_model=TaskResponse)
async def create_task(
    background_tasks: BackgroundTasks,
    prompt: str = Form(...),
    file: Optional[UploadFile] = File(None)
) -> TaskResponse:
    """创建新的分析任务"""
    try:
        logger.info(f"收到新任务请求: {prompt[:100]}...")
        
        # 创建任务
        task = await task_manager.create_task(prompt, file)
        
        # 在后台执行任务
        background_tasks.add_task(task_manager.execute_task, task.task_id)
        
        logger.info(f"任务创建成功: {task.task_id}")
        return task
        
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建任务失败: {str(e)}")


@router.get("/tasks/{task_id}", response_model=TaskResponse)
async def get_task(task_id: str) -> TaskResponse:
    """获取任务信息"""
    try:
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return task
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务失败: {str(e)}")


@router.get("/tasks")
async def list_tasks(limit: int = 10, offset: int = 0) -> Dict[str, Any]:
    """获取任务列表"""
    try:
        tasks = await task_manager.list_tasks(limit, offset)
        return {
            "tasks": tasks,
            "total": len(tasks),
            "limit": limit,
            "offset": offset
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务列表失败: {str(e)}")


@router.delete("/tasks/{task_id}")
async def cancel_task(task_id: str) -> Dict[str, str]:
    """取消任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {"message": "任务已取消", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"取消任务失败: {str(e)}")


@router.get("/tasks/{task_id}/results")
async def get_task_results(task_id: str) -> Dict[str, Any]:
    """获取任务执行结果"""
    try:
        results = await task_manager.get_task_results(task_id)
        if not results:
            raise HTTPException(status_code=404, detail="任务结果不存在")
        
        return results
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务结果失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务结果失败: {str(e)}")


@router.get("/tasks/{task_id}/insights")
async def get_task_insights(task_id: str) -> Dict[str, Any]:
    """获取任务分析洞察"""
    try:
        insights = await task_manager.get_task_insights(task_id)
        return {"task_id": task_id, "insights": insights}
        
    except Exception as e:
        logger.error(f"获取任务洞察失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取任务洞察失败: {str(e)}")


@router.get("/health")
async def health_check() -> Dict[str, str]:
    """健康检查"""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "0.1.0"
    }


@router.get("/test-cors")
async def test_cors() -> Dict[str, str]:
    """测试CORS"""
    return {
        "message": "CORS test successful",
        "timestamp": datetime.now().isoformat()
    }


@router.post("/test-task")
async def test_task_creation(prompt: str = Form(...)) -> Dict[str, str]:
    """测试任务创建"""
    return {
        "message": "Task creation test successful",
        "prompt": prompt,
        "timestamp": datetime.now().isoformat()
    }


@router.post("/tasks/{task_id}/approve-plan", response_model=UserInteractionResponse)
async def approve_plan(task_id: str, request: PlanApprovalRequest) -> UserInteractionResponse:
    """用户审批分析计划"""
    try:
        success = await task_manager.approve_plan(
            task_id=task_id,
            approved=request.approved,
            modified_plan=request.modified_plan,
            library_preferences=request.library_preferences
        )

        if not success:
            raise HTTPException(status_code=400, detail="审批失败，任务可能不存在或不在等待审批状态")

        message = "计划已确认，继续执行分析" if request.approved else "计划已拒绝，任务已取消"
        return UserInteractionResponse(success=True, message=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"审批计划失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"审批计划失败: {str(e)}")


@router.get("/stats")
async def get_stats() -> Dict[str, Any]:
    """获取系统统计信息"""
    try:
        stats = await task_manager.get_stats()
        return stats

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.get("/tasks/{task_id}/report-stream")
async def get_report_stream(task_id: str, request: Request) -> StreamingResponse:
    """获取报告生成的SSE流"""
    try:
        # 检查任务是否存在
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 创建SSE响应
        stream_id = f"report_{task_id}"
        return await create_sse_response(task_id, stream_id, request)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建报告流失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建报告流失败: {str(e)}")


@router.get("/tasks/{task_id}/stream")
async def get_task_stream(task_id: str, request: Request) -> StreamingResponse:
    """获取任务执行的SSE流"""
    try:
        # 检查任务是否存在
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 创建SSE响应
        stream_id = f"task_{task_id}"
        return await create_sse_response(task_id, stream_id, request)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建任务流失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建任务流失败: {str(e)}")


@router.get("/tasks/{task_id}/report-status")
async def get_report_status(task_id: str):
    """获取报告生成状态"""
    try:
        from src.api.sse import report_stream_manager

        # 从报告流管理器获取状态
        status = report_stream_manager.get_report_status(task_id)

        if not status:
            # 如果没有状态，返回默认状态
            return {
                "overall_status": "idle",
                "progress": 0,
                "sections": {},
                "current_section": None,
                "report_path": None
            }

        return status

    except Exception as e:
        logger.error(f"获取报告状态失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报告状态失败: {str(e)}")


@router.post("/tasks/{task_id}/report/save-edited")
async def save_edited_report(task_id: str, request: ReportEditRequest) -> Dict[str, Any]:
    """保存编辑后的报告"""
    try:
        # 检查任务是否存在
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 创建报告目录
        report_dir = os.path.join(settings.results_directory, task_id)
        os.makedirs(report_dir, exist_ok=True)

        # 生成新的报告文件名（带版本号）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"analysis_report_edited_{timestamp}.md"
        new_report_path = os.path.join(report_dir, report_filename)

        # 保存编辑后的内容
        with open(new_report_path, 'w', encoding='utf-8') as f:
            f.write(request.content)

        logger.info(f"编辑后的报告已保存: {new_report_path}")

        # 返回新报告的路径信息
        return {
            "success": True,
            "new_report_path": report_filename,
            "full_path": new_report_path,
            "task_id": task_id,
            "saved_at": datetime.now().isoformat(),
            "message": "报告编辑版本保存成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"保存编辑报告失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"保存编辑报告失败: {str(e)}")


@router.get("/tasks/{task_id}/report/versions")
async def get_report_versions(task_id: str) -> Dict[str, Any]:
    """获取报告的所有版本"""
    try:
        # 检查任务是否存在
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")

        # 获取报告目录
        report_dir = os.path.join(settings.results_directory, task_id)

        if not os.path.exists(report_dir):
            return {
                "task_id": task_id,
                "versions": [],
                "total": 0
            }

        # 查找所有报告文件
        versions = []
        for filename in os.listdir(report_dir):
            if filename.startswith("analysis_report") and filename.endswith(".md"):
                file_path = os.path.join(report_dir, filename)
                file_stat = os.stat(file_path)

                # 判断是否为编辑版本
                is_edited = "edited" in filename

                versions.append({
                    "filename": filename,
                    "is_edited": is_edited,
                    "size": file_stat.st_size,
                    "created_at": datetime.fromtimestamp(file_stat.st_ctime).isoformat(),
                    "modified_at": datetime.fromtimestamp(file_stat.st_mtime).isoformat()
                })

        # 按创建时间排序（最新的在前）
        versions.sort(key=lambda x: x["created_at"], reverse=True)

        return {
            "task_id": task_id,
            "versions": versions,
            "total": len(versions)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取报告版本失败: {task_id}, 错误: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取报告版本失败: {str(e)}")


@router.get("/tasks/{task_id}/files")
async def get_task_files(task_id: str):
    """获取任务生成的文件列表"""
    try:
        import os
        from pathlib import Path

        # 任务文件夹路径
        task_folder = Path(settings.results_directory) / task_id
        logger.info(f"查找文件夹: {task_folder.absolute()}")

        if not task_folder.exists():
            logger.warning(f"任务文件夹不存在: {task_folder.absolute()}")
            return {"files": []}

        files = []
        for file_path in task_folder.rglob("*"):
            if file_path.is_file():
                # 获取文件信息
                stat = file_path.stat()

                # 确定文件类型
                suffix = file_path.suffix.lower()
                if suffix in ['.png', '.jpg', '.jpeg', '.gif', '.svg']:
                    file_type = 'image'
                elif suffix in ['.md', '.markdown']:
                    file_type = 'markdown'
                elif suffix in ['.html', '.htm']:
                    file_type = 'html'
                elif suffix in ['.txt', '.log']:
                    file_type = 'text'
                else:
                    file_type = 'other'

                # 获取文件创建时间（跨平台兼容）
                import platform
                if platform.system() == 'Windows':
                    # Windows系统中st_ctime是创建时间
                    created_time = stat.st_ctime
                else:
                    # Unix系统中尝试使用st_birthtime，否则使用st_ctime
                    created_time = getattr(stat, 'st_birthtime', stat.st_ctime)

                files.append({
                    "name": file_path.name,
                    "path": str(file_path.relative_to(task_folder)),
                    "type": file_type,
                    "size": stat.st_size,
                    "created": created_time,  # 创建时间
                    "modified": stat.st_mtime  # 修改时间
                })

        # 按修改时间排序，最新的在前
        files.sort(key=lambda x: x["modified"], reverse=True)

        return {"files": files}

    except Exception as e:
        logger.error(f"获取文件列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/files/{file_name}/content")
async def get_file_content(task_id: str, file_name: str):
    """获取文件内容（用于预览）"""
    try:
        from pathlib import Path
        from urllib.parse import unquote

        # 解码文件名
        file_name = unquote(file_name)

        # 任务文件夹路径
        task_folder = Path(settings.results_directory) / task_id
        file_path = task_folder / file_name

        # 安全检查：确保文件在任务文件夹内
        if not file_path.resolve().is_relative_to(task_folder.resolve()):
            raise HTTPException(status_code=403, detail="访问被拒绝")

        if not file_path.exists():
            raise HTTPException(status_code=404, detail="文件不存在")

        # 读取文件内容
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content
        except UnicodeDecodeError:
            # 如果不是文本文件，返回错误
            raise HTTPException(status_code=400, detail="文件不是文本格式")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件内容失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/files/{file_name}")
async def download_file(task_id: str, file_name: str):
    """下载/预览文件"""
    try:
        from pathlib import Path
        from urllib.parse import unquote
        from fastapi.responses import FileResponse
        import mimetypes

        # 解码文件名
        file_name = unquote(file_name)

        # 任务文件夹路径
        task_folder = Path(settings.results_directory) / task_id
        file_path = task_folder / file_name

        # 安全检查：确保文件在任务文件夹内
        if not file_path.resolve().is_relative_to(task_folder.resolve()):
            raise HTTPException(status_code=403, detail="访问被拒绝")

        if not file_path.exists():
            logger.error(f"文件不存在: {file_path}")
            raise HTTPException(status_code=404, detail="文件不存在")

        # 根据文件扩展名确定媒体类型
        media_type, _ = mimetypes.guess_type(str(file_path))
        if media_type is None:
            media_type = 'application/octet-stream'

        logger.info(f"提供文件: {file_path}, 媒体类型: {media_type}")

        return FileResponse(
            path=str(file_path),
            filename=file_name,
            media_type=media_type
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取文件失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
